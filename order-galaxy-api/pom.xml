<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.reagent.order</groupId>
        <artifactId>order-galaxy-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>order-galaxy-api</artifactId>

    <name>order-galaxy-api</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-api-info-bom</artifactId>
            <version>${msharp.version}</version>
            <type>pom</type>
        </dependency>
    </dependencies>
</project>