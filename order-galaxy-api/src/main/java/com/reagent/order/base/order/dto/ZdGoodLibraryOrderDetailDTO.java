package com.reagent.order.base.order.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 中单商品库订单详情
 * @date 2023/11/10 21
 */
public class ZdGoodLibraryOrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String goodName;

    /**
     * 匹配度
     */
    private Integer matchDegree;

    /**
     * 单位id
     */
    private Integer orgId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGoodName() {
        return goodName;
    }

    public void setGoodName(String goodName) {
        this.goodName = goodName;
    }

    public Integer getMatchDegree() {
        return matchDegree;
    }

    public void setMatchDegree(Integer matchDegree) {
        this.matchDegree = matchDegree;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }
}
