package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.ExportTemplateDTO;
import com.ruijing.base.swagger.api.rpc.annotation.RpcApi;
import com.ruijing.base.swagger.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/9/1 14:56
 */
@RpcApi
public interface ExportTemplateRpcService {

    @RpcMethod(value = "通过usertype和userid获取导出模板")
    RemoteResponse<List<ExportTemplateDTO>> getExportTemplateByUserInfo(ExportTemplateDTO exportTemplateDTO);

    @RpcMethod(value = "通过usertype和userid获取导出模板计数")
    RemoteResponse<Integer> countExportTemplateByUserInfo(ExportTemplateDTO exportTemplateDTO);

    @RpcMethod(value = "保存模板信息")
    RemoteResponse<Boolean> saveExportTemplate(ExportTemplateDTO exportTemplateDTO);

    @RpcMethod(value = "根据template主键删除模板")
    RemoteResponse<Boolean> deleteExportTemplate(ExportTemplateDTO exportTemplateDTO);

    @RpcMethod(value = "根据template主键更新模板信息")
    RemoteResponse<Boolean> updateExportTemplate(ExportTemplateDTO exportTemplateDTO);

    @RpcMethod(value = "根据共享状态和单位，计算模板数量")
    RemoteResponse<Integer> countShareTemplateInOrg(ExportTemplateDTO exportTemplateDTO);

    @RpcMethod(value = "根据共享状态和单位，获取此共享状态的模板列表")
    RemoteResponse<List<ExportTemplateDTO>> getExportTemplateByShareStatusInOrg(ExportTemplateDTO exportTemplateDTO);
}
