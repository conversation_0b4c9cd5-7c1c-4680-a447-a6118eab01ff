package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.reagent.order.base.order.dto.OrderExportDTO;
import com.reagent.order.base.order.dto.OrderExportQueryDTO;
import com.reagent.order.base.order.dto.OrderExportResultDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 10:52
 * @Version 1.0
 * @Desc:描述
 */
public interface OrderExportRpcService {

    RemoteResponse<BasePageResultDTO<OrderExportDTO>> findOrderExportList(OrderExportQueryDTO orderExportQueryDTO);

    RemoteResponse<Boolean> deleteOrderExportInfoById(Integer id);

    RemoteResponse<OrderExportResultDTO> saveOrderExport(OrderExportDTO orderExportDTO);

    RemoteResponse<Boolean> updateById(OrderExportDTO orderExportDTO);

    RemoteResponse<OrderExportDTO> findById(Integer id);
}
