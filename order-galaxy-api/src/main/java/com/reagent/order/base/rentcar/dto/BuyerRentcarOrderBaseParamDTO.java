package com.reagent.order.base.rentcar.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 采购人 网约车列表查询 入参
 * @author: zhuk
 * @create: 2019-07-30 18:45
 **/
public class BuyerRentcarOrderBaseParamDTO implements Serializable {

    private static final long serialVersionUID = -4571552028179569856L;
    /**
     * 采购人id
     */
    private Long buyerId;

    /**
     * 有pi权限的课题组
     */
    private List<Long> piDepartmentIds;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 查询内容
     */
    private String searchContext;

    /**
     * 起始时间
     */
    private Date starTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 当前页码
     */
    private Integer pageNumber;

    /**
     * 页面大小
     */
    private Integer pageSize;

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public List<Long> getPiDepartmentIds() {
        return piDepartmentIds;
    }

    public void setPiDepartmentIds(List<Long> piDepartmentIds) {
        this.piDepartmentIds = piDepartmentIds;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getSearchContext() {
        return searchContext;
    }

    public void setSearchContext(String searchContext) {
        this.searchContext = searchContext;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getStarTime() {
        return starTime;
    }

    public void setStarTime(Date starTime) {
        this.starTime = starTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "BuyerRentcarOrderBaseParamDTO{" +
                "buyerId=" + buyerId +
                ", piDepartmentIds=" + piDepartmentIds +
                ", businessType=" + businessType +
                ", searchContext='" + searchContext + '\'' +
                ", starTime=" + starTime +
                ", endTime=" + endTime +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                '}';
    }
}
