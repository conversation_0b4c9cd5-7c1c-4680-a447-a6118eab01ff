package com.reagent.order.base.log.enums;

public enum OrderDockingOperationEnum {

    /**
     * 调用切换经费卡接口  接口
     */
    CHANG_FUND_CARD(1,"更改经费卡"),

    /**
     * 经费卡接口回调 操作
     */
    CHANG_FUND_CARD_CALLBACK(2,"更改经费卡回调"),

    /**
     * 解冻经费卡
     */
    UNFREEZE_FUND_CARD(3,"解冻经费卡"),

    /**
     * 解冻经费卡回调
     */
    UNFREEZE_FUND_CARD_CALLBACK(4,"解冻经费卡回调");

    public final int type;

    public final String operation;

    OrderDockingOperationEnum(int type, String operation) {
        this.type = type;
        this.operation = operation;
    }
}
