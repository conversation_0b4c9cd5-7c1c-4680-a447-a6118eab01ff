package com.reagent.order.base.order.enums;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 16:39
 * @Version 1.0
 * @Desc:描述
 */
public enum OrderExportStatusEnum {

    EXPORTING(1,"导出中"),

    EXPORT_SUCCESS(2, "导出成功"),

    EXPORT_FAIL(3, "导出失败");

    public final Integer value;

    public final String description;

    OrderExportStatusEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static  OrderExportStatusEnum getByDescription(String description) {
        for (OrderExportStatusEnum orderExportStatusEnum : OrderExportStatusEnum.values()) {
            if (orderExportStatusEnum.description.equals(description)){
                return orderExportStatusEnum;
            }
        }
        return null;
    }

    public static  OrderExportStatusEnum getByValue(Integer value) {
        for (OrderExportStatusEnum orderExportStatusEnum : OrderExportStatusEnum.values()) {
            if (orderExportStatusEnum.value.intValue() == value.intValue()){
                return orderExportStatusEnum;
            }
        }
        return null;
    }
}
