package com.reagent.order.base.order.dto.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-12-20 14:20
 */
public class OrderAddressRequestDTO extends BasePageRequestDTO {

    @RpcModelProperty("代配送状态")
    private Integer deliveryStatus;

    @RpcModelProperty("操作人guid")
    private String operatorGuid;

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }
}
