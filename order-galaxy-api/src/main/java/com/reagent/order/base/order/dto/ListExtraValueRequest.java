package com.reagent.order.base.order.dto;

import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 查询List类型extraValue请求DTO
 * 仅支持List类型的extraKey，如ACCEPT_APPROVE_USERS(12)、ORDER_TAG(42)等
 */
public class ListExtraValueRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单ID列表")
    private List<Integer> orderIds;

    @ModelProperty(value = "List类型扩展键，仅支持List类型的extraKey", enumLink = "com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum")
    private List<Integer> extraKeys;

    public List<Integer> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Integer> orderIds) {
        this.orderIds = orderIds;
    }

    public List<Integer> getExtraKeys() {
        return extraKeys;
    }

    public void setExtraKeys(List<Integer> extraKeys) {
        this.extraKeys = extraKeys;
    }

    @Override
    public String toString() {
        return "ListExtraValueRequest{" +
                "orderIds=" + orderIds +
                ", extraKeys=" + extraKeys +
                '}';
    }
}
