package com.reagent.order.base.order.enums;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 16:52
 * @Version 1.0
 * @Desc:描述
 */
public enum ExportFileTypeEnum {

    HMS_ORDER_DETAIL(1,"HMS订单明细表"),

    HMS_PRODUCT_DETAIL(2, "HMS商品明细表"),

    BUYER_ORDER_DETAIL(3, "采购人中心订单明细表-我的订单"),

    BUYER_PRODUCT_DETAIL(4, "采购人中心商品明细表-我的订单"),

    BUYER_WARE_HOUSE(5, "采购人中心出入库"),

    BUYER_ORDER_STATISTICS(6, "采购人中心订单统计"),

    BUYER_GROUP_ORDER_DETAIL(7, "采购人中心订单明细表-课题组订单"),

    BUYER_GOODS_RETURN_DETAIL(10, "采购人中心退货明细表"),

    OMS_ORDER_STAT(11, "oms订单统计"),

    BUYER_GROUP_PRODUCT_DETAIL(8, "采购人中心商品明细表-课题组订单"),

    OMS_DELIVERY_PROXY_ORDER_LIST(12, "oms代配送订单"),

    BUYER_GROUP_PRODUCT_DETIAL(8, "采购人中心商品明细表-课题组订单"),

    DIY_OMS_ORDER(13, "自定义导出——OMS"),

    DIY_HMS_ORDER(14, "自定义导出——HMS"),

    DIY_BUYER_ORDER(15, "自定义导出——www我的订单"),

    DIY_BUYER_GROUP_ORDER(16, "自定义导出——www课题组订单"),

    ADVERTISEMENT_ORDER_EXPORT(17, "广告投放交易详情导出"),

    RISK_ORDER(18, "疑似异常订单"),

    BUYER_ORDER_LOG(19, "采购人中心订单日志-我的订单"),

    BUYER_ORDER_LOG_GROUP(20, "采购人中心订单日志-课题组订单"),

    ;

    public final Integer value;

    public final String description;

    ExportFileTypeEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static  ExportFileTypeEnum getByDescription(String description) {
        for (ExportFileTypeEnum exportFileTypeEnum : ExportFileTypeEnum.values()) {
            if (exportFileTypeEnum.description.equals(description)){
                return exportFileTypeEnum;
            }
        }
        return null;
    }

    public static  ExportFileTypeEnum getByValue(Integer value) {
        for (ExportFileTypeEnum exportFileTypeEnum : ExportFileTypeEnum.values()) {
            if (exportFileTypeEnum.value.intValue() == value.intValue()){
                return exportFileTypeEnum;
            }
        }
        return null;
    }
}
