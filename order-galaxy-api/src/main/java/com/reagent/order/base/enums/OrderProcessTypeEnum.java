package com.reagent.order.base.enums;

/**
 * @description: 订单流程类型枚举
 * @author: zhuk
 * @create: 2019-08-26 17:29
 **/
public enum OrderProcessTypeEnum {

    /**
     * 线上
     */
    ON_LINE(0,"线上"),

    /**
     * 线下
     */
    OFF_LINE(1,"线下");

    /**
     * 值
     */
    public Integer value;

    /**
     * 描述
     */
    public String desc;


    OrderProcessTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
