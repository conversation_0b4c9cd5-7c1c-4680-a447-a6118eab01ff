package com.reagent.order.base.enums;

/**
 *
 * 订单 业务类型枚举
 * <AUTHOR>
 * @create: 2019-07-29
 */
public enum OrderBusinessTypeEnum {

    /**
     * 网约车业务类型
     */
    RENTCAR(1,"网约车","WY");

    /**
     * typeValue
     */
    public Integer typeValue;


    /**
     * typeName
     */
    public String typeName;

    /**
     * 类型编号
     */
    public String typeCode;

    OrderBusinessTypeEnum(Integer typeValue, String typeName, String typeCode) {
        this.typeValue = typeValue;
        this.typeName = typeName;
        this.typeCode = typeCode;
    }

    public static OrderBusinessTypeEnum getOrderBusinessType(Integer typeValue){
        OrderBusinessTypeEnum[] values = OrderBusinessTypeEnum.values();
        OrderBusinessTypeEnum result = null;
        for (OrderBusinessTypeEnum type : values){
            if (type.typeValue.equals(typeValue) ){
                result = type;
            }
        }
        return result;
    }
}
