package com.reagent.order.base.order.dto;

import com.ruijing.base.swagger.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/26 15:55
 * @Description
 **/
public class OrderAcceptQueryDTO implements Serializable {

    private static final long serialVersionUID = 4663060421586562681L;

    /**
     * 单位id
     */
    @RpcModelProperty("单位id")
    private Integer orgId;

    /**
     * 订单id列表
     */
    @RpcModelProperty("订单id列表")
    private List<Integer> orderIdList;

    public Integer getOrgId() {
        return orgId;
    }

    public OrderAcceptQueryDTO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public OrderAcceptQueryDTO setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
        return this;
    }
}
