package com.reagent.order.base.order.enums.product;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-04-22 11:27
 * @description: 订单商品交易状态
 */
public enum OrderProductTransactionStatusEnum {

    WAITING_FOR_DELIVERY(1, "待发货"),

    WAITING_FOR_RECEIVE(2, "待收货"),

    RECEIVED(3, "已收货"),

    RETURNING(4, "退货中"),

    RETURNED(5, "已退货"),

    CANCELED(6, "已取消"),
    ;

    private final int code;

    private final String desc;

    OrderProductTransactionStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderProductTransactionStatusEnum getByCode(Integer code) {
        if(code == null){
            return null;
        }
        for(OrderProductTransactionStatusEnum e : OrderProductTransactionStatusEnum.values()){
            if(e.code == code){
                return e;
            }
        }
        return null;
    }
}
