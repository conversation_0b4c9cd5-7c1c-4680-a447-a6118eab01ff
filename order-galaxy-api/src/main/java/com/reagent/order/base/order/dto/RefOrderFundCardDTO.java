package com.reagent.order.base.order.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 订单关联经费卡信息表
 * @date 2024/2/5 下午 02:53
 */
public class RefOrderFundCardDTO implements Serializable {

    @RpcModelProperty("")
    private Integer id;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("经费卡id")
    private String fundCardId;

    @RpcModelProperty("经费卡号")
    private String fundCardNo;

    @RpcModelProperty("冻结金额")
    private BigDecimal freezeAmount;

    @RpcModelProperty("使用金额")
    private BigDecimal useAmount;

    @RpcModelProperty("经费支出申请单号")
    private String expenseApplyNo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getFundCardId() {
        return fundCardId;
    }

    public void setFundCardId(String fundCardId) {
        this.fundCardId = fundCardId;
    }

    public String getFundCardNo() {
        return fundCardNo;
    }

    public void setFundCardNo(String fundCardNo) {
        this.fundCardNo = fundCardNo;
    }

    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    public BigDecimal getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    public String getExpenseApplyNo() {
        return expenseApplyNo;
    }

    public void setExpenseApplyNo(String expenseApplyNo) {
        this.expenseApplyNo = expenseApplyNo;
    }
}
