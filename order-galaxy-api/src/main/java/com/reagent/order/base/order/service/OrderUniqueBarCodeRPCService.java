package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.dto.OrderDetailBatchesRequestDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeStatisticsDTO;
import com.reagent.order.base.order.dto.request.OrderQRCodePageRequestDTO;
import com.ruijing.base.swagger.api.rpc.annotation.RpcApi;
import com.ruijing.base.swagger.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.annotation.MethodDeprecated;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

@RpcApi("商品批次信息（含二维码）RPC接口")
public interface OrderUniqueBarCodeRPCService {

    /**
     * 查询商品订单明细批次(含二维码)
     * @param request
     * @return 批次记录
     */
    @RpcMethod("根据detailId查询批次信息")
    RemoteResponse<List<OrderUniqueBarCodeDTO>> findByDetailId(OrderDetailBatchesRequestDTO request);

    /**
     * 查询商品订单明细批次(含二维码)
     * @param request
     * @return 批次记录
     */
    @RpcMethod("根据barCode查询单个批次信息")
    RemoteResponse<OrderUniqueBarCodeDTO> findByBarCode(OrderDetailBatchesRequestDTO request);

    /**
     * 查询商品订单明细批次(含二维码)
     * @param request
     * @return 批次记录
     */
    @RpcMethod("根据barCode批量查询批次信息, barCodeList必填")
    RemoteResponse<List<OrderUniqueBarCodeDTO>> findByBarCodeList(OrderDetailBatchesRequestDTO request);

    /**
     * 录入批次信息
     * @param request
     * @return
     */
    @RpcMethod("新增录入批次信息--单位一物一码用")
    RemoteResponse<Integer> addDetailBatches(List<OrderUniqueBarCodeDTO> request);

    /**
     * 修改批次信息。修改绑定气瓶时，传-1为删除绑定的气瓶
     * @param request
     * @return
     */
    @RpcMethod("修改录入批次信息，list.barCode不能为空")
    RemoteResponse<Integer> setDetailBatches(List<OrderUniqueBarCodeDTO> request);

    /**
     * 订单生成二维码--单位一物一码专用
     * @return
     */
    @RpcMethod("订单生成二维码--单位一物一码专用(需要供应商填写的)")
    RemoteResponse<Boolean> generatedBarCodeByOrder(OrderBaseParamDTO request);

    /**
     * 写入中爆条形码
     *
     * @param request 条形码数据
     * @return 是否写入成功
     */
    @RpcMethod("写入单位条形码--已经生成好的，不需要供应商填写")
    RemoteResponse<Boolean> insertOrgBarCode(List<OrderUniqueBarCodeDTO> request);

    /**
     * 写入中爆条形码
     *
     * @param request 条形码数据
     * @return 是否写入成功
     */
    @RpcMethod("写入中爆条形码--已经生成好的，不需要供应商填写")
    RemoteResponse<Boolean> insertCbsdBarCode(List<OrderUniqueBarCodeDTO> request);

    @RpcMethod("写入库房条形码--与订单无关联")
    RemoteResponse<Boolean> insertWarehouseBarcode(List<OrderUniqueBarCodeDTO> request);

    /**
     * 查询订单批次码录入统计信息
     * @param request
     * @return
     */
    @RpcMethod("查询订单批次码录入统计信息, orderNo必填")
    RemoteResponse<List<OrderUniqueBarCodeStatisticsDTO>> findBarCodeStatisticsByOrderNo(OrderDetailBatchesRequestDTO request);

    /**
     * 查询商品批次码录入统计信息
     * @param request
     * @return
     */
    @RpcMethod("查询商品批次码录入统计信息-- detailId,type必填")
    RemoteResponse<OrderUniqueBarCodeStatisticsDTO> findBarCodeStatisticsByDetailId(OrderDetailBatchesRequestDTO request);

    /**
     * 订单号查询订单明细批次(含二维码)
     * @param request
     * @return 批次记录
     */
    @RpcMethod("根据订单号查询所有批次信息--orderNo/orderNoList,type必填")
    RemoteResponse<List<OrderUniqueBarCodeDTO>> findByOrderNo(OrderDetailBatchesRequestDTO request);

    /**
     * 业务单号查询订单明细批次(含二维码)
     * @param request
     * @return 批次记录
     */
    @RpcMethod("根据业务单号查询所有批次信息, orderNo, entryNo, applyNo, exitNo, returnNo其一必填")
    RemoteResponse<List<OrderUniqueBarCodeDTO>> findByBusinessNo(OrderDetailBatchesRequestDTO request);

    /**
     * 根据业务单号更新对应批次信息
     * @param request
     * @return 更新数量
     */
    @RpcMethod("根据业务单号更新对应批次信息, orderNo, entryNo, applyNo, exitNo, returnNo其一必填, updatedStatus, expectStatus必填")
    @Deprecated
    @MethodDeprecated
    RemoteResponse<Integer> compareAndSetByBusinessNoAndStatus(OrderDetailBatchesRequestDTO request);

    @RpcMethod("根据业务单号更新对应批次信息, orderNo, entryNo, returnNo, uniBarCodeList其一必填, inventoryStatus, transactionStatus必填")
    RemoteResponse<Integer> updateStatusByBusinessNo(OrderDetailBatchesRequestDTO request);

    /**
     * 删除订单号批次信息, 慎用
     * @param request
     * @return 删除数量
     */
    @RpcMethod("删除订单号批次信息, orderNo,type必填")
    RemoteResponse<Integer> deleteByOrderNo(OrderDetailBatchesRequestDTO request);

    /**
     * 分页查询二维码批次信息
     * @param request 分页参数
     * @return 分页结果
     */
    @RpcMethod("分页查询二维码批次信息--仅支持单位一物一码")
    PageableResponse<List<OrderUniqueBarCodeDTO>> queryPage(OrderQRCodePageRequestDTO request);

    @RpcMethod("查询从指定的订单下，指定的barcode起始的limit条数据，用于数据迁移")
    @Deprecated
    RemoteResponse<List<OrderUniqueBarCodeDTO>> pageByBarcode(String orderNo, String startBarcode, Integer limit);
}
