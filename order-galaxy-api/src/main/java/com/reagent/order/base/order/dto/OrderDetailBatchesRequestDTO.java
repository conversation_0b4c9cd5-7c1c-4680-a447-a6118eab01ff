package com.reagent.order.base.order.dto;


import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

@RpcModel("订单批次信息查询入参(包含二维码)")
public class OrderDetailBatchesRequestDTO implements Serializable {

    private static final long serialVersionUID = -6617329349771995839L;

    @RpcModelProperty("条形码")
    private String uniBarCode;

    @RpcModelProperty("条形码数组")
    private List<String> uniBarCodeList;

    @RpcModelProperty("条形码类型--1.单位 2.中爆，不传默认查单位")
    private List<Integer> typeList;

    @RpcModelProperty("单个detailId, 单个商品批次查询用这个")
    private Integer detailId;

    @RpcModelProperty("数组detailId, 批量查询用这个")
    private List<Integer> detailIdList;

    @RpcModelProperty("查询数量, 默认0代表不限制数量")
    private int limit;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("入库单号")
    private String entryNo;

    @RpcModelProperty("申领单号")
    @Deprecated
    private String applyNo;

    @RpcModelProperty("出库单号")
    @Deprecated
    private String exitNo;

    @RpcModelProperty("退货单号")
    private String returnNo;

    /**
     * {@link com.reagent.order.base.order.enums.BarCodeStatusEnum}
     */
    @RpcModelProperty("码更新的状态")
    @Deprecated
    private Integer updatedStatus;

    /**
     * {@link com.reagent.order.base.order.enums.BarCodeStatusEnum}
     */
    @RpcModelProperty("码预期的状态")
    @Deprecated
    private Integer expectStatus;

    @RpcModelProperty(value = "库房状态", enumClass = OrderProductInventoryStatusEnum.class)
    private Integer inventoryStatus;

    @RpcModelProperty(value = "交易状态", enumClass = OrderProductTransactionStatusEnum.class)
    private Integer transactionStatus;

    @RpcModelProperty("是否有效, 1有效0无效")
    private Integer valid;

    @RpcModelProperty("订单号列表")
    private List<String> orderNoList;

    public List<Integer> getTypeList() {
        return typeList;
    }

    public OrderDetailBatchesRequestDTO setTypeList(List<Integer> typeList) {
        this.typeList = typeList;
        return this;
    }

    public List<Integer> getDetailIdList() {
        return detailIdList;
    }

    public void setDetailIdList(List<Integer> detailIdList) {
        this.detailIdList = detailIdList;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public OrderDetailBatchesRequestDTO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public int getLimit() {
        return limit;
    }

    public OrderDetailBatchesRequestDTO setLimit(int limit) {
        this.limit = limit;
        return this;
    }

    public String getUniBarCode() {
        return uniBarCode;
    }

    public OrderDetailBatchesRequestDTO setUniBarCode(String uniBarCode) {
        this.uniBarCode = uniBarCode;
        return this;
    }

    public List<String> getUniBarCodeList() {
        return uniBarCodeList;
    }

    public OrderDetailBatchesRequestDTO setUniBarCodeList(List<String> uniBarCodeList) {
        this.uniBarCodeList = uniBarCodeList;
        return this;
    }

    public String getEntryNo() {
        return entryNo;
    }

    public OrderDetailBatchesRequestDTO setEntryNo(String entryNo) {
        this.entryNo = entryNo;
        return this;
    }

    @Deprecated
    public String getApplyNo() {
        return applyNo;
    }

    @Deprecated
    public OrderDetailBatchesRequestDTO setApplyNo(String applyNo) {
        this.applyNo = applyNo;
        return this;
    }

    @Deprecated
    public String getExitNo() {
        return exitNo;
    }

    @Deprecated
    public OrderDetailBatchesRequestDTO setExitNo(String exitNo) {
        this.exitNo = exitNo;
        return this;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public OrderDetailBatchesRequestDTO setReturnNo(String returnNo) {
        this.returnNo = returnNo;
        return this;
    }

    @Deprecated
    public Integer getUpdatedStatus() {
        return updatedStatus;
    }

    @Deprecated
    public OrderDetailBatchesRequestDTO setUpdatedStatus(Integer updatedStatus) {
        this.updatedStatus = updatedStatus;
        return this;
    }

    @Deprecated
    public Integer getExpectStatus() {
        return expectStatus;
    }

    @Deprecated
    public OrderDetailBatchesRequestDTO setExpectStatus(Integer expectStatus) {
        this.expectStatus = expectStatus;
        return this;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public OrderDetailBatchesRequestDTO setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
        return this;
    }

    public Integer getTransactionStatus() {
        return transactionStatus;
    }

    public OrderDetailBatchesRequestDTO setTransactionStatus(Integer transactionStatus) {
        this.transactionStatus = transactionStatus;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OrderDetailBatchesRequestDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public OrderDetailBatchesRequestDTO setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailBatchesRequestDTO.class.getSimpleName() + "[", "]")
                .add("uniBarCode='" + uniBarCode + "'")
                .add("uniBarCodeList=" + uniBarCodeList)
                .add("typeList=" + typeList)
                .add("detailId=" + detailId)
                .add("detailIdList=" + detailIdList)
                .add("limit=" + limit)
                .add("orderNo='" + orderNo + "'")
                .add("entryNo='" + entryNo + "'")
                .add("applyNo='" + applyNo + "'")
                .add("exitNo='" + exitNo + "'")
                .add("returnNo='" + returnNo + "'")
                .add("updatedStatus=" + updatedStatus)
                .add("expectStatus=" + expectStatus)
                .add("valid=" + valid)
                .add("orderNoList=" + orderNoList)
                .toString();
    }
}
