package com.reagent.order.base.order.enums;

/**
 * 二维码打印枚举
 */
public enum BarCodePrintedEnum {
    UN_PRINT(0, "未打印"),
    PRINTED(1, "已打印"),
    ;

    BarCodePrintedEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public final int code;

    public final String desc;

    public BarCodePrintedEnum getByCode(Integer code) {
        BarCodePrintedEnum[] values = BarCodePrintedEnum.values();
        for (BarCodePrintedEnum item : values) {
            if (item.code == code) {
                return item;
            }
        }
        throw new IllegalStateException("未知状态:" + code);
    }
}
