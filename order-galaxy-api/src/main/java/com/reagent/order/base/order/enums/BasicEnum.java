package com.reagent.order.base.order.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 枚举基类, 枚举有映射关系可以实现此接口
 * 提供init方法, 初始化枚举成map k -> v
 * @param <K>
 */
public interface BasicEnum<K> {

    default <E extends Enum & BasicEnum> Map<K, E> init(Class<E> eClass) {
        E[] enumConstants = eClass.getEnumConstants();

        if (enumConstants.length == 0) {
            throw new IllegalStateException("specified enum is empty");
        }

        Map<K, E> enumMap = new HashMap<>(enumConstants.length);

        for (E it : enumConstants) {
            enumMap.put((K) it.getCode(), it);
        }

        return enumMap;
    }

    K getCode();
}
