package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderFileOperationLogDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogBatchQueryRequestDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogRequestDTO;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * 订单文件操作日志RPC服务接口
 */
public interface OrderFileOperationLogRpcService {

    /**
     * 批量查询订单文件操作日志
     *
     * @param requestDTO 批量查询请求参数，包含orderIds和logIds
     * @return 订单文件操作日志列表
     */
    @RpcMethod(value = "批量查询订单文件操作日志，支持按订单ID或日志ID查询，单次最多支持200条记录")
    RemoteResponse<List<OrderFileOperationLogDTO>> batchQuery(OrderFileOperationLogBatchQueryRequestDTO requestDTO);

    /**
     * 批量保存订单文件操作日志
     *
     * @param fileOperationLogs 批量保存请求参数
     * @return 是否保存成功
     */
    @RpcMethod(value = "批量保存订单文件操作日志，单次最多支持200条记录")
    RemoteResponse<Boolean> batchSave(List<OrderFileOperationLogRequestDTO> fileOperationLogs);
}
