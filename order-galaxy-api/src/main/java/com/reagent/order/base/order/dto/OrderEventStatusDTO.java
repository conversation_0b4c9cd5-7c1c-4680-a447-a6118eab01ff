package com.reagent.order.base.order.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/15 18:06
 * @description
 */
public class OrderEventStatusDTO implements Serializable {

    private static final long serialVersionUID = -6713365699241674812L;
    
    /**
     * 主键
     */
    private Integer id;
    
    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 单位id
     */
    private Integer orgId;
    
    /**
     * 事件类型
     */
    private Integer eventType;
    
    /**
     * 事件状态
     */
    private Integer eventStatus;
    
    /**
     * 创建时间
     */
    private Integer createTime;
    
    /**
     * 更新时间
     */
    private Integer updateTime;

    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Integer getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(Integer eventStatus) {
        this.eventStatus = eventStatus;
    }

    public Integer getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderEventTypeDTO{");
        sb.append("id=").append(id);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", orgId=").append(orgId);
        sb.append(", eventType=").append(eventType);
        sb.append(", eventStatus=").append(eventStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }
}
