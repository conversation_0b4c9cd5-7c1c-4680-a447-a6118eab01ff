package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: OrderBase 常规参数
 * @author: zhuk
 * @create: 2019-07-30 14:26
 **/
public class OrderBaseParamDTO implements Serializable {

    private static final long serialVersionUID = 386390741886401077L;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 订单编号数组
     */
    private List<String> orderNumberList;

    /**
     * orderStatus 参考枚举 RentcarOrderStatusEnum
     */
    private List<Integer> orderStatusList;

    /**
     * orderStatus 参考枚举 RentcarOrderStatusEnum
     */
    private Integer orderStatus;

    /**
     * 采购人id
     */
    private Long buyerId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单id 列表
     */
    private List<Long> orderIdList;

    /**
     * 机构编码 列表
     */
    private String orgCode;

    /**
     *  业务类型 对应 枚举类OrderBusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 查询条数限制
     */
    private Integer size;

    /**
     * 超时天数
     */
    private Integer timeOutDay;

    /**
     * 页码
     */
    private Integer pageNumber;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * detailId
     */
    private Integer detailId;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public List<Integer> getOrderStatusList() {
        return orderStatusList;
    }

    public void setOrderStatusList(List<Integer> orderStatusList) {
        this.orderStatusList = orderStatusList;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public List<Long> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Long> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getTimeOutDay() {
        return timeOutDay;
    }

    public void setTimeOutDay(Integer timeOutDay) {
        this.timeOutDay = timeOutDay;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getOrderNumberList() {
        return orderNumberList;
    }

    public void setOrderNumberList(List<String> orderNumberList) {
        this.orderNumberList = orderNumberList;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public OrderBaseParamDTO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderBaseParamDTO{");
        sb.append("orderNumber='").append(orderNumber).append('\'');
        sb.append(", orderNumberList=").append(orderNumberList);
        sb.append(", orderStatusList=").append(orderStatusList);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderIdList=").append(orderIdList);
        sb.append(", businessType=").append(businessType);
        sb.append(", size=").append(size);
        sb.append(", timeOutDay=").append(timeOutDay);
        sb.append(", pageNumber=").append(pageNumber);
        sb.append(", pageSize=").append(pageSize);
        sb.append('}');
        return sb.toString();
    }
}
