package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.dto.request.OrderAddressRequestDTO;
import com.ruijing.base.swagger.api.rpc.annotation.RpcApi;
import com.ruijing.base.swagger.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

@RpcApi("配送地址RPC接口")
public interface OrderAddressRPCService {

    /**
     * 批量插入配送地址接口
     * @param request   配送地址入参
     * @return          成功数
     */
    @RpcMethod("批量插入配送地址接口，单次操作不可超过100条，id不可为空")
    RemoteResponse<Integer> insertList(List<OrderAddressDTO> request);

    /**
     * 查询配送地址接口
     * @param request   查询入参
     * @return          配送地址
     */
    @RpcMethod("查询配送地址接口，单次操作不可超过100条, orderIdList不可为空")
    RemoteResponse<List<OrderAddressDTO>> listByOrderId(OrderBaseParamDTO request);

    /**
     * 更新配送地址接口
     * @param request   更新入参
     * @return          是否成功
     */
    @RpcMethod("更新配送地址接口，仅支持单个地址修改，id, orderNo不可为空")
    RemoteResponse<Boolean> saveAddress(OrderAddressDTO request);

    @RpcMethod("更新地址表，不连带更新订单主表的地址")
    RemoteResponse<Boolean> updateAddress(OrderAddressDTO orderAddressDTO);

    /**
     * 查询配送地址接口
     * @return
     */
    @RpcMethod("查询配送地址接口，orderNumber不可为空")
    RemoteResponse<OrderAddressDTO> findByOrderNo(OrderBaseParamDTO request);

    @RpcMethod("批量更新配送地址信息")
    RemoteResponse<Boolean> batchUpdateByOrderNo(List<OrderAddressDTO> orderAddressDTOList);

    @RpcMethod("查询操作过的代配送订单")
    PageableResponse<List<OrderAddressDTO>> findOrderIdInOperatorIdAndDeliveryStatus(OrderAddressRequestDTO requestDTO);
}
