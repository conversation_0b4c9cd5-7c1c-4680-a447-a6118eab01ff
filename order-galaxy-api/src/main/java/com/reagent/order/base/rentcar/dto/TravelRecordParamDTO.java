package com.reagent.order.base.rentcar.dto;

import java.io.Serializable;

/**
 * @description: 查询网约车行程记录传参DTO
 * @author: zhuk
 * @create: 2019-07-29 20:32
 **/
public class TravelRecordParamDTO implements Serializable {

    private static final long serialVersionUID = 8492305794861482194L;
    /**
     * 采购人id
     */
    private Long buyerId;

    /**
     *课题组id
     */
    private Long departmentId;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 当前页码
     */
    private Integer pageNumber;

    /**
     * 页面大小
     */
    private Integer pageSize;

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "FindTravelRecordDTO{" +
                "buyerId=" + buyerId +
                ", departmentId=" + departmentId +
                ", businessType=" + businessType +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                '}';
    }
}
