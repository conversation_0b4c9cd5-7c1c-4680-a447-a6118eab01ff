package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 结果对象
 * @author: zhuk
 * @create: 2019-08-05 20:48
 **/
public class OrderBaseResultDTO implements Serializable {

    private static final long serialVersionUID = 7541481237905824443L;
    /**
     * orderBaseList
     */
    private List<OrderBaseDTO> orderBaseDTOList;

    public List<OrderBaseDTO> getOrderBaseDTOList() {
        return orderBaseDTOList;
    }

    public void setOrderBaseDTOList(List<OrderBaseDTO> orderBaseDTOList) {
        this.orderBaseDTOList = orderBaseDTOList;
    }

    @Override
    public String toString() {
        return "OrderBaseResultDTO{" +
                "orderBaseDTOList=" + orderBaseDTOList +
                '}';
    }
}
