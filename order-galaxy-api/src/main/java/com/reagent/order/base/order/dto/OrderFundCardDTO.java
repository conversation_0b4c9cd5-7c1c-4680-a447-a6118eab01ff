package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class OrderFundCardDTO implements Serializable {

    private static final long serialVersionUID = -391479295749177084L;
    /**
     * id
     */
    private Integer id;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 经费卡id
     */
    private String fundCardId;

    /**
     * 经费卡code
     */
    private String fundCardCode;

    /**
     * 经费卡号
     */
    private String fundCardNo;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 是否曾经财务冻结过
     */
    private Integer froze;

    /**
     * 序列，整形
     */
    private Integer sequence;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getFundCardId() {
        return fundCardId;
    }

    public void setFundCardId(String fundCardId) {
        this.fundCardId = fundCardId;
    }

    public String getFundCardNo() {
        return fundCardNo;
    }

    public void setFundCardNo(String fundCardNo) {
        this.fundCardNo = fundCardNo;
    }

    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getFroze() {
        return froze;
    }

    public void setFroze(Integer froze) {
        this.froze = froze;
    }

    public String getFundCardCode() {
        return fundCardCode;
    }

    public void setFundCardCode(String fundCardCode) {
        this.fundCardCode = fundCardCode;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderFundCardDTO{");
        sb.append("id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", fundCardId='").append(fundCardId).append('\'');
        sb.append(", fundCardCode='").append(fundCardCode).append('\'');
        sb.append(", fundCardNo='").append(fundCardNo).append('\'');
        sb.append(", freezeAmount=").append(freezeAmount);
        sb.append(", froze=").append(froze);
        sb.append(", sequence=").append(sequence);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }
}
