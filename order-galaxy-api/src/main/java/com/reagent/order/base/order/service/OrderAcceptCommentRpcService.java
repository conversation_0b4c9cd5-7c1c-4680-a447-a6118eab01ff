package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderAcceptCommentDTO;
import com.reagent.order.base.order.dto.OrderAcceptQueryDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/2/25 15:32
 * @Description
 **/
public interface OrderAcceptCommentRpcService {
    /**
     * 南医深圳的评价标识map
     */
    Map<Integer, String> SMU_SZ_COMMENT_MAP = new HashMap<Integer, String>(){{
        put(1, "外包装完整");
        put(2, "外观质量完好");
        put(3, "标识清晰");
        put(4, "相关资料齐全");
    }};

    /**
     * 单位对应评价map
     */
    Map<Integer, Map<Integer, String>> ORG_COMMENT = new HashMap<Integer, Map<Integer, String>>(){{
        put(107, SMU_SZ_COMMENT_MAP);
    }};

    /**
     * @description: 保存订单验收评价，传入orgid, orderid, 评价id列表
     * @date: 2021/2/25 15:59
     * @author: zengyanru
     * @param orderAcceptCommentDTO
     * @return com.ruijing.fundamental.api.remote.RemoteResponse<java.lang.Boolean>
     */
    RemoteResponse<Boolean> saveOrderComment(OrderAcceptCommentDTO orderAcceptCommentDTO);

    /**
     * @description: 获取订单评价，传入订单orderid，orgid
     * @date: 2021/2/25 15:59
     * @author: zengyanru
     * @param input
     * @return com.ruijing.fundamental.api.remote.RemoteResponse<com.reagent.order.base.order.dto.OrderAcceptCommentDTO>
     */
    RemoteResponse<List<OrderAcceptCommentDTO>> getOrderComment(OrderAcceptQueryDTO input);
}
