package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: <PERSON><PERSON>
 * @Description:
 * @DateTime: 2021/6/28 15:33
 */
public class BaseOrderExtraDTO implements Serializable {

    private static final long serialVersionUID = -3382437369303276690L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 订单主表id，0无意义
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 单位id，0无意义
     */
    private Integer orgId;

    /**
     * 单位个性化操作类型，0无意义，由枚举维护
     */
    private Integer extraKey;

    /**
     * 拓展表key描述
     */
    private String extraKeyDesc;

    /**
     * 单位个性化描述字串
     */
    private String extraValue;

    /**
     * 生成时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(Integer extraKey) {
        this.extraKey = extraKey;
    }

    public String getExtraKeyDesc() {
        return extraKeyDesc;
    }

    public void setExtraKeyDesc(String extraKeyDesc) {
        this.extraKeyDesc = extraKeyDesc;
    }

    public String getExtraValue() {
        return extraValue;
    }

    public void setExtraValue(String extraValue) {
        this.extraValue = extraValue;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderExtraDTO{");
        sb.append("id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", orgId=").append(orgId);
        sb.append(", extraKey=").append(extraKey);
        sb.append(", extraKeyDesc='").append(extraKeyDesc).append('\'');
        sb.append(", extraValue='").append(extraValue).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append('}');
        return sb.toString();
    }
}
