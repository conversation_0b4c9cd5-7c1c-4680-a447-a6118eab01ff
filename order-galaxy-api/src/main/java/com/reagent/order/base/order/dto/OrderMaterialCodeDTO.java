package com.reagent.order.base.order.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单物资编码
 */
@RpcModel(value = "订单物资编码")
public class OrderMaterialCodeDTO implements Serializable {
    /**
    * id
    */
    @RpcModelProperty("id")
    private Long id;

    /**
    * 品牌
    */
    @RpcModelProperty("品牌")
    private String brand;

    /**
    * 货号
    */
    @RpcModelProperty("货号")
    private String goodCode;

    /**
    * 商品规格
    */
    @RpcModelProperty("商品规格")
    private String spec;

    /**
    * 物资编码
    */
    @RpcModelProperty("物资编码")
    private String materialCode;


    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(String goodCode) {
        this.goodCode = goodCode;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

}