package com.reagent.order.base.log.service;

import com.reagent.order.base.log.dto.OrderDockingLogDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

/**
 * @description: 订单 相关其他日志Rpc接口
 * @author: zhuk
 * @create: 2019-11-15 17:04
 **/
public interface OrderOtherLogRpcService {

    /**
     * 插入订单对接日志
     * @param orderDockingLogDTO 订单对接日志
     * @return RemoteResponse
     */
    RemoteResponse insertOrderDockingLog(OrderDockingLogDTO orderDockingLogDTO);
}
