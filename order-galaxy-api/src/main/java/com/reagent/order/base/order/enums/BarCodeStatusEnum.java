package com.reagent.order.base.order.enums;

import java.util.Map;

/**
 * 一物一码的状态
 */
public enum BarCodeStatusEnum implements BasicEnum<Integer> {

    UN_INPUT(0, "待录入批次"),
    WAITING_FOR_DELIVERY(1, "待发货"),
    WAITING_FOR_ACCEPT(2, "待收货"),
    WAITING_FOR_ENTRY(3, "待入库审批"),
    ENTERED(4, "已入库"),
    ENTRY_REBUT(5, "入库驳回"),
    WAITING_FOR_EXIT(6, "待出库审批"),
    EXITED(7, "已出库"),
    WAITING_FOR_CONFIRM_RETURN(8, "退货待确认"),
    CANCEL_RETURN(9, "取消退货"),
    APPROVED_RETURN(10,"同意退货 "),
    CONFIRM_RETURN(11,"退还货物" ),
    FINISH_RETURN(12,"已退货"),
    REFUSED_RETURN(13,"拒绝退货"),
    ACCEPTED(14,"已收货"),
    ;

    private static Map<Integer, BarCodeStatusEnum> enumConst = BarCodeStatusEnum.UN_INPUT.init(BarCodeStatusEnum.class);

    public final int code;

    public final String desc;

    BarCodeStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取映射枚举值
     * @param code
     * @return  enum instance
     */
    public static BarCodeStatusEnum getByCode(Integer code) {
        BarCodeStatusEnum instance = enumConst.get(code);
        if (instance == null) {
            throw new IllegalStateException("unknown BarCodeStatusEnum, current code:" + code);
        }
        return instance;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
