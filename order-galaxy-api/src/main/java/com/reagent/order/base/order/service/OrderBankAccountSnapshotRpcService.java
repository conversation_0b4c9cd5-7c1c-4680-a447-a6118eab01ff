package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderBankAccountSnapshotDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

public interface OrderBankAccountSnapshotRpcService {

    /**
     * 批量插入银行账户快照(订单生成时用，只插入）
     * @param list 快照列表
     * @return 是否成功
     */
    RemoteResponse<Boolean> insertList(List<OrderBankAccountSnapshotDTO> list);

    /**
     * 批量更新银行账户快照（只更新不插入）
     * @param list 要更新的数据
     * @return 是否成功
     */
    RemoteResponse<Boolean> updateList(List<OrderBankAccountSnapshotDTO> list);

    /**
     * 批量保存银行账户快照（有则更新，无则插入）
     * @param list 快照列表
     * @return 是否成功
     */
    RemoteResponse<Boolean> saveList(List<OrderBankAccountSnapshotDTO> list);

    /**
     * 根据订单id查询银行账户快照
     * @param orderIdList 订单id
     * @return 银行账户快照
     */
    RemoteResponse<List<OrderBankAccountSnapshotDTO>> listByOrderId(List<Integer> orderIdList);

    /**
     * 根据订单id删除银行账户快照
     * @param orderIdList 订单id
     * @return 是否成功
     */
    RemoteResponse<Boolean> deleteByOrderIdList(List<Integer> orderIdList);
}
