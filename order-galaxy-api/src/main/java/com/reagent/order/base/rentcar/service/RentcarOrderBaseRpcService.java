package com.reagent.order.base.rentcar.service;

import com.reagent.order.base.order.dto.OrderBasePageResultDTO;
import com.reagent.order.base.rentcar.dto.BuyerRentcarOrderBaseParamDTO;
import com.reagent.order.base.rentcar.dto.SuppRentcarOrderBaseParamDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

/**
 * @description: 租车订单相关业务接口
 * @author: zhuk
 * @create: 2019-07-29 11:18
 **/

public interface RentcarOrderBaseRpcService {

    /**
     * 供应商 网约车 Orderbase列表查询
     * @param suppRentcarOrderBaseParamDTO 入参
     * @return RentcarOrderBaseResultDTO
     */
    RemoteResponse<OrderBasePageResultDTO> rentcarOrderBaseListForSupp(SuppRentcarOrderBaseParamDTO suppRentcarOrderBaseParamDTO);

    /**
     * 采购人 网约车 orderBase 列表查询
     * @param buyerRentcarOrderBaseParamDTO 入参
     * @return RentcarOrderBaseResultDTO
     */
    RemoteResponse<OrderBasePageResultDTO> rentcarOrderBaseListForBuyer(BuyerRentcarOrderBaseParamDTO buyerRentcarOrderBaseParamDTO);
}
