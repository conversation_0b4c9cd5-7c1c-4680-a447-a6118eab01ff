package com.reagent.order.base.log.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * 批量订单号查询修正日志请求
 */
@RpcModel("批量订单号查询修正日志请求")
public class DataOperationLogQueryRequest implements Serializable {

    private static final long serialVersionUID = -1834567890123456789L;

    @RpcModelProperty("订单号列表")
    private List<String> orderNos;

    @RpcModelProperty("操作类型列表")
    private List<Integer> operationTypes;

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public List<Integer> getOperationTypes() {
        return operationTypes;
    }

    public void setOperationTypes(List<Integer> operationTypes) {
        this.operationTypes = operationTypes;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DataOperationLogQueryRequest.class.getSimpleName() + "[", "]")
                .add("orderNos=" + orderNos)
                .add("operationTypes=" + operationTypes)
                .toString();
    }
} 