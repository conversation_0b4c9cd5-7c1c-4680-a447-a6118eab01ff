package com.reagent.order.base.rentcar.dto;

import com.reagent.order.base.order.dto.OrderBaseDTO;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 分页查Orderbase结果DTO
 * @author: zhuk
 * @create: 2019-07-30 13:26
 **/
public class RentcarOrderBaseResultDTO implements Serializable {

    private static final long serialVersionUID = 7541481237905824443L;
    /**
     * orderBaseList
     */
    private List<OrderBaseDTO> orderBaseDTOList;

    /**
     * 当前页码
     */
    private Integer pageNumber;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 总数量
     */
    private Long totalNum;

    public List<OrderBaseDTO> getOrderBaseDTOList() {
        return orderBaseDTOList;
    }

    public void setOrderBaseDTOList(List<OrderBaseDTO> orderBaseDTOList) {
        this.orderBaseDTOList = orderBaseDTOList;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    @Override
    public String toString() {
        return "SuppRentcarOrderBaseResultDTO{" +
                "orderBaseDTOList=" + orderBaseDTOList +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                ", totalNum=" + totalNum +
                '}';
    }
}
