package com.reagent.order.base.order.dto.request;


import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 订单文件操作日志批量查询请求DTO
 */
@Model("订单文件操作日志批量查询请求DTO")
public class OrderFileOperationLogBatchQueryRequestDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    

    @ModelProperty("订单ID列表")
    private List<Integer> orderIds;
    

    @ModelProperty("日志ID列表")
    private List<Integer> logIds;
    
    public List<Integer> getOrderIds() {
        return orderIds;
    }
    
    public void setOrderIds(List<Integer> orderIds) {
        this.orderIds = orderIds;
    }
    
    public List<Integer> getLogIds() {
        return logIds;
    }
    
    public void setLogIds(List<Integer> logIds) {
        this.logIds = logIds;
    }

    @Override
    public String toString() {
        return "OrderFileOperationLogBatchQueryRequestDTO{" +
                "orderIds=" + orderIds +
                ", logIds=" + logIds +
                '}';
    }
}
