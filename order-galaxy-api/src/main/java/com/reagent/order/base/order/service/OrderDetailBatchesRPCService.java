package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderDetailBatchesDTO;
import com.reagent.order.base.order.dto.OrderDetailBatchesRequestDTO;
import com.reagent.order.base.order.dto.OrderDetailExtraDTO;
import com.reagent.order.base.order.dto.request.OrderDetailExtraListRequestDTO;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

public interface OrderDetailBatchesRPCService {
    /**
     * 更新订单明细批次
     * @param list
     * @return 成功数量
     */
    RemoteResponse<Integer> saveOrderDetailBatches(List<OrderDetailBatchesDTO> list);

    /**
     * 查询商品订单明细批次
     * @param request
     * @return 批次记录
     */
    RemoteResponse<List<OrderDetailBatchesDTO>> findByDetailIdIn(OrderDetailBatchesRequestDTO request);

    /**
     * 根据订单详情id或订单id查询订单详情额外信息，id集合数量限制200个
     * @param req
     * @return
     */
    @RpcMethod("根据订单详情id或订单id查询订单详情额外信息，id集合数量限制500个，订单id不为空使用订单id查询，否则使用订单详情id查询")
    @SuppressWarnings("迁移到 orderDetailExtraRpcService")
    RemoteResponse<List<OrderDetailExtraDTO>> listOrderDetailExtra(OrderDetailExtraListRequestDTO req);

    /**
     * 根据订单详情id或订单id查询订单详情额外信息，id集合数量限制200个
     * @param req
     * @return
     */
    @RpcMethod("根据订单详情id或订单id删除订单详情额外信息，id集合数量限制500个")
    @SuppressWarnings("迁移到 orderDetailExtraRpcService")
    RemoteResponse<Boolean> deleteOrderDetailExtra(OrderDetailExtraListRequestDTO req);

    /**
     * 批量插入订单详情额外信息数量限制200个
     * @param orderDetailExtraDTOList
     * @return
     */
    @RpcMethod("批量插入订单详情额外信息数量限制500个")
    @SuppressWarnings("迁移到 orderDetailExtraRpcService")
    RemoteResponse<Boolean> batchInsertOrderDetailExtra(List<OrderDetailExtraDTO> orderDetailExtraDTOList);

    /**
     * 批量保存订单详情额外信息
     * @param orderDetailExtraDTOList 数据
     * @return 是否成功
     */
    @RpcMethod("批量保存订单详情额外信息数量限制200个")
    RemoteResponse<Boolean> batchSaveOrderDetailExtra(List<OrderDetailExtraDTO> orderDetailExtraDTOList);
}
