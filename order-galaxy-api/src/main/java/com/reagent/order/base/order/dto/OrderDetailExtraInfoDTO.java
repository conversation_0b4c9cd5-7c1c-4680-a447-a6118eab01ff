package com.reagent.order.base.order.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 订单详情规格信息
 * @date 2023/10/27 27
 */
public class OrderDetailExtraInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单详情id")
    private Integer id;

    @RpcModelProperty("包装规格")
    private String packingUnit;

    @RpcModelProperty("包装数量")
    private String packingValue;

    @RpcModelProperty("最小包装规格")
    private String minPackingUnit;

    @RpcModelProperty("最小包装数量")
    private String minPackingValue;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPackingUnit() {
        return packingUnit;
    }

    public void setPackingUnit(String packingUnit) {
        this.packingUnit = packingUnit;
    }

    public String getPackingValue() {
        return packingValue;
    }

    public void setPackingValue(String packingValue) {
        this.packingValue = packingValue;
    }

    public String getMinPackingUnit() {
        return minPackingUnit;
    }

    public void setMinPackingUnit(String minPackingUnit) {
        this.minPackingUnit = minPackingUnit;
    }

    public String getMinPackingValue() {
        return minPackingValue;
    }

    public void setMinPackingValue(String minPackingValue) {
        this.minPackingValue = minPackingValue;
    }
}
