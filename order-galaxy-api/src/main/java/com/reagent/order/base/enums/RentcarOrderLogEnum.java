package com.reagent.order.base.enums;

/**
 * @description: 网约车订单日志类型
 * @author: zhuk
 * @create: 2019-08-07 17:47
 **/
public enum RentcarOrderLogEnum {

    /**
     * 打车
     */
    CALL_CAR(1,"乘客下单"),

    /**
     * 取消打车
     */
    CANCEL_CAR(2,"取消订单"),

    /**
     * 订单被接受
     */
    ACCEPTED_CAR(3,"司机接单"),

    /**
     *  乘车人上车/开始计费
     */
    INTO_CAR(4,"开始计费"),

    /**
     *  乘车人下车/结束计费
     */
    OUT_CAR(5,"结束计费"),

    /**
     * 乘车人支付
     */
    PAY_CAR(6,"乘客支付"),

    /**
     * 司机改派
     */
    CHANGE_DRIVER(7, "司机改派");

    public Integer value;

    public String operation;

    RentcarOrderLogEnum(Integer value, String operation) {
        this.value = value;
        this.operation = operation;
    }
}
