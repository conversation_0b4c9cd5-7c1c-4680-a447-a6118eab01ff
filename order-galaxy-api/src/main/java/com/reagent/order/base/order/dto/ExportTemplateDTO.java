package com.reagent.order.base.order.dto;

import com.ruijing.base.swagger.api.rpc.annotation.RpcModel;
import com.ruijing.base.swagger.api.rpc.annotation.RpcModelProperty;
import com.sun.org.apache.bcel.internal.generic.VariableLengthInstruction;

import java.io.Serializable;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/9/1 15:06
 */
@RpcModel
public class ExportTemplateDTO implements Serializable {

    private static final long serialVersionUID = -4159639129578178938L;
    /**
     * id
     */
    @RpcModelProperty(value = "id")
    private Integer id;

    /**
     * 用户id
     */
    @RpcModelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户类型，1-采购，3-oms
     */
    @RpcModelProperty(value = "用户类型，1-采购，3-oms")
    private Integer userType;

    @RpcModelProperty(value = "模板名称")
    private String templateName;

    /**
     * 模板自定义信息json
     */
    @RpcModelProperty(value = "模板自定义信息json")
    private String templateJson;

    @RpcModelProperty(value = "共享状态，0-未共享，1-共享")
    private Integer shareStatus;

    @RpcModelProperty(value = "单位id")
    private Integer orgId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getTemplateJson() {
        return templateJson;
    }

    public void setTemplateJson(String templateJson) {
        this.templateJson = templateJson;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Integer getShareStatus() {
        return shareStatus;
    }

    public void setShareStatus(Integer shareStatus) {
        this.shareStatus = shareStatus;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ExportTemplateDTO{");
        sb.append("id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", userType=").append(userType);
        sb.append(", templateName='").append(templateName).append('\'');
        sb.append(", templateJson='").append(templateJson).append('\'');
        sb.append(", shareStatus=").append(shareStatus);
        sb.append(", orgId=").append(orgId);
        sb.append('}');
        return sb.toString();
    }
}
