package com.reagent.order.base.order.dto;

import com.reagent.order.base.order.enums.BarCodePrintedEnum;
import com.reagent.order.base.order.enums.BarCodeStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductBatchesStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.StringJoiner;

public class OrderUniqueBarCodeDTO implements Serializable {

    private static final long serialVersionUID = 1072408443978854922L;

    @ModelProperty("条形码字段")
    private String uniBarCode;

    /**
     * 条形码类型 1：单位条形码 2：中爆条形码
     */
    @ModelProperty(value = "条形码类型 1：单位条形码 2：中爆条形码")
    private Integer type;

    /**
     * 订单号
     */
    @ModelProperty(value = "订单号")
    private String orderNo;

    /**
     * 订单明细id
     */
    @ModelProperty(value = "订单明细id")
    private Integer orderDetailId;

    /**
     * 商品名
     */
    @ModelProperty(value = "商品名")
    private String productName;

    /**
     * 商品货号
     */
    @ModelProperty(value = "商品货号")
    private String productCode;


    /**
     * 生产日期
     */
    @ModelProperty(value = "生产日期")
    private Date productionDate;

    /**
     * 规格
     */
    @ModelProperty(value = "规格")
    private String spec;

    /**
     * 品牌
     */
    @ModelProperty(value = "品牌")
    private String brand;

    /**
     * 供应商Id
     */
    @ModelProperty(value = "供应商Id")
    private Integer supplierId;

    /**
     * 供应商名称
     */
    @ModelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 商品单价
     */
    @ModelProperty(value = "商品单价")
    private BigDecimal price;

    /**
     * 批号
     */
    @ModelProperty(value = "批号")
    private String batches;

    /**
     * 有效期
     */
    @ModelProperty(value = "有效期")
    private String expiration;

    /**
     * 生产厂家
     */
    @ModelProperty(value = "生产厂家")
    private String manufacturer;

    /**
     * 耐久度(外观) 0正常1破损
     */
    @ModelProperty(value = "耐久度(外观) 0正常1破损")
    private Integer exterior;

    /**
     * 气瓶码，修改时传""为删除绑定的气瓶
     */
    @ModelProperty(value = "绑定的气瓶码")
    private String gasBottleBarcode;

    /**
     * 0：待录入批次
     * 1：待发货
     * 2：待收货
     * 3：待入库审批
     * 4：已入库
     * --入库驳回
     * 5：待出库审批
     * 6：已出库
     * 7：退货待确认
     * 8：取消退货
     * 9：同意退货
     * 10：退还货物
     * 11：已退货
     * 12: 拒绝退货
     * {@link BarCodeStatusEnum}
     * 拆分的三个子状态（batchesStatus，inventoryStatus，transactionStatus）适配完成后删除该字段
     */
    @Deprecated
    private Integer status;

    /**
     * 批次状态
     */
    @ModelProperty(value = "批次状态", enumClass = OrderProductBatchesStatusEnum.class)
    private Integer batchesStatus;

    /**
     * 库房状态
     */
    @ModelProperty(value = "库房状态", enumClass = OrderProductInventoryStatusEnum.class)
    private Integer inventoryStatus;

    /**
     * 交易状态
     */
    @ModelProperty(value = "交易状态", enumClass = OrderProductTransactionStatusEnum.class)
    private Integer transactionStatus;

    /**
     * 库房id
     */
    @ModelProperty("库房id")
    private Integer roomId;

    /**
     * 入库单号
     */
    @ModelProperty("入库单号")
    private String entryNo;

    /**
     * 申领单号
     */
    @ModelProperty("申领单号")
    private String applyNo;

    /**
     * 出库单号
     */
    @ModelProperty("出库单号")
    private String exitNo;

    /**
     * 退货单号
     */
    @ModelProperty("退货单号")
    private String returnNo;

    /**
     * 是否已打印
     */
    @ModelProperty(value = "是否已打印", enumClass = BarCodePrintedEnum.class)
    private Integer printed;

    /**
     * 数量
     */
    @ModelProperty("数量")
    private Integer total;

    /**
     * 商品图片
     */
    @ModelProperty("商品图片")
    private String productPicture;

    /**
     * 退货原因
     */
    @ModelProperty("退货原因")
    private String returnReason;

    /**
     * 退货说明
     */
    @ModelProperty("退货说明")
    private String returnDescription;

    /**
     * 操作用户的guid, 入库必填
     */
    @ModelProperty("操作用户的guid, 入库必填")
    private String userGuid;

    /**
     * 是否有效, 1有效0无效
     */
    @ModelProperty(value = "是否有效, 1有效0无效")
    private Integer valid;

    public String getUniBarCode() {
        return uniBarCode;
    }

    public OrderUniqueBarCodeDTO setUniBarCode(String uniBarCode) {
        this.uniBarCode = uniBarCode;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public OrderUniqueBarCodeDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OrderUniqueBarCodeDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public OrderUniqueBarCodeDTO setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public OrderUniqueBarCodeDTO setProductName(String productName) {
        this.productName = productName;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public OrderUniqueBarCodeDTO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public String getSpec() {
        return spec;
    }

    public OrderUniqueBarCodeDTO setSpec(String spec) {
        this.spec = spec;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public OrderUniqueBarCodeDTO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public OrderUniqueBarCodeDTO setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public OrderUniqueBarCodeDTO setSupplierName(String supplierName) {
        this.supplierName = supplierName;
        return this;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public OrderUniqueBarCodeDTO setPrice(BigDecimal price) {
        this.price = price;
        return this;
    }

    public String getBatches() {
        return batches;
    }

    public OrderUniqueBarCodeDTO setBatches(String batches) {
        this.batches = batches;
        return this;
    }

    public String getExpiration() {
        return expiration;
    }

    public OrderUniqueBarCodeDTO setExpiration(String expiration) {
        this.expiration = expiration;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public OrderUniqueBarCodeDTO setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public Integer getExterior() {
        return exterior;
    }

    public OrderUniqueBarCodeDTO setExterior(Integer exterior) {
        this.exterior = exterior;
        return this;
    }

    public String getGasBottleBarcode() {
        return gasBottleBarcode;
    }

    public OrderUniqueBarCodeDTO setGasBottleBarcode(String gasBottleBarcode) {
        this.gasBottleBarcode = gasBottleBarcode;
        return this;
    }

    @Deprecated
    public Integer getStatus() {
        return status;
    }

    @Deprecated
    public OrderUniqueBarCodeDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getBatchesStatus() {
        return batchesStatus;
    }

    public OrderUniqueBarCodeDTO setBatchesStatus(Integer batchesStatus) {
        this.batchesStatus = batchesStatus;
        return this;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public OrderUniqueBarCodeDTO setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
        return this;
    }

    public Integer getTransactionStatus() {
        return transactionStatus;
    }

    public OrderUniqueBarCodeDTO setTransactionStatus(Integer transactionStatus) {
        this.transactionStatus = transactionStatus;
        return this;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public OrderUniqueBarCodeDTO setRoomId(Integer roomId) {
        this.roomId = roomId;
        return this;
    }

    public String getEntryNo() {
        return entryNo;
    }

    public OrderUniqueBarCodeDTO setEntryNo(String entryNo) {
        this.entryNo = entryNo;
        return this;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public OrderUniqueBarCodeDTO setApplyNo(String applyNo) {
        this.applyNo = applyNo;
        return this;
    }

    public String getExitNo() {
        return exitNo;
    }

    public OrderUniqueBarCodeDTO setExitNo(String exitNo) {
        this.exitNo = exitNo;
        return this;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public OrderUniqueBarCodeDTO setReturnNo(String returnNo) {
        this.returnNo = returnNo;
        return this;
    }

    public Integer getPrinted() {
        return printed;
    }

    public OrderUniqueBarCodeDTO setPrinted(Integer printed) {
        this.printed = printed;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public OrderUniqueBarCodeDTO setTotal(Integer total) {
        this.total = total;
        return this;
    }

    public String getProductPicture() {
        return productPicture;
    }

    public OrderUniqueBarCodeDTO setProductPicture(String productPicture) {
        this.productPicture = productPicture;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public OrderUniqueBarCodeDTO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getReturnDescription() {
        return returnDescription;
    }

    public OrderUniqueBarCodeDTO setReturnDescription(String returnDescription) {
        this.returnDescription = returnDescription;
        return this;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public OrderUniqueBarCodeDTO setUserGuid(String userGuid) {
        this.userGuid = userGuid;
        return this;
    }

    public Integer getValid() {
        return valid;
    }

    public OrderUniqueBarCodeDTO setValid(Integer valid) {
        this.valid = valid;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderUniqueBarCodeDTO.class.getSimpleName() + "[", "]")
                .add("uniBarCode='" + uniBarCode + "'")
                .add("type=" + type)
                .add("orderNo='" + orderNo + "'")
                .add("orderDetailId=" + orderDetailId)
                .add("productName='" + productName + "'")
                .add("productCode='" + productCode + "'")
                .add("productionDate=" + productionDate)
                .add("spec='" + spec + "'")
                .add("brand='" + brand + "'")
                .add("supplierId=" + supplierId)
                .add("supplierName='" + supplierName + "'")
                .add("price=" + price)
                .add("batches='" + batches + "'")
                .add("expiration='" + expiration + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("exterior=" + exterior)
                .add("gasBottleBarcode='" + gasBottleBarcode + "'")
                .add("status=" + status)
                .add("batchesStatus=" + batchesStatus)
                .add("inventoryStatus=" + inventoryStatus)
                .add("transactionStatus=" + transactionStatus)
                .add("roomId=" + roomId)
                .add("entryNo='" + entryNo + "'")
                .add("applyNo='" + applyNo + "'")
                .add("exitNo='" + exitNo + "'")
                .add("returnNo='" + returnNo + "'")
                .add("printed=" + printed)
                .add("total=" + total)
                .add("productPicture='" + productPicture + "'")
                .add("returnReason='" + returnReason + "'")
                .add("returnDescription='" + returnDescription + "'")
                .add("userGuid='" + userGuid + "'")
                .add("valid=" + valid)
                .toString();
    }

    /**
     * 深拷贝的方法
     * @return
     */
    @Override
    public OrderUniqueBarCodeDTO clone() {
        OrderUniqueBarCodeDTO result = new OrderUniqueBarCodeDTO();
        result.setUniBarCode(this.getUniBarCode());
        result.setType(this.getType());
        result.setOrderNo(this.getOrderNo());
        result.setOrderDetailId(this.getOrderDetailId());
        result.setProductName(this.getProductName());
        result.setSpec(this.getSpec());
        result.setBatches(this.getBatches());
        result.setExpiration(this.getExpiration());
        result.setManufacturer(this.getManufacturer());
        result.setExterior(this.getExterior());
        result.setStatus(this.getStatus());
        result.setBatchesStatus(this.getBatchesStatus());
        result.setInventoryStatus(this.getInventoryStatus());
        result.setTransactionStatus(this.getTransactionStatus());
        result.setEntryNo(this.getEntryNo());
        result.setExitNo(this.getExitNo());
        result.setReturnNo(this.getReturnNo());
        result.setPrinted(this.getPrinted());
        result.setTotal(this.getTotal());
        result.setBrand(this.getBrand());
        result.setProductCode(this.getProductCode());
        result.setProductionDate(this.getProductionDate());
        result.setProductPicture(this.getProductPicture());
        result.setReturnReason(this.getReturnReason());
        result.setReturnDescription(this.getReturnDescription());
        result.setApplyNo(this.getApplyNo());
        result.setRoomId(this.getRoomId());
        result.setPrice(this.getPrice());
        result.setSupplierId(this.supplierId);
        result.setValid(this.valid);

        return result;
    }
}
