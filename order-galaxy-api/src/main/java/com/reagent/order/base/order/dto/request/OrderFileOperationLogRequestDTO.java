package com.reagent.order.base.order.dto.request;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;

/**
 * 订单文件操作日志请求DTO
 */
@Model("订单文件操作日志请求DTO")
public class OrderFileOperationLogRequestDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订单id
     */
    @ModelProperty("订单id")
    private Integer orderId;
    
    /**
     * 订单日志主表ID（t_order_approval_log）
     */
    @ModelProperty("订单日志主表ID")
    private Integer logId;
    
    /**
     * 文件路径
     */
    @ModelProperty("文件路径")
    private String url;
    
    /**
     * 文件名
     */
    @ModelProperty("文件名")
    private String fileName;
    
    public Integer getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
    
    public Integer getLogId() {
        return logId;
    }
    
    public void setLogId(Integer logId) {
        this.logId = logId;
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
