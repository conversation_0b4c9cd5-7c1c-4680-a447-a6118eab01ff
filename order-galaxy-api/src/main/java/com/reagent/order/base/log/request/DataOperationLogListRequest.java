package com.reagent.order.base.log.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/12/7 14:42
 * @description
 */
@RpcModel("oms异常日志查询请求")
public class DataOperationLogListRequest implements Serializable {

    @RpcModelProperty(value = "当前页码")
    private Integer pageNo;

    @RpcModelProperty(value = "每页记录数")
    private Integer pageSize;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("操作人姓名")
    private String operatorName;

    @RpcModelProperty("单位id")
    private Integer orgId;

    @RpcModelProperty("操作开始时间")
    private Date starTime;

    @RpcModelProperty("操作结束时间")
    private Date endTime;

    @RpcModelProperty("审批单号")
    private String approveNumber;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getStarTime() {
        return starTime;
    }

    public void setStarTime(Date starTime) {
        this.starTime = starTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getApproveNumber() {
        return approveNumber;
    }

    public void setApproveNumber(String approveNumber) {
        this.approveNumber = approveNumber;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DataOperationLogListRequest.class.getSimpleName() + "[", "]")
                .add("pageNo=" + pageNo)
                .add("pageSize=" + pageSize)
                .add("orderNo='" + orderNo + "'")
                .add("operatorName='" + operatorName + "'")
                .add("orgId=" + orgId)
                .add("starTime=" + starTime)
                .add("endTime=" + endTime)
                .add("approveNumber='" + approveNumber + "'")
                .toString();
    }
}
