package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderEventStatusDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/15 18:05
 * @description
 */
public interface OrderEventStatusRpcService {
    /**
     * 批量插入列表
     * @param orderEventStatusDTOS List<OrderEventStatusDTO>
     * @return
     */
    RemoteResponse<Integer> insertList(List<OrderEventStatusDTO> orderEventStatusDTOS);

    /**
     * 通过订单号和订单事件类型修改订单事件状态
     * @param orderEventStatusDTO OrderEventStatusDTO
     * @return
     */
    RemoteResponse<Integer> updateByOrderNoAndEventType(OrderEventStatusDTO orderEventStatusDTO);

    /**
     * 通过订单号和事件类型查询订单事件状态记录
     * @param orderEventStatusDTO OrderEventStatusDTO
     * @return
     */
    RemoteResponse<OrderEventStatusDTO> selectByOrderNoAndEventType(OrderEventStatusDTO orderEventStatusDTO);

    /**
     * 通过订单号列表和事件类型查询订单事件状态记录
     * @param orderNoList 订单号清单
     * @param eventType 事件类型 
     * @return List<OrderEventStatusDTO> 订单事件状态类型
     */
    RemoteResponse<List<OrderEventStatusDTO>> selectByOrderNoListAndEventType(List<String> orderNoList,Integer eventType);

    /**
     * 通过订单号查询订单事件状态
     * @param orderNo 订单号
     * @return
     */
    RemoteResponse<List<OrderEventStatusDTO>> selectByOrderNo(String orderNo);

    /**
     * 通过订单号和事件类型删除订单事件状态
     * @param orderEventStatusDTO OrderEventStatusDTO
     * @return
     */
    RemoteResponse<Integer> deleteByOrderNoAndEventType(OrderEventStatusDTO orderEventStatusDTO);

    /**
     * 通过订单号删除订单事件状态
     * @param orderNo 订单号
     * @return
     */
    RemoteResponse<Integer> deleteByOrderNo(String orderNo);
    
}
