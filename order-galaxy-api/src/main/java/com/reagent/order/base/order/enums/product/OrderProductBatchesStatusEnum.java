package com.reagent.order.base.order.enums.product;

import com.reagent.order.base.order.enums.BarCodeStatusEnum;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-04-22 11:27
 * @description: 订单一物批次状态
 */
public enum OrderProductBatchesStatusEnum {

    UN_INPUT(0, "待录入"),

    INPUTTED(1,"已录入"),
    ;

    private final int code;

    private final String desc;

    OrderProductBatchesStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderProductBatchesStatusEnum getByCode(Integer code) {
        if(code == null){
            return null;
        }
        for(OrderProductBatchesStatusEnum e : OrderProductBatchesStatusEnum.values()){
            if(e.code == code){
                return e;
            }
        }
        return null;
    }
}
