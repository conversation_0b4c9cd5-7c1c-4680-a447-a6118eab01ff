package com.reagent.order.base.enums;

/**
 * 网约车订单状态
 * <AUTHOR>
 * @create: 2019-07-30
 *
 */
public enum RentcarOrderStatusEnum {

    /**
     * 待接单  已下单成功，平台还没分配车辆和司机
     */
    WAITING_ORDER(51,"待接单"),

    /**
     * 已接单  平台已分配车辆和司机
     */
    ACCEPTED_ORDER(52,"已接单"),


    /**
     *  用户取消  平台已接单，开始计费前，用户取消订单
     */
    USER_CANCEL_ACCEPTED(53,"用户取消已接订单"),

    /**
     * 服务中 司机已达出发地，开始计费
     */
    SERVICING_ORDER(54,"服务中"),

    /**
     * 待支付 司机已达目的地，结束计费
     */
    PAYING_ORDER(55,"待支付"),

    /**
     * 系统取消 平台无司机接单，超时180s
     */
    SYSTEM_CANCEL(56,"系统取消"),

    /**
     * 客服取消  司机接单后，订单支付前，供应商平台取消订单
     */
    SUPPLIER_CANCEL(57,"客服取消"),

    /**
     * 用户取消（未接单） 等待应答时，用户主动取消订单
     */
    USER_CANCEL_WAITING(58,"用户取消未接订单"),

    /**
     * 待提交 用户确认支付费用
     */
    WAITING_APPROVAL(60,"待提交"),

    /**
     * 审批中 用户提交审批后
     */
    PENDING_APPROVAL(61,"审批中"),

    /**
     * 审批驳回 订单被驳回
     */
    REJECTED_APPROVAL(62,"审批驳回"),

    /**
     * 审批通过
     */
    AGREE_APPROVAL(63,"审批通过"),

    /**
     * 结算中/冻结成功
     */
    PENDING_SETTLEMENT(70,"结算中"),

    /**
     * 结算完成
     */
    OVER_SETTLEMENT(71,"结算完成");

    /**
     * statusValue
     */
    public Integer statusValue;

    /**
     * statusName
     */
    public String statusName;

    RentcarOrderStatusEnum(Integer statusValue, String statusNmae) {
        this.statusValue = statusValue;
        this.statusName = statusNmae;
    }
}
