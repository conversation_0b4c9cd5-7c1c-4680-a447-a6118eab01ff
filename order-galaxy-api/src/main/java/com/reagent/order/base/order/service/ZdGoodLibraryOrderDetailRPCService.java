package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.ZdGoodLibraryOrderDetailDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @description 中大商品库订单接口
 * @date 2023/11/10 17
 */
public interface ZdGoodLibraryOrderDetailRPCService {

    /**
     * 批量插入 中大商品库旧订单详情 数量不可超过400
     * @param dtoList
     * @return
     */
    RemoteResponse<Boolean> batchInsertSelective(List<ZdGoodLibraryOrderDetailDTO> dtoList);
}
