package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderDetailAcceptancePicDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptancePicRequestDTO;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.annotation.MethodParam;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

public interface OrderDetailAcceptancePicRpcService {

    /**
     * 根据订单ID查询 详情关联验收图片列表
     *
     * @param orderId 订单ID
     * @return 详情关联验收图片列表
     */
    RemoteResponse<List<OrderDetailAcceptancePicDTO>> listByOrderId(@MethodParam("orderId") Integer orderId);

    /**
     * 根据订单ID集合批量查询 详情关联验收图片列表
     *
     * @param orderIds 订单ID集合（最大支持200个）
     * @return 详情关联验收图片列表
     */
    @RpcMethod(value = "根据订单ID批量查询详情关联验收图片列表，单次最多200个订单")
    RemoteResponse<List<OrderDetailAcceptancePicDTO>> listByOrderIds(@MethodParam("orderIds") List<Integer> orderIds);

    /**
     * 根据订单ID删除 详情关联验收图片列表
     *
     * @param orderId 订单ID
     * @return 详情关联验收图片列表
     */
    RemoteResponse<Boolean> deleteByOrderId(@MethodParam("orderId") Integer orderId);

    /**
     * 批量保存 详情关联验收图片列表
     *
     * @param list 详情关联验收图片列表
     * @return boolean
     */
    RemoteResponse<Boolean> batchSave(List<OrderDetailAcceptancePicRequestDTO> list);

}