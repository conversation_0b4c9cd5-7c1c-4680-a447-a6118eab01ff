package com.reagent.order.base.order.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 订单详情额外信息传输类
 * @date 2023/7/18 10:29
 */
public class OrderDetailExtraDTO implements Serializable {

    /**
     * 主键
     */
    @RpcModelProperty("主键")
    private Integer id;

    /**
     * 订单主表id，0无意义
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 订单详情id
     */
    @RpcModelProperty("订单详情id")
    private Integer orderDetailId;

    /**
     * 单位id，0无意义
     */
    @RpcModelProperty("单位id")
    private Integer orgId;

    /**
     * 单位个性化操作类型,由枚举维护
     */
    @RpcModelProperty(value = "单位个性化操作类型，由枚举维护，用于保存及查询", enumLink = "com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum")
    private String extraKey;

    /**
     * 单位个性化操作类型,由枚举维护
     */
    @RpcModelProperty(value = "单位个性化操作类型，由枚举维护，用于保存及查询", enumLink = "com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum")
    private Integer extraKeyType;

    /**
     * 单位个性化描述字串
     */
    @RpcModelProperty("单位个性化描述字串")
    private String extraValue;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(String extraKey) {
        this.extraKey = extraKey;
    }

    public String getExtraValue() {
        return extraValue;
    }

    public void setExtraValue(String extraValue) {
        this.extraValue = extraValue;
    }

    public Integer getExtraKeyType() {
        return extraKeyType;
    }

    public void setExtraKeyType(Integer extraKeyType) {
        this.extraKeyType = extraKeyType;
    }

    @Override
    public String toString() {
        return "OrderDetailExtraDTO{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", orderDetailId=" + orderDetailId +
                ", orgId=" + orgId +
                ", extraKey='" + extraKey + '\'' +
                ", extraValue='" + extraValue + '\'' +
                '}';
    }
}
