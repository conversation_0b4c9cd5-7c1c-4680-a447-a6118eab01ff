package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 10:50
 * @Version 1.0
 * @Desc:描述
 */
public class BasePageResultDTO<T> implements Serializable {

    private static final long serialVersionUID = 3667979039471500875L;
    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页记录数
     */
    private Integer pageSize;

    /**
     * 记录总数
     */
    private long total;

    /**
     * 数据体
     */
    private List<T> data;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("BasePageResultDTO{");
        sb.append("pageNo=").append(pageNo);
        sb.append(", pageSize=").append(pageSize);
        sb.append(", total=").append(total);
        sb.append(", data=").append(data);
        sb.append('}');
        return sb.toString();
    }
}
