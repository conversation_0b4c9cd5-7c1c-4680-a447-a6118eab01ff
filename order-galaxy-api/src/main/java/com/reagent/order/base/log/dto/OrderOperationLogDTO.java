package com.reagent.order.base.log.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 订单操作日志 DTO
 * @author: zhuk
 * @create: 2019-07-31 20:58
 **/
public class OrderOperationLogDTO implements Serializable {
    private static final long serialVersionUID = -4542358047827463884L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 业务类型 枚举
     */
    private Integer businessType;

    /**
     * 操作类型，生成/供应商确认订单/取消订单  枚举
     */
    private Integer operation;

    /**
     * 操作类型对应的文字描述
     */
    private String operationDesc;


    /**
     * 操作备注取消原因，拒绝取消原因，验收图片，退货原因。
     */
    private String operationNote;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人类型。采购人，供应商，系统
     */
    private Integer operatorType;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作人课题组id
     */
    private Long operatorDepartId;

    /**
     * 操作人课题组名称
     */
    private String operatorDepartName;

    /**
     * 操作人orgid
     */
    private Long operatorOrgId;

    /**
     * 操作人orgCode
     */
    private String operatorOrgCode;

    /**
     * 操作人org名称
     */
    private String operatorOrgName;

    /**
     * 生成时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public String getOperationNote() {
        return operationNote;
    }

    public void setOperationNote(String operationNote) {
        this.operationNote = operationNote;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Long getOperatorDepartId() {
        return operatorDepartId;
    }

    public void setOperatorDepartId(Long operatorDepartId) {
        this.operatorDepartId = operatorDepartId;
    }

    public String getOperatorDepartName() {
        return operatorDepartName;
    }

    public void setOperatorDepartName(String operatorDepartName) {
        this.operatorDepartName = operatorDepartName;
    }

    public Long getOperatorOrgId() {
        return operatorOrgId;
    }

    public void setOperatorOrgId(Long operatorOrgId) {
        this.operatorOrgId = operatorOrgId;
    }

    public String getOperatorOrgCode() {
        return operatorOrgCode;
    }

    public void setOperatorOrgCode(String operatorOrgCode) {
        this.operatorOrgCode = operatorOrgCode;
    }

    public String getOperatorOrgName() {
        return operatorOrgName;
    }

    public void setOperatorOrgName(String operatorOrgName) {
        this.operatorOrgName = operatorOrgName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    @Override
    public String toString() {
        return "OrderOperationLogDTO{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", orderNumber='" + orderNumber + '\'' +
                ", businessType=" + businessType +
                ", operation=" + operation +
                ", operationDesc='" + operationDesc + '\'' +
                ", operationNote='" + operationNote + '\'' +
                ", operatorId=" + operatorId +
                ", operatorType=" + operatorType +
                ", operatorName='" + operatorName + '\'' +
                ", operatorDepartId=" + operatorDepartId +
                ", operatorDepartName='" + operatorDepartName + '\'' +
                ", operatorOrgId=" + operatorOrgId +
                ", operatorOrgCode='" + operatorOrgCode + '\'' +
                ", operatorOrgName='" + operatorOrgName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
