package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.Date;

public class OrderDetailBatchesDTO implements Serializable {

    private static final long serialVersionUID = -8074241579199131931L;

    private Integer id;

    /**
     * 订单明细id
     */
    private Integer detailId;

    /**
     * 明细录入批次
     */
    private String batches;

    /**
     * 录入有效期
     */
    private String expiration;

    /**
     * 耐久度(外观) 0正常1破损
     */
    private Integer exterior;

    private Date createTime;

    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getBatches() {
        return batches;
    }

    public void setBatches(String batches) {
        this.batches = batches;
    }

    public String getExpiration() {
        return expiration;
    }

    public void setExpiration(String expiration) {
        this.expiration = expiration;
    }

    public Integer getExterior() {
        return exterior;
    }

    public void setExterior(Integer exterior) {
        this.exterior = exterior;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", detailId=").append(detailId);
        sb.append(", batches=").append(batches);
        sb.append(", expiration=").append(expiration);
        sb.append(", exterior=").append(exterior);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}
