package com.reagent.order.base.order.enums.product;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-04-22 11:27
 * @description:
 */
public enum OrderProductInventoryStatusEnum {

    WAITING_FOR_INBOUND(0, "待入库"),
    NO_NEED(1, "无需入库"),
    INBOUNDING(2, "入库中"),
    COMPLETE_INBOUND(3, "已入库"),
    COMPLETE_OUTBOUND(4, "已出库"),
    ;

    private final int code;

    private final String desc;

    OrderProductInventoryStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderProductInventoryStatusEnum getByCode(Integer code) {
        if(code == null){
            return null;
        }
        for(OrderProductInventoryStatusEnum e : OrderProductInventoryStatusEnum.values()){
            if(e.code == code){
                return e;
            }
        }
        return null;
    }
}
