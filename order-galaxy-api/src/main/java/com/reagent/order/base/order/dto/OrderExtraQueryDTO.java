package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 订单外部查询条件DTO
 * @author: zhuk
 * @create: 2019-08-27 18:28
 **/
public class OrderExtraQueryDTO implements Serializable {

    private static final long serialVersionUID = 4299309552705764922L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 业务类型 枚举
     */
    private Integer businessType;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 字段名称
     */
    private String key;

    /**
     * 字段值
     */
    private String value;

    /**
     * 字段类型
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "OrderExtraQueryDTO{" +
                "id=" + id +
                ", businessType=" + businessType +
                ", orderId=" + orderId +
                ", key='" + key + '\'' +
                ", value='" + value + '\'' +
                ", type=" + type +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
