package com.reagent.order.base.order.dto;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;


@Model("订单 列表类型拓展字段DTO")
public class ListOrderExtraDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单ID")
    private Integer orderId;

    @ModelProperty(value = "拓展字段key", enumLink = "com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum")
    private Integer extraKey;

    @ModelProperty("拓展字段值")
    private List<String> extraValueList;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(Integer extraKey) {
        this.extraKey = extraKey;
    }

    public List<String> getExtraValueList() {
        return extraValueList;
    }

    public void setExtraValueList(List<String> extraValueList) {
        this.extraValueList = extraValueList;
    }

    @Override
    public String toString() {
        return "ListOrderExtraDTO{" +
                "orderId=" + orderId +
                ", extraKey=" + extraKey +
                ", extraValueList=" + extraValueList +
                '}';
    }
}
