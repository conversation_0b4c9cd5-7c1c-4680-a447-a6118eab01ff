package com.reagent.order.base.rentcar.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 供应商 查询  网约车订单 参数
 * @author: zhuk
 * @create: 2019-07-30 11:44
 **/
public class SuppRentcarOrderBaseParamDTO implements Serializable {

    private static final long serialVersionUID = 2458445966385306898L;
    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 起始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 订单状态
     */
    private List<Integer> orderStatusList;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 采购人名称
     */
    private String buyerName;

    /**
     * 单位名称
     */
    private String orgName;

    /**
     * 当前页码
     */
    private Integer pageNumber;

    /**
     * 页面大小
     */
    private Integer pageSize;

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public List<Integer> getOrderStatusList() {
        return orderStatusList;
    }

    public void setOrderStatusList(List<Integer> orderStatusList) {
        this.orderStatusList = orderStatusList;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "SuppRentcarOrderBaseParamDTO{" +
                "supplierId=" + supplierId +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", businessType=" + businessType +
                ", orderStatus=" + orderStatusList +
                ", orderNumber='" + orderNumber + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", orgName='" + orgName + '\'' +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                '}';
    }
}
