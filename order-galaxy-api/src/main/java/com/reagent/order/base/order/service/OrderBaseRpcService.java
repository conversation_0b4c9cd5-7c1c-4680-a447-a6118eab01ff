package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.*;
import com.reagent.order.base.rentcar.dto.BuyerRentcarOrderBaseParamDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * @description: OrderBase RPC服务
 * @author: z<PERSON><PERSON>
 * @create: 2019-07-30 15:10
 **/

public interface OrderBaseRpcService {

    /**
     * 根据订单号查询 orderBase
     * @param paramDTO 入参
     * @return 订单信息
     */
    RemoteResponse<OrderBaseDTO> findOrderBaseByOrderNumber(OrderBaseParamDTO paramDTO);

    /**
     * 创建OrderExtraQuery
     * @param orderBaseAndExtrasDTO 入参
     * @return RemoteResponse
     */
    RemoteResponse createOrderBaseAndExtraQuery(OrderBaseAndExtrasDTO orderBaseAndExtrasDTO);

    /**
     * 根据主键 用非空值 更新订单
     *
     * @param orderBaseDTO 入参
     * @return 更新行数
     */
    RemoteResponse updateOrderBaseById(OrderBaseDTO orderBaseDTO);

    /**
     * 根据用户id 和 订单状态查询订单条数
     * @param orderBaseParamDTO
     * @return
     */
    RemoteResponse<Integer> countByBuyerIdAndStatus(OrderBaseParamDTO orderBaseParamDTO);

    /**
     * 根据订单id  查询 orderBase信息
     *
     * @param orderBaseParamDTO 入参
     * @return OrderBaseDTO
     */
    RemoteResponse<OrderBaseDTO> findOrderBaseById(OrderBaseParamDTO orderBaseParamDTO);

    /**
     * 根据订单id集合 查询订单
     *
     * @param orderBaseParamDTO
     * @return
     */
    RemoteResponse<OrderBaseResultDTO> findOrderBaseByIdList(OrderBaseParamDTO orderBaseParamDTO);

    /**
     * 根据 BusinessType 生成订单号
     *
     * @return 订单号
     */
    RemoteResponse<Long> getOrderId();

    /**
     * 创建orderBase
     *
     * @param orderBaseDTO 入参
     * @return boolean
     */
    RemoteResponse createOrderBase(OrderBaseDTO orderBaseDTO);

    /**
     * 更新订单状态
     *
     * @param orderBaseParamDTO 入参
     * @return 结果
     */
    RemoteResponse updateOrderBaseStatus(OrderBaseParamDTO orderBaseParamDTO);

    /**
     * 根据 用户和订单状态差查询订单信息
     *
     * @param orderBaseParamDTO 用户id 订单状态
     * @return List<OrderBaseDTO>
     */
    RemoteResponse<List<OrderBaseDTO>> findOrderByBuyerAndStatusLis(OrderBaseParamDTO orderBaseParamDTO);

    /**
     *  中大 查询公示栏订单
     * @param publicityOrderParamDTO 入参
     * @return 订单集合
     */
    RemoteResponse<OrderBasePageResultDTO> findPublicityOrder(PublicityOrderParamDTO publicityOrderParamDTO);

    /**
     * 获取行程记录
     * @param buyerParamDTO 入参
     * @return RemoteResponse
     */
    RemoteResponse<OrderBasePageResultDTO> getTravelRecordList(BuyerRentcarOrderBaseParamDTO buyerParamDTO);

    /**
     * 根据状态和超时天数 获取超时订单
     * @param orderBaseParamDTO
     * @return
     */
    RemoteResponse<OrderBasePageResultDTO> getTimeOutOrder(OrderBaseParamDTO orderBaseParamDTO);
}
