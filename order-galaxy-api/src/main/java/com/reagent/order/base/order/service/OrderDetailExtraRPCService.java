package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderDetailExtraDTO;
import com.reagent.order.base.order.dto.OrderDetailExtraInfoDTO;
import com.reagent.order.base.order.dto.request.OrderDetailExtraListRequestDTO;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @description 订单额外信息rpc服务
 * @date 2023/10/27 54
 */
public interface OrderDetailExtraRPCService {


    /**
     * 根据订单详情id查询订单详情规格信息，id集合数量限制500个
     * @param req
     * @return
     */
    @RpcMethod("根据订单详情id查询订单详情规格信息，id集合数量限制500个")
    RemoteResponse<List<OrderDetailExtraInfoDTO>> listOrderDetailExtraInfo(OrderDetailExtraListRequestDTO req);

    /**
     * 根据订单详情id或订单id查询订单详情额外信息，id集合数量限制500个
     * @param req
     * @return
     */
    @RpcMethod("根据订单详情id或订单id查询订单详情额外信息，id集合数量限制200个，订单id不为空使用订单id查询，否则使用订单详情id查询")
    RemoteResponse<List<OrderDetailExtraDTO>> listOrderDetailExtra(OrderDetailExtraListRequestDTO req);

    /**
     * 根据订单详情id或订单id查询订单详情额外信息，id集合数量限制500个
     * @param req
     * @return
     */
    @RpcMethod("根据订单详情id或订单id删除订单详情额外信息，id集合数量限制200个")
    RemoteResponse<Boolean> deleteOrderDetailExtra(OrderDetailExtraListRequestDTO req);

    /**
     * 批量插入订单详情额外信息数量限 500个
     * @param orderDetailExtraDTOList
     * @return
     */
    @RpcMethod("批量插入订单详情额外信息数量限制200个")
    RemoteResponse<Boolean> batchInsertOrderDetailExtra(List<OrderDetailExtraDTO> orderDetailExtraDTOList);

}
