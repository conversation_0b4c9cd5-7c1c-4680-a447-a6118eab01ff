package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.RefOrderFundCardDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @description 订单关联经费卡表服务类
 * @date 2024/2/5 下午 02:57
 */
public interface RefFundCardOrderRpcService {

    RemoteResponse<List<RefOrderFundCardDTO>> listInOrderId(List<Integer> orderIdList);

    RemoteResponse<Boolean> batchInsertSelective(List<RefOrderFundCardDTO> refOrderFundCardDTOList);

    RemoteResponse<Boolean> batchUpdateSelective(List<RefOrderFundCardDTO> refOrderFundCardDTOList);

    /**
     * 根据orderId更新经费卡信息，目前仅用于拆单，拆单为单经费卡才可拆单，不存在更新多条数据问题
     * @param refOrderFundCardDTO 经费卡信息
     * @return 是否更新成功
     */
    RemoteResponse<Boolean> updateByOrderId(RefOrderFundCardDTO refOrderFundCardDTO);

    /**
     * 根据id删除记录
     * @return 是否删除成果
     */
    RemoteResponse<Boolean> deleteInOrderId(List<Integer> orderIdList);
}
