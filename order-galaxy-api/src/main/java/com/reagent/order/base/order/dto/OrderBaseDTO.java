package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 出行记录返回结果
 * @author: zhukai
 * @create: 2019-07-29 20:50
 **/
public class OrderBaseDTO implements Serializable {

    private static final long serialVersionUID = 1907280729075979601L;

    /**
     * 订单id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 父订单id
     */
    private Long parentId;

    /**
     * 订单父 订单号
     */
    private String parentNumber;

    /**
     * 来源类型,  对应类型枚举
     */
    private Integer sourceType;

    /**
     * 来源 id
     */
    private Long sourceId;

    /**
     * 来源单号
     */
    private String sourceNumber;

    /**
     * 采购人id
     */
    private Long buyerId;

    /**
     * 采购人姓名
     */
    private String buyerName;

    /**
     * 采购人电话
     */
    private String buyerPhone;

    /**
     * 采购人邮箱
     */
    private String buyerEmail;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 组织代码
     */
    private String orgCode;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 课题组id
     */
    private Long departmentId;

    /**
     * 课题组名称
     */
    private String departmentName;

    /**
     * 学院id，课题组父id
     */
    private Long collegeId;

    /**
     * 学院名称，课题组父 名称
     */
    private String collegeName;


    /**
     * 业务类型  网约车、store
     */
    private Integer businessType;

    /**
     * 订单状态  枚举
     */
    private Integer orderStatus;

    /**
     * 订单成交总价  单位 分
     */
    private Long actualAmount;

    /**
     * 订单原总价价
     */
    private Long originalPrice;

    /**
     * 订单优惠总金额
     */
    private Long discountsAmount;

    /**
     * 流程类型  0：线上单  1：线下单
     */
    private Integer processType;

    /**
     * 供应商订单描述
     */
    private String descriptionSupplier;

    /**
     * 采购人订单描述
     */
    private String descriptionBuyer;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 供应商邮箱
     */
    private String supplierEmail;

    /**
     * 供应商联系电话
     */
    private String supplierPhone;

    /**
     * 运费 单位 分
     */
    private Long carryFee;

    /**
     * 收货人名称
     */
    private String receiverName;

    /**
     * 收货人联系方式
     */
    private String receiverPhone;

    /**
     * 收货地址
     */
    private String receiverAddress;

    /**
     * 生成时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentNumber() {
        return parentNumber;
    }

    public void setParentNumber(String parentNumber) {
        this.parentNumber = parentNumber;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Long getSourceId() {
        return sourceId;
    }

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceNumber() {
        return sourceNumber;
    }

    public void setSourceNumber(String sourceNumber) {
        this.sourceNumber = sourceNumber;
    }

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId = buyerId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getBuyerPhone() {
        return buyerPhone;
    }

    public void setBuyerPhone(String buyerPhone) {
        this.buyerPhone = buyerPhone;
    }

    public String getBuyerEmail() {
        return buyerEmail;
    }

    public void setBuyerEmail(String buyerEmail) {
        this.buyerEmail = buyerEmail;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Long getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Long collegeId) {
        this.collegeId = collegeId;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Long getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(Long actualAmount) {
        this.actualAmount = actualAmount;
    }

    public Long getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(Long originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Long getDiscountsAmount() {
        return discountsAmount;
    }

    public void setDiscountsAmount(Long discountsAmount) {
        this.discountsAmount = discountsAmount;
    }

    public Integer getProcessType() {
        return processType;
    }

    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public String getDescriptionSupplier() {
        return descriptionSupplier;
    }

    public void setDescriptionSupplier(String descriptionSupplier) {
        this.descriptionSupplier = descriptionSupplier;
    }

    public String getDescriptionBuyer() {
        return descriptionBuyer;
    }

    public void setDescriptionBuyer(String descriptionBuyer) {
        this.descriptionBuyer = descriptionBuyer;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public String getSupplierEmail() {
        return supplierEmail;
    }

    public void setSupplierEmail(String supplierEmail) {
        this.supplierEmail = supplierEmail;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }

    public Long getCarryFee() {
        return carryFee;
    }

    public void setCarryFee(Long carryFee) {
        this.carryFee = carryFee;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "OrderBaseDTO{" +
                "id=" + id +
                ", orderNumber='" + orderNumber + '\'' +
                ", parentId=" + parentId +
                ", parentNumber='" + parentNumber + '\'' +
                ", sourceType=" + sourceType +
                ", sourceId=" + sourceId +
                ", sourceNumber='" + sourceNumber + '\'' +
                ", buyerId=" + buyerId +
                ", buyerName='" + buyerName + '\'' +
                ", buyerPhone='" + buyerPhone + '\'' +
                ", buyerEmail='" + buyerEmail + '\'' +
                ", orgId=" + orgId +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                ", departmentId=" + departmentId +
                ", departmentName='" + departmentName + '\'' +
                ", collegeId=" + collegeId +
                ", collegeName='" + collegeName + '\'' +
                ", businessType=" + businessType +
                ", orderStatus=" + orderStatus +
                ", actualAmount=" + actualAmount +
                ", originalPrice=" + originalPrice +
                ", discountsAmount=" + discountsAmount +
                ", processType=" + processType +
                ", descriptionSupplier='" + descriptionSupplier + '\'' +
                ", descriptionBuyer='" + descriptionBuyer + '\'' +
                ", supplierId=" + supplierId +
                ", supplierName='" + supplierName + '\'' +
                ", supplierCode='" + supplierCode + '\'' +
                ", supplierEmail='" + supplierEmail + '\'' +
                ", supplierPhone='" + supplierPhone + '\'' +
                ", carryFee=" + carryFee +
                ", receiverName='" + receiverName + '\'' +
                ", receiverPhone='" + receiverPhone + '\'' +
                ", receiverAddress='" + receiverAddress + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
