package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 10:50
 * @Version 1.0
 * @Desc:描述
 */
public class OrderExportDTO implements Serializable {

    private static final long serialVersionUID = 7872125848263927406L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 导出文件名称
     */
    private String fileName;

    /**
     * 导出时间
     */
    private Date exportDate;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 导出人id
     */
    private Integer userId;

    /**
     * 导出人名称
     */
    private String userName;

    /**
     * 导出状态 1导出中2导出成功3导出失败
     */
    private Integer status;

    /**
     * 文件类型 1订单明细2商品明细
     */
    private Integer fileType;

    /**
     * 文件下载地址
     */
    private String fileUrl;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Date getExportDate() {
        return exportDate;
    }

    public void setExportDate(Date exportDate) {
        this.exportDate = exportDate;
    }


    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFileType() {
        return fileType;
    }

    public void setFileType(Integer fileType) {
        this.fileType = fileType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderExportDTO{");
        sb.append("id=").append(id);
        sb.append(", fileName='").append(fileName).append('\'');
        sb.append(", exportDate=").append(exportDate);
        sb.append(", userId=").append(userId);
        sb.append(", userName='").append(userName).append('\'');
        sb.append(", status=").append(status);
        sb.append(", fileType=").append(fileType);
        sb.append(", fileUrl='").append(fileUrl).append('\'');
        sb.append(", failReason='").append(failReason).append('\'');
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append('}');
        return sb.toString();
    }
}
