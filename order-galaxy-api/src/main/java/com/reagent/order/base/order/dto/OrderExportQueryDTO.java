package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 11:15
 * @Version 1.0
 * @Desc:描述
 */
public class OrderExportQueryDTO implements Serializable {

    private static final long serialVersionUID = 21664628479544231L;

    /**
     *当前页码,不传默认为1
     */
    private Integer pageNo = 1;

    /**
     * 每页记录数，不传默认为10
     */
    private Integer pageSize = 10;


    /**
     * 导出时间起始
     */
    private Date exportDateStart;

    /**
     * 导出时间结束
     */
    private Date exportDateEnd;

    /**
     * 导出状态
     */
    private Integer status;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 导出文件类型，参考枚举com.reagent.order.base.order.enums.ExportFileTypeEnum
     */
    private List<Integer> fileTypeList;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getExportDateStart() {
        return exportDateStart;
    }

    public void setExportDateStart(Date exportDateStart) {
        this.exportDateStart = exportDateStart;
    }

    public Date getExportDateEnd() {
        return exportDateEnd;
    }

    public void setExportDateEnd(Date exportDateEnd) {
        this.exportDateEnd = exportDateEnd;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<Integer> getFileTypeList() {
        return fileTypeList;
    }

    public OrderExportQueryDTO setFileTypeList(List<Integer> fileTypeList) {
        this.fileTypeList = fileTypeList;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderExportQueryDTO{");
        sb.append("pageNo=").append(pageNo);
        sb.append(", pageSize=").append(pageSize);
        sb.append(", exportDateStart=").append(exportDateStart);
        sb.append(", exportDateEnd=").append(exportDateEnd);
        sb.append(", status=").append(status);
        sb.append(", userId=").append(userId);
        sb.append(", orgId=").append(orgId);
        sb.append(", fileTypeList=").append(fileTypeList);
        sb.append('}');
        return sb.toString();
    }
}
