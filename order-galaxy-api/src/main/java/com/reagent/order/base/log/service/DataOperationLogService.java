package com.reagent.order.base.log.service;

import com.reagent.order.base.log.dto.DataOperationLogDTO;
import com.reagent.order.base.log.request.DataOperationLogListRequest;
import com.reagent.order.base.log.request.DataOperationLogQueryRequest;
import com.ruijing.base.swagger.api.rpc.annotation.RpcApi;
import com.ruijing.base.swagger.api.rpc.annotation.RpcMethod;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/7 14:49
 * @description
 */
@RpcApi(value = "OMS订单数据修改日志服务")
public interface DataOperationLogService {

    /**
     * 插入日志
     * @param dataOperationLogDTO 修改日志数据
     * @return 是否成功
     */
    RemoteResponse<Boolean> insertLog(DataOperationLogDTO dataOperationLogDTO);

    /**
     * 批量插入日志
     * @param dataOperationLogDTOList 修改日志数据
     * @return 是否成功
     */
    RemoteResponse<Boolean> batchInsertLog(List<DataOperationLogDTO> dataOperationLogDTOList);

    /**
     * 根据参数查找（需传分页参数）
     * @param dataOperationLogListRequest 参数
     * @return 日志数据
     */
    PageableResponse<List<DataOperationLogDTO>> listByParams(DataOperationLogListRequest dataOperationLogListRequest);
    

    @RpcMethod(value = "根据批量订单号查询修正日志",notes = "orderNos单次最多支持200个")
    RemoteResponse<List<DataOperationLogDTO>> listByOrderNosAndTypes(DataOperationLogQueryRequest request);
}
