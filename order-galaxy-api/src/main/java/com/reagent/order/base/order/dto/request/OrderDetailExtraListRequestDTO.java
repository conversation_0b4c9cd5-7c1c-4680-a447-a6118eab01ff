package com.reagent.order.base.order.dto.request;

import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @description 订单详情额外信息查询接口
 * @date 2023/7/18 10:53
 */
public class OrderDetailExtraListRequestDTO implements Serializable {

    @ModelProperty("订单id集合，限制200个")
    private List<Integer> orderIdList;

    @ModelProperty("订单详情id集合，限制200个")
    private List<Integer> orderDetailIdList;

    @ModelProperty(value = "需要查询的额外信息key")
    private List<String> extraKeyList;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public List<Integer> getOrderDetailIdList() {
        return orderDetailIdList;
    }

    public void setOrderDetailIdList(List<Integer> orderDetailIdList) {
        this.orderDetailIdList = orderDetailIdList;
    }

    public List<String> getExtraKeyList() {
        return extraKeyList;
    }

    public OrderDetailExtraListRequestDTO setExtraKeyList(List<String> extraKeyList) {
        this.extraKeyList = extraKeyList;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderDetailExtraListRequestDTO.class.getSimpleName() + "[", "]")
                .add("orderIdList=" + orderIdList)
                .add("orderDetailIdList=" + orderDetailIdList)
                .add("extraKeyList=" + extraKeyList)
                .toString();
    }
}
