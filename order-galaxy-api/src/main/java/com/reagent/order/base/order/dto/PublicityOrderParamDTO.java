package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 公示订单 入参
 * @author: zhuk
 * @create: 2019-08-26 17:06
 **/
public class PublicityOrderParamDTO implements Serializable {

    private static final long serialVersionUID = 6673161201526918960L;

    /**
     * 订单状态集合
     */
    private List<Integer> statusList;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 学院名称
     */
    private String collegeName;

    /**
     * 运力类型
     */
    private String rideType;

    /**
     * 流程类型 0线上单 1线下单
     */
    private Integer processType;

    /**
     *  业务类型 对应 枚举类OrderBusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 页码
     */
    private Integer pageNumber;

    /**
     * 页面大小
     */
    private Integer pageSize;

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public String getRideType() {
        return rideType;
    }

    public void setRideType(String rideType) {
        this.rideType = rideType;
    }

    public Integer getProcessType() {
        return processType;
    }

    public void setProcessType(Integer processType) {
        this.processType = processType;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "PublicityOrderParamDTO{" +
                "statusList=" + statusList +
                ", supplierName='" + supplierName + '\'' +
                ", collegeName='" + collegeName + '\'' +
                ", rideType=" + rideType +
                ", processType=" + processType +
                ", businessType=" + businessType +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                '}';
    }
}
