package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: orderBase 及 额外查询条件对象
 * @author: zhuk
 * @create: 2019-08-27 19:35
 **/
public class OrderBaseAndExtrasDTO implements Serializable {

    private static final long serialVersionUID = 3456656812216402895L;
    /**
     * orderBaseDTO
     */
    private OrderBaseDTO orderBaseDTO;

    /**
     * 额外查询条件对象集合
     */
    private List<OrderExtraQueryDTO> extraQueryDTOList;

    public OrderBaseDTO getOrderBaseDTO() {
        return orderBaseDTO;
    }

    public void setOrderBaseDTO(OrderBaseDTO orderBaseDTO) {
        this.orderBaseDTO = orderBaseDTO;
    }

    public List<OrderExtraQueryDTO> getExtraQueryDTOList() {
        return extraQueryDTOList;
    }

    public void setExtraQueryDTOList(List<OrderExtraQueryDTO> extraQueryDTOList) {
        this.extraQueryDTOList = extraQueryDTOList;
    }

    @Override
    public String toString() {
        return "OrderBaseAndExtrasDTO{" +
                "orderBaseDTO=" + orderBaseDTO +
                ", extraQueryDTOList=" + extraQueryDTOList +
                '}';
    }
}
