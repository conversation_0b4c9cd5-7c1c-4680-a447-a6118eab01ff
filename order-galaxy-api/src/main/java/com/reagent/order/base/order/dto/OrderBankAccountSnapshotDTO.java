package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @CreateTime 2023-11-29 18:40
 * @Description
 */
public class OrderBankAccountSnapshotDTO implements Serializable {

    private static final long serialVersionUID = -7068530540304115582L;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 单位id
     */
    private Integer orgId;

    /**
     * 银行id
     */
    private Integer bankId;

    /**
     * 银行开户名
     */
    private String bankAccountName;

    /**
     * 所属银行
     */
    private String bankName;

    /**
     * 开户行名称
     */
    private String bankBranch;

    /**
     * 支行联行号
     */
    private String bankCode;

    /**
     * 省份代码
     */
    private String provinceCode;

    /**
     * 城市代码
     */
    private String cityCode;

    /**
     * 银行卡号
     */
    private String bankCardNumber;

    /**
     * 账户类型
     */
    private Integer accountType;

    public Integer getOrderId() {
        return orderId;
    }

    public OrderBankAccountSnapshotDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OrderBankAccountSnapshotDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public OrderBankAccountSnapshotDTO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public Integer getBankId() {
        return bankId;
    }

    public OrderBankAccountSnapshotDTO setBankId(Integer bankId) {
        this.bankId = bankId;
        return this;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public OrderBankAccountSnapshotDTO setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
        return this;
    }

    public String getBankName() {
        return bankName;
    }

    public OrderBankAccountSnapshotDTO setBankName(String bankName) {
        this.bankName = bankName;
        return this;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public OrderBankAccountSnapshotDTO setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
        return this;
    }

    public String getBankCode() {
        return bankCode;
    }

    public OrderBankAccountSnapshotDTO setBankCode(String bankCode) {
        this.bankCode = bankCode;
        return this;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public OrderBankAccountSnapshotDTO setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
        return this;
    }

    public String getCityCode() {
        return cityCode;
    }

    public OrderBankAccountSnapshotDTO setCityCode(String cityCode) {
        this.cityCode = cityCode;
        return this;
    }

    public String getBankCardNumber() {
        return bankCardNumber;
    }

    public OrderBankAccountSnapshotDTO setBankCardNumber(String bankCardNumber) {
        this.bankCardNumber = bankCardNumber;
        return this;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public OrderBankAccountSnapshotDTO setAccountType(Integer accountType) {
        this.accountType = accountType;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderBankAccountSnapshotDTO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("orderNo='" + orderNo + "'")
                .add("orgId=" + orgId)
                .add("bankId=" + bankId)
                .add("bankAccountName='" + bankAccountName + "'")
                .add("bankName='" + bankName + "'")
                .add("bankBranch='" + bankBranch + "'")
                .add("bankCode='" + bankCode + "'")
                .add("provinceCode='" + provinceCode + "'")
                .add("cityCode='" + cityCode + "'")
                .add("bankCardNumber='" + bankCardNumber + "'")
                .add("accountType=" + accountType)
                .toString();
    }
}
