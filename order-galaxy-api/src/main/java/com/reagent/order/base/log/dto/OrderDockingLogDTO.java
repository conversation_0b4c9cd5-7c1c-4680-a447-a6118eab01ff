package com.reagent.order.base.log.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 订单调用日志
 * @author: zhuk
 * @create: 2019-10-18 19:32
 **/
public class OrderDockingLogDTO implements Serializable {

    private static final long serialVersionUID = -8251543687548556806L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 对接的单号  主要是订单号
     */
    private String dockingNumber;

    /**
     * 调用接口的信息
     */
    private String paramInfo;

    /**
     * 接口返回信息，回调信息
     */
    private String extraInfo;

    /**
     * 操作
     */
    private String operation;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 结果
     */
    private String result;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDockingNumber() {
        return dockingNumber;
    }

    public void setDockingNumber(String dockingNumber) {
        this.dockingNumber = dockingNumber;
    }

    public String getParamInfo() {
        return paramInfo;
    }

    public void setParamInfo(String paramInfo) {
        this.paramInfo = paramInfo;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "OrderDockingLogDTO{" +
                "id=" + id +
                ", dockingNumber='" + dockingNumber + '\'' +
                ", paramInfo='" + paramInfo + '\'' +
                ", extraInfo='" + extraInfo + '\'' +
                ", operation='" + operation + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", result='" + result + '\'' +
                ", remark='" + remark + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
