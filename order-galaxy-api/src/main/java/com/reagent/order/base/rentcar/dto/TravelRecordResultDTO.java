package com.reagent.order.base.rentcar.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询出行记录 结果dto
 * @author: zhu<PERSON>
 * @create: 2019-07-29 21:03
 **/
public class TravelRecordResultDTO implements Serializable {

    private static final long serialVersionUID = -1535878047858927748L;
    /**
     * 订单ID 列表
     */
    private List<Long> orderIds;

    /**
     * 当前页码
     */
    private Integer pageNumber;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 总数量
     */
    private Long totalNum;

    public List<Long> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(List<Long> orderIds) {
        this.orderIds = orderIds;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    @Override
    public String toString() {
        return "TravelRecordDTO{" +
                "orderIds=" + orderIds +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                ", totalNum=" + totalNum +
                '}';
    }
}
