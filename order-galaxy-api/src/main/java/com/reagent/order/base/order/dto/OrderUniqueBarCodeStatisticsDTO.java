package com.reagent.order.base.order.dto;

import com.ruijing.base.swagger.api.rpc.annotation.RpcModel;
import com.ruijing.base.swagger.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

@RpcModel("商品批次码的计数模型")
public class OrderUniqueBarCodeStatisticsDTO implements Serializable {

    private static final long serialVersionUID = -527457736665519407L;

    @RpcModelProperty("detailId")
    private Integer detailId;

    @RpcModelProperty("商品名称")
    private String productName;

    @RpcModelProperty("商品品牌")
    private String brand;

    @RpcModelProperty("商品规格")
    private String spec;

    @RpcModelProperty("已录入批次的商品数")
    private int hasInputTotal;

    @RpcModelProperty("已打印的数量")
    private int printedTotal;

    @RpcModelProperty("商品购买总数")
    private int total;

    public Integer getDetailId() {
        return detailId;
    }

    public OrderUniqueBarCodeStatisticsDTO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public OrderUniqueBarCodeStatisticsDTO setProductName(String productName) {
        this.productName = productName;
        return this;
    }

    public int getHasInputTotal() {
        return hasInputTotal;
    }

    public OrderUniqueBarCodeStatisticsDTO setHasInputTotal(int hasInputTotal) {
        this.hasInputTotal = hasInputTotal;
        return this;
    }

    public int getTotal() {
        return total;
    }

    public OrderUniqueBarCodeStatisticsDTO setTotal(int total) {
        this.total = total;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public OrderUniqueBarCodeStatisticsDTO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getSpec() {
        return spec;
    }

    public OrderUniqueBarCodeStatisticsDTO setSpec(String spec) {
        this.spec = spec;
        return this;
    }

    public int getPrintedTotal() {
        return printedTotal;
    }

    public OrderUniqueBarCodeStatisticsDTO setPrintedTotal(int printedTotal) {
        this.printedTotal = printedTotal;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderUniqueBarCodeStatisticsDTO{");
        sb.append("detailId=").append(detailId);
        sb.append(", productName='").append(productName).append('\'');
        sb.append(", brand='").append(brand).append('\'');
        sb.append(", spec='").append(spec).append('\'');
        sb.append(", hasInputTotal=").append(hasInputTotal);
        sb.append(", total=").append(total);
        sb.append('}');
        return sb.toString();
    }
}
