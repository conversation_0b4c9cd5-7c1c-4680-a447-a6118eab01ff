package com.reagent.order.base.order.enums;

import java.util.ArrayList;
import java.util.List;

public enum DeliveryTypeEnum {

    NORMAL(0, "普通配送"),
    PROXY(1, "代配送"),
    ORG(2, "单位配送"),
    ;

    /**
     * code
     */
    private Integer code;

    /**
     * description
     */
    private String description;

    // 属于代配送的配送方式
    public static final List<Integer> PROXY_DELIVERY_LIST = new ArrayList<>();

    static {
        PROXY_DELIVERY_LIST.add(PROXY.getCode());
        PROXY_DELIVERY_LIST.add(ORG.getCode());
    }

    DeliveryTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
