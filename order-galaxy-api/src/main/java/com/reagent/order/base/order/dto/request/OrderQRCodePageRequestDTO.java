package com.reagent.order.base.order.dto.request;



import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 二维码分页查询入参
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-01-11 10:15
 */
@RpcModel("二维码分页查询入参")
public class OrderQRCodePageRequestDTO extends BasePageRequestDTO implements Serializable {

    private static final long serialVersionUID = 5471025100997692621L;

    @RpcModelProperty("库房id")
    private Integer roomId;

    @RpcModelProperty("供应商id")
    private Integer supplierId;

    @RpcModelProperty("商品货号")
    private String productCode;
    
    @RpcModelProperty
    private String productName;

    @Deprecated
    @RpcModelProperty("二维码状态")
    private List<Integer> status;

    @RpcModelProperty("库房状态")
    private List<Integer> inventoryStatusList;

    @RpcModelProperty("是否有效")
    private Integer valid;

    @RpcModelProperty("规格")
    private String spec;

    @RpcModelProperty("品牌")
    private String brand;

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public List<Integer> getStatus() {
        return status;
    }

    public void setStatus(List<Integer> status) {
        this.status = status;
    }

    public List<Integer> getInventoryStatusList() {
        return inventoryStatusList;
    }

    public OrderQRCodePageRequestDTO setInventoryStatusList(List<Integer> inventoryStatusList) {
        this.inventoryStatusList = inventoryStatusList;
        return this;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    @Override
    public String toString() {
        return "OrderQRCodePageRequestDTO{" +
                "roomId=" + roomId +
                ", supplierId=" + supplierId +
                ", productCode='" + productCode + '\'' +
                ", productName='" + productName + '\'' +
                ", status=" + status +
                ", inventoryStatusList=" + inventoryStatusList +
                ", valid=" + valid +
                ", spec='" + spec + '\'' +
                ", brand='" + brand + '\'' +
                '}';
    }
}
