package com.reagent.order.base.order.dto.request;

import java.io.Serializable;

/**
 * 订单详情验收文件信息 入参
 */
public class OrderDetailAcceptanceFileRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单详情id
     */
    private Integer detailId;

    /**
     * 文件路径
     */
    private String url;

    /**
     * 文件名
     */
    private String fileName;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}