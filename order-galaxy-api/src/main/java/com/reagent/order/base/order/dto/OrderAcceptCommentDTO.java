package com.reagent.order.base.order.dto;

import com.ruijing.base.swagger.api.rpc.annotation.RpcModel;
import com.ruijing.base.swagger.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/2/25 15:34
 * @Description
 **/
@RpcModel("订单管理-订单验收评价传输体")
public class OrderAcceptCommentDTO implements Serializable {

    private static final long serialVersionUID = 1032876974011231633L;

    /**
     * 订单验收评价id列表
     */
    @RpcModelProperty("订单验收评价id列表")
    private List<Integer> acceptCommentTagList;

    /**
     * 订单主表id
     */
    @RpcModelProperty("订单主表id")
    private Integer orderId;

    /**
     * 订单验收评价列表
     */
    @RpcModelProperty("订单验收评价列表")
    private List<String> acceptCommentList;

    /**
     * 单位id
     */
    @RpcModelProperty("单位id")
    private Integer orgId;

    public List<Integer> getAcceptCommentTagList() {
        return acceptCommentTagList;
    }

    public OrderAcceptCommentDTO setAcceptCommentTagList(List<Integer> acceptCommentTagList) {
        this.acceptCommentTagList = acceptCommentTagList;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public OrderAcceptCommentDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public List<String> getAcceptCommentList() {
        return acceptCommentList;
    }

    public OrderAcceptCommentDTO setAcceptCommentList(List<String> acceptCommentList) {
        this.acceptCommentList = acceptCommentList;
        return this;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public OrderAcceptCommentDTO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderAcceptCommentDTO{");
        sb.append("acceptCommentTagList=").append(acceptCommentTagList);
        sb.append(", orderId=").append(orderId);
        sb.append(", acceptCommentList=").append(acceptCommentList);
        sb.append(", orgId=").append(orgId);
        sb.append('}');
        return sb.toString();
    }
}
