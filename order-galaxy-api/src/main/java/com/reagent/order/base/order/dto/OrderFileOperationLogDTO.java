package com.reagent.order.base.order.dto;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单文件操作日志DTO
 */
@Model("订单文件操作日志DTO")
public class OrderFileOperationLogDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @ModelProperty("主键ID")
    private Integer id;
    
    /**
     * 订单id
     */
    @ModelProperty("订单id")
    private Integer orderId;
    
    /**
     * 订单日志主表ID（t_order_approval_log）
     */
    @ModelProperty("订单日志主表ID")
    private Integer logId;
    
    /**
     * 文件路径
     */
    @ModelProperty("文件路径")
    private String url;
    
    /**
     * 文件名
     */
    @ModelProperty("文件名")
    private String fileName;
    
    /**
     * 创建时间
     */
    @ModelProperty("创建时间")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @ModelProperty("更新时间")
    private Date updateTime;
    
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getOrderId() {
        return orderId;
    }
    
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }
    
    public Integer getLogId() {
        return logId;
    }
    
    public void setLogId(Integer logId) {
        this.logId = logId;
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "OrderFileOperationLogDTO{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", logId=" + logId +
                ", url='" + url + '\'' +
                ", fileName='" + fileName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
