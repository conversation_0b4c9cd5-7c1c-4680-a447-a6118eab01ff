package com.reagent.order.base.order.dto.request;

import java.io.Serializable;

/**
 * @description: 基础分页类
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-01-11 10:34
 */
public class BasePageRequestDTO implements Serializable {

    private static final long serialVersionUID = 5634938436685736373L;

    private Integer pageNo = 1;

    private Integer pageSize = 20;

    public BasePageRequestDTO() {
    }

    public Integer getPageNo() {
        return this.pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder("BasePageParamDTO{");
        sb.append("pageNo=").append(this.pageNo);
        sb.append(", pageSize=").append(this.pageSize);
        sb.append('}');
        return sb.toString();
    }
}
