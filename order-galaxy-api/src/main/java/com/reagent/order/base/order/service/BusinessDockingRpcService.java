package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * 第三方业务订单rpc接口
 * <AUTHOR>
 * @Date 2020/8/18 5:52 下午
 */
public interface BusinessDockingRpcService {

    /**
     * 通过业务单号查询业务订单
     * @param request
     * @return
     */
    RemoteResponse<List<BusinessDockingDTO>> findByBusinessNo(OrderBaseParamDTO request);

    /**
     * 更新第三方业务单记录
     * @param request
     * @return
     */
    RemoteResponse<Integer> updateByBusinessNo(BusinessDockingDTO request);

    /**
     * 保存第三方对接单记录
     * @param request
     * @return
     */
    RemoteResponse<Integer> saveBusinessOrders(List<BusinessDockingDTO> request);

    /**
     * 查询第三方对接单记录
     * @param request
     * @return
     */
    RemoteResponse<List<BusinessDockingDTO>> findByOrgCodeAndStatus(OrderBaseParamDTO request);

    /**
     * 更新第三方对接单记录
     * @param request
     * @return
     */
    RemoteResponse<Integer> updateBatchByDockingNoList(List<BusinessDockingDTO> request);
}
