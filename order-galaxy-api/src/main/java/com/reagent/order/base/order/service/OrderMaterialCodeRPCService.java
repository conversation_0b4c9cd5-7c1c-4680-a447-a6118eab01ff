package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderMaterialCodeDTO;
import com.reagent.order.base.order.dto.request.OrderMaterialCodeRequest;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * Name: OrderMaterialCodeRPCService
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/3/11
 */
public interface OrderMaterialCodeRPCService {

    /**
     * 新增物资编码
     * @param orderMaterialCodeDTOList      新增入参
     * @return                              新增结果
     */
    RemoteResponse<Integer> insert(List<OrderMaterialCodeDTO> orderMaterialCodeDTOList);


    /**
     * 通过 品牌+货号+规格 查询商品编码
     * @param request               查询入参
     * @return                      查询结果
     */
    RemoteResponse<List<OrderMaterialCodeDTO>> queryByParam(List<OrderMaterialCodeRequest> request);


    /**
     * 更新订单物资编码信息
     * @param orderMaterialCodeDTO  修改入参
     * @return                      修改结果
     */
    RemoteResponse<Integer> update(OrderMaterialCodeDTO orderMaterialCodeDTO);


}
