package com.reagent.order.base.log.service;

import com.reagent.order.base.log.dto.OrderLogParamDTO;
import com.reagent.order.base.log.dto.OrderOperationLogDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * @description: 订单日志rpc 服务
 * @author: zhuk
 * @create: 2019-07-31 21:03
 **/
public interface OrderOperationLogRpcService {

    /**
     * 根据 订单 id 查询订单日志
     * @param orderLogParamDTO 入参
     * @return  List<OrderOperationLogDTO>
     */
    RemoteResponse<List<OrderOperationLogDTO>> findOrderOperationLogByOrderId(OrderLogParamDTO orderLogParamDTO);

    /**
     * 增加orderOperationLog日志
     * @param orderOperationLogDTO 入参
     * @return
     */
    RemoteResponse createOrderOperationLog(OrderOperationLogDTO orderOperationLogDTO);
}
