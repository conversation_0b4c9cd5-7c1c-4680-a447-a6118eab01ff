package com.reagent.order.base.log.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

/**
 *
 * <AUTHOR>
 * @date 2022/12/7 14:22
 * @description
 */
@RpcModel("OMS数据操作日志DTO")
public class DataOperationLogDTO implements Serializable {

    private static final long serialVersionUID = 5694747198603092216L;

    /**
     * 主键
     */
    @RpcModelProperty("主键")
    private Integer id;

    /**
    * 订单单号
    */
    @RpcModelProperty("订单号")
    private String orderNo;

    /**
    * 数据修正理由
    */
    @RpcModelProperty("数据修正理由")
    private String fixReason;

    /**
    * 操作内容
    */
    @RpcModelProperty("操作内容")
    private String operation;

    /**
    * 操作人guid
    */
    @RpcModelProperty("操作人guid")
    private String operatorGuid;

    /**
    * 操作人名字
    */
    @RpcModelProperty("操作人名字")
    private String operatorName;

    /**
    * 单位id
    */
    @RpcModelProperty("单位id")
    private Integer orgId;

    /**
    * 单位名称
    */
    @RpcModelProperty("单位名称")
    private String orgName;

    /**
    * 操作类型
    */
    @RpcModelProperty("操作类型")
    private Integer operationType;

    /**
     * 修改前状态
     */
    @Deprecated
    @RpcModelProperty("修改前状态")
    private Integer prevStatus;

    /**
     * 修改前的值
     */
    @RpcModelProperty("修改前的描述")
    private String preDesc;

    /**
    * 审批编号
    */
    @RpcModelProperty("审批编号")
    private String approveNumber;

    /**
    * 操作时间
    */
    @RpcModelProperty("操作时间")
    private Date operateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getFixReason() {
        return fixReason;
    }

    public void setFixReason(String fixReason) {
        this.fixReason = fixReason;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getPrevStatus() {
        return prevStatus;
    }

    public void setPrevStatus(Integer prevStatus) {
        this.prevStatus = prevStatus;
    }

    public String getApproveNumber() {
        return approveNumber;
    }

    public void setApproveNumber(String approveNumber) {
        this.approveNumber = approveNumber;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getPreDesc() {
        return preDesc;
    }

    public DataOperationLogDTO setPreDesc(String preDesc) {
        this.preDesc = preDesc;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DataOperationLogDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("orderNo='" + orderNo + "'")
                .add("fixReason='" + fixReason + "'")
                .add("operation='" + operation + "'")
                .add("operatorGuid='" + operatorGuid + "'")
                .add("operatorName='" + operatorName + "'")
                .add("orgId=" + orgId)
                .add("orgName='" + orgName + "'")
                .add("operationType=" + operationType)
                .add("prevStatus=" + prevStatus)
                .add("preDesc='" + preDesc + "'")
                .add("approveNumber='" + approveNumber + "'")
                .add("operateTime=" + operateTime)
                .toString();
    }
}