package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * 订单经费卡关联服务
 * <AUTHOR>
 */
public interface OrderFundCardCacheRpcService {
    /**
     * 保存 新订单关联的经费卡
     * @param orderFundCardList
     * @return
     */
    RemoteResponse saveOrderFundCard(List<OrderFundCardDTO> orderFundCardList);

    /**
     * 移除经费卡关联记录
     * @param orderIdList
     * @return
     */
    RemoteResponse removeOrderFundCard(List<Integer> orderIdList);

    /**
     * 通过订单id获取经费卡换卡缓存, 按照序列倒序排序
     * @param orderIdList
     * @return
     */
    RemoteResponse<List<OrderFundCardDTO>> findByOrderIdDesc(List<Integer> orderIdList);
}
