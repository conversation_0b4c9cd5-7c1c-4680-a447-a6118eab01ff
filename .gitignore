# Created by .ignore support plugin (hsz.mobi)
### Example user template template
### Example user template

# IntelliJ project files
.idea
*.iml
out
gen

HELP.md
target/
# !.mvn/wrapper/maven-wrapper.jar
# !**/src/main/**/target/
# !**/src/test/**/target/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
# !**/src/main/**/build/
# !**/src/test/**/build/

### VS Code ###
.vscode/

### manually added 20200810 ###
.mvn/
/src/*
.gitattributes
mvnw
mvnw.cmd
/store-expert-server/.gitignore
/store-expert-api/.gitignore
*.bak


### copy from store bid service ###
/build/
/bin/
/.DS_Store
/target/
/src/main/resources/rebel.xml
/store.iml
/doc/~$*

# idea file
*.iml

# idea dir
.idea/

# eclipse file
.project
.classpath

#eclipse dir
/.settings/

#maven dir
target/

/.project
/.settings/org.eclipse.wst.common.component

logs/*



