<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>order-galaxy-service</artifactId>
        <groupId>com.reagent.order</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>order-galaxy-server</artifactId>
    <packaging>jar</packaging>

    <properties>
        <commons-lang3.version>3.5</commons-lang3.version>
        <commons-beanutils.version>1.9.3</commons-beanutils.version>
    </properties>

    <dependencies>
        <!--        上传文件依赖-->
        <dependency>
            <artifactId>msharp-upload-client</artifactId>
            <groupId>com.ruijing.fundamental</groupId>
            <version>${msharp.version}</version>
        </dependency>
        <!--order-galaxy-api-->
        <dependency>
            <groupId>com.reagent.order</groupId>
            <artifactId>order-galaxy-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.order</groupId>
            <artifactId>order-base-client</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>

        <!--锐竞springboot 基础框架-->
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>inf-bom-spring-boot-starter</artifactId>
            <version>${msharp.version}</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-order-api</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>1.10.19</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba.testable</groupId>
            <artifactId>testable-all</artifactId>
            <version>0.6.6</version>
            <scope>test</scope>
        </dependency>

        <!--用户中心-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-user-api</artifactId>
            <version>1.0.25-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-sequence-api</artifactId>
            <version>${msharp.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>store-wms-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-api</artifactId>
            <version>${msharp.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-api-annotation</artifactId>
            <version>${msharp.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-api-system</artifactId>
            <version>${msharp.version}</version>
        </dependency>



        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <!--<plugin>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    &lt;!&ndash;如果想在没有web.xml文件的情况下构建WAR，请设置为false&ndash;&gt;
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    &lt;!&ndash;设置war包的名字&ndash;&gt;
                    <warName>order-galaxy-service</warName>
                </configuration>
            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                  <executions>
                      <execution>
                          <goals>
                              <goal>repackage</goal>
                          </goals>
                      </execution>
                  </executions>
            </plugin>

        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>