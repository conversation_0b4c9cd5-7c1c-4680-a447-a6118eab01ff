create table rentcar_order
(
    id                  bigint unsigned auto_increment comment '主键'
        primary key,
    order_id            bigint       default -1                    not null comment '订单id',
    order_number        varchar(30) default ''                    not null comment '订单编号',
    buyer_id            bigint       default -1                    not null comment '采购人id',
    department_id       bigint       default -1                    not null comment '课题组id',
    car_platform        tinyint      default -1                    not null comment '车辆平台  曹操/神舟',
    origin_lng          varchar(10)  default ''                    not null comment '出发地经度',
    origin_lat          varchar(10)  default ''                    not null comment '出发地维度',
    origin_address      varchar(150) default ''                    not null comment '出发地地址',
    origin_name         varchar(150) default ''                    not null comment '出发地名称',
    destination_lng     varchar(10)  default ''                    not null comment '目的地经度',
    destination_lat     varchar(10)  default ''                    not null comment '目的地纬度',
    destination_address varchar(150) default ''                    not null comment '目的地地址',
    destination_name    varchar(150) default ''                    not null comment '目的地名称',
    ride_type           tinyint      default -1                    not null comment '运力类型：2 新能源；3 舒适型；4 豪华型；5 商务型',
    product_type        tinyint      default -1                    not null comment '用车类型（0-网约车/1-出租车）',
    reserve_type        tinyint      default -1                    not null comment '预订类型 1预约、2实时',
    total_time          bigint       default 0                     not null comment '用车总时长',
    total_distance      varchar(10)  default ''                    not null comment '总里程',
    departure_time      timestamp    default CURRENT_TIMESTAMP     not null comment '出发时间',
    license_plate       varchar(10)  default ''                    not null comment '车牌号',
    car_detail          varchar(200) default ''                    not null comment '车辆型号详情',
    driver_name         varchar(50)  default ''                    not null comment '司机姓名',
    forecast_amount     bigint       default 0                     not null comment '预估金额',
    actual_amount       bigint       default 0                     not null comment '实付金额',
    discounts_amount    bigint       default 0                     not null comment '优惠金额',
    travel_type         tinyint      default -1                    not null comment '出行类型（0-差旅/1-会议/2-培训/3-其他交通）',
    travel_purpose      varchar(200) default ''                    not null comment '出行目的',
    create_time         timestamp    default CURRENT_TIMESTAMP     not null comment '生成时间',
    update_time         timestamp    default CURRENT_TIMESTAMP     not null on update CURRENT_TIMESTAMP comment '更新时间',
        constraint uniq_ordernumber
        unique (order_number),
    constraint uniq_orderid
        unique (order_id)
)
    charset = utf8;

