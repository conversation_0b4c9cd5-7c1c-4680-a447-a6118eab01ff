create table order_operation_log
(
    id                   bigint unsigned auto_increment comment '主键'
        primary key,
    order_id             bigint       default -1                not null comment ' 订单id ',
    order_number         varchar(30)  default ''                not null comment ' 订单号 ',
    business_type        int                                    not null comment ' 业务类型 枚举 ',
    operation            int                                    not null comment ' 操作类型，生成/供应商确认订单/取消订单  枚举 ',
    operation_note       varchar(255)                           not null comment ' 操作备注取消原因，拒绝取消原因，验收图片，退货原因。 ',
    operator_id          bigint                                 not null comment ' 操作人id ',
    operator_type        int                                    not null comment ' 操作人类型。采购人，供应商，系统 ',
    operator_name        varchar(100) default ''                not null comment ' 操作人名称 ',
    operator_depart_id   bigint                                 not null comment ' 操作人课题组id ',
    operator_depart_name varchar(100) default ''                not null comment ' 操作人课题组名称 ',
    operator_org_id      bigint                                 not null comment ' 操作人orgid ',
    operator_org_code    varchar(250) default ''                not null comment ' 操作人orgCode ',
    operator_org_name    varchar(100) default ''                not null comment ' 操作人org名称 ',
    create_time          timestamp    default CURRENT_TIMESTAMP not null comment ' 生成时间 ',
    update_time          timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment ' 更新时间 '
)
    charset = utf8;

create index idx_orderid
    on order_operation_log (order_id);


