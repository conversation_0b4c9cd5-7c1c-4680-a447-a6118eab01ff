-- auto-generated definition
create table order_base
(
    id                   bigint unsigned auto_increment comment '订单id'
        primary key,
    order_number         varchar(30)  default ''                not null comment '订单号',
    parent_id            bigint       default -1                not null comment '父订单id',
    parent_Number        varchar(30)  default ''                not null comment '订单父 订单号',
    source_type          int          default -1                not null comment '来源类型,  对应类型枚举',
    source_id            bigint       default -1                not null comment '来源 id',
    source_number        varchar(30)  default ''                not null comment '来源单号',
    buyer_id             bigint       default -1                not null comment '采购人id',
    buyer_name           varchar(100) default ''                not null comment '采购人姓名',
    buyer_phone          varchar(20)  default ''                not null comment '采购人电话',
    buyer_email          varchar(50)  default ''                not null comment '采购人邮箱',
    org_id               bigint       default -1                not null comment '组织id',
    org_code             varchar(250) default ''                not null comment '组织代码',
    org_name             varchar(100) default ''                not null comment '组织名称',
    department_id        bigint       default -1                not null comment '课题组id',
    department_name      varchar(100) default ''                not null comment '课题组名称',
    business_type        int          default -1                not null comment '业务类型  网约车、store',
    order_status         int          default -1                not null comment '订单状态  枚举',
    actual_amount        bigint       default 0                 not null comment '订单成交总价  单位 分',
    original_price       bigint       default 0                 not null comment '订单原总价价',
    discounts_amount     bigint       default 0                 not null comment '订单优惠总金额',
    process_type         int          default 0                 not null comment '流程类型  0：线上单  1：线下单',
    description_supplier varchar(255) default ''                not null comment '供应商订单描述',
    description_buyer    varchar(255) default ''                not null comment '采购人订单描述',
    supplier_id          bigint       default -1                not null comment '供应商id',
    supplier_name        varchar(100) default ''                not null comment '供应商名称',
    supplier_code        varchar(100) default ''                not null comment '供应商code',
    supplier_email       varchar(50)  default ''                not null comment '供应商邮箱',
    supplier_phone       varchar(20)  default ''                not null comment '供应商联系电话',
    carry_fee            bigint       default 0                 not null comment '运费 单位 分',
    receiver_name        varchar(100) default ''                not null comment '收货人名称',
    receiver_phone       varchar(50)  default ''                not null comment '收货人联系方式',
    receiver_address     varchar(200) default ''                not null comment '收货地址',
    create_time          timestamp    default CURRENT_TIMESTAMP not null comment '生成时间',
    update_time          timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uniq_ordernumber
        unique (order_number)
)
    charset = utf8;

create index idx_buyerid_departmentid_orderstatus
    on order_base (buyer_id, department_id, order_status);

create index idx_createtime
    on order_base (create_time);

create index idx_supplierid_orderstatus
    on order_base (supplier_id, order_status);

