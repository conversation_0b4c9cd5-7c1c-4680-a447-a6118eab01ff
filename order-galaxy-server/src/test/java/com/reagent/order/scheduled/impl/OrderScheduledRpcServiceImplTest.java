package com.reagent.order.scheduled.impl;

import com.reagent.order.OrderServerApplicationTests;
import com.reagent.order.base.scheduled.OrderScheduledRpcService;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2020/11/11 0011 15:54
 * @Version 1.0
 * @Desc:描述
 */
public class OrderScheduledRpcServiceImplTest extends OrderServerApplicationTests {

    @Resource
    private OrderScheduledRpcService orderScheduledRpcService;

    @Test
    public void testClearOverdueExportInfo() {
        orderScheduledRpcService.clearOverdueExportInfo();
    }

    @Test
    public void testLocalDateTime() {
        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime newLocalDate = localDateTime.minusDays(60);
        LocalDate localDate = newLocalDate.toLocalDate();
        System.out.println(localDate);

    }
}
