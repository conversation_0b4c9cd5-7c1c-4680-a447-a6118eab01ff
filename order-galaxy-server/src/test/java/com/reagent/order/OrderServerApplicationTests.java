package com.reagent.order;

import com.reagent.order.utils.IdGeneratorUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@ImportResource(locations={"classpath:/spring/*.xml"})
public class OrderServerApplicationTests {

    @Test
    //@Ignore
    public void contextLoads() {
        List<Long> barCodeList = IdGeneratorUtils.getBarCodeList(990);
        for (Long barCode : barCodeList) {
            System.out.println(barCode);
        }
    }

}

