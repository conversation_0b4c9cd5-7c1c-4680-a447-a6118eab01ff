package com.reagent.order.base.order.service.impl;

import com.reagent.order.MockBaseTestCase;
import com.reagent.order.base.order.dto.OrderDetailBatchesDTO;
import com.reagent.order.base.order.mapper.OrderDetailBatchesDOMapper;
import com.reagent.order.base.order.model.OrderDetailBatchesDO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.List;

public class OrderDetailBaseServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderDetailBaseServiceImpl orderDetailBaseService;

    @Mock
    private OrderDetailBatchesDOMapper orderDetailBatchesDOMapper;

    @Test
    public void saveOrderDetailBatches() {
        Mockito.when(orderDetailBatchesDOMapper.updateByDetailId(Mockito.any())).thenReturn(1);
        Mockito.when(orderDetailBatchesDOMapper.insertList(Mockito.any())).thenReturn(1);
        OrderDetailBatchesDO orderDetailBatchesDO = new OrderDetailBatchesDO();
        orderDetailBatchesDO.setDetailId(1);
        Mockito.when(orderDetailBatchesDOMapper.findByDetailIdIn(Mockito.any())).thenReturn(Arrays.asList(orderDetailBatchesDO));
        OrderDetailBatchesDTO item1 = new OrderDetailBatchesDTO();
        item1.setDetailId(1);

        OrderDetailBatchesDTO item2 = new OrderDetailBatchesDTO();
        item1.setDetailId(2);
        int i = orderDetailBaseService.saveOrderDetailBatches(Arrays.asList(item1, item2));
        Assert.assertTrue(i != 0);

        Mockito.when(orderDetailBatchesDOMapper.findByDetailIdIn(Mockito.any())).thenReturn(Arrays.asList());
        i = orderDetailBaseService.saveOrderDetailBatches(Arrays.asList(item1, item2));
        Assert.assertTrue(i != 0);
    }

    @Test
    public void findByDetailIdIn() {
        OrderDetailBatchesDO orderDetailBatchesDO = new OrderDetailBatchesDO();
        Mockito.when(orderDetailBatchesDOMapper.findByDetailIdIn(Mockito.any())).thenReturn(Arrays.asList(orderDetailBatchesDO));
        List<OrderDetailBatchesDTO> result = orderDetailBaseService.findByDetailIdIn(Arrays.asList(1));
        Assert.assertTrue(result.size() != 0);
    }
}