package com.reagent.order.base.order.mapper;

import com.reagent.order.OrderServerApplicationTests;
import com.reagent.order.base.order.model.OrderBase;
import com.ruijing.fundamental.common.util.JsonUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class OrderBaseMapperTest extends OrderServerApplicationTests {

    @Resource
    private OrderBaseMapper orderbaseMapper;

    @Test
    public void findRentcarOrderForBuyer() {
        List<OrderBase> 任 = orderbaseMapper.findRentcarOrderForBuyer(null, null, 1, null, null, "任间");
        System.out.println(JsonUtils.toJson(任));
    }
}