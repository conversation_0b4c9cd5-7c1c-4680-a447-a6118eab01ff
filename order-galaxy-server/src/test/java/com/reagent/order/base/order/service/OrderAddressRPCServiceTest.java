package com.reagent.order.base.order.service;

import com.reagent.order.MockBaseTestCase;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.mapper.OrderAddressDOMapper;
import com.reagent.order.base.order.model.OrderAddressDO;
import com.reagent.order.rpc.client.OrderMasterRPCClient;
import com.reagent.order.rpc.orderaddress.service.impl.OrderAddressRPCServiceImpl;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.order.cache.RedisClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


public class OrderAddressRPCServiceTest extends MockBaseTestCase {

    @InjectMocks
    OrderAddressRPCServiceImpl orderAddressRPCService;

    @Mock
    OrderAddressDOMapper orderAddressDOMapper;

    @Mock
    OrderMasterRPCClient orderMasterRPCClient;

    @Mock
    RedisClient redisClient;

    @Test
    public void insertList() {
        Mockito.when(orderAddressDOMapper.insertList(Mockito.any())).thenReturn(1);
        Mockito.when(orderAddressDOMapper.findByOrderNoIn(Mockito.any())).thenReturn(new ArrayList<>());
        Mockito.doNothing().when(redisClient).getLock(Mockito.anyString(), Mockito.anyInt());
        Mockito.doNothing().when(redisClient).unlock(Mockito.anyString());
        OrderAddressDTO orderAddressDTO = new OrderAddressDTO();
        orderAddressDTO.setId(1);
        RemoteResponse<Integer> response = orderAddressRPCService.insertList(Arrays.asList(orderAddressDTO));
        Assert.assertTrue(response.getData() > 0);
    }

    @Test
    public void listByOrderId() {
        OrderAddressDO o = new OrderAddressDO();
        o.setId(1);
        o.setProvince("广东省");
        o.setProvinceProxy("东广省");
        Mockito.when(orderAddressDOMapper.findByIdIn(Mockito.any())).thenReturn(Arrays.asList(o));

        OrderBaseParamDTO request = new OrderBaseParamDTO();
        request.setOrderIdList(Arrays.asList(1L));
        RemoteResponse<List<OrderAddressDTO>> response = orderAddressRPCService.listByOrderId(request);
        Assert.assertTrue(response.getData().size() > 0);
    }

    @Test
    public void updateByOrderId() {
        Mockito.when(orderAddressDOMapper.updateById(Mockito.any())).thenReturn(1);
        Mockito.when(orderAddressDOMapper.countById(Mockito.any())).thenReturn(1);
        Mockito.when(orderMasterRPCClient.updateById(Mockito.any())).thenReturn(1);

        OrderAddressDTO request = new OrderAddressDTO();
        request.setId(1);
        request.setOrderNo("test");
        request.setCity("广州市");
        request.setDeliveryType(1);
        RemoteResponse<Boolean> response = orderAddressRPCService.saveAddress(request);
        Assert.assertTrue(response.getData());
    }
}