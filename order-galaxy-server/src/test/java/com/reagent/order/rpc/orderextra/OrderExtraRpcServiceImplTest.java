package com.reagent.order.rpc.orderextra;

import com.reagent.order.MockBaseTestCase;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.mapper.OrderExtraMapper;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

public class OrderExtraRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderExtraRpcServiceImpl orderExtraRpcService;

    @Mock
    private OrderExtraMapper orderExtraMapper;

    @Test
    public void insertList() {
        Mockito.when(orderExtraMapper.insertList(Mockito.anyList())).thenReturn(1);
        BaseOrderExtraDTO baseOrderExtraDTO = new BaseOrderExtraDTO();
        RemoteResponse<Integer> response = orderExtraRpcService.insertList(New.list(baseOrderExtraDTO));
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void selectByOrderIdList() {
        Mockito.when(orderExtraMapper.selectByOrderIdIn(Mockito.anyList())).thenReturn(New.list());
        RemoteResponse<List<BaseOrderExtraDTO>> response = orderExtraRpcService.selectByOrderIdList(New.list(123));
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void selectByOrderNoList() {
        Mockito.when(orderExtraMapper.selectByOrderNoIn(Mockito.anyList())).thenReturn(New.list());
        RemoteResponse<List<BaseOrderExtraDTO>> response = orderExtraRpcService.selectByOrderNoList(New.list("orderno"));
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void selectByOrderIdAndExtraValue() {
        BaseOrderExtraDTO query = new BaseOrderExtraDTO();
        query.setOrderId(123);
        Mockito.when(orderExtraMapper.selectByOrderIdAndExtraKey(Mockito.anyInt(), Mockito.anyInt())).thenReturn(New.list());
        RemoteResponse<List<BaseOrderExtraDTO>> response = orderExtraRpcService.selectByOrderIdAndExtraKey(query);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void selectByOrderIdInAndExtraKey() {
        Mockito.when(orderExtraMapper.selectByOrderIdInAndExtraKey(Mockito.anyCollection(), Mockito.anyInt())).thenReturn(New.list());
        RemoteResponse<List<BaseOrderExtraDTO>> response = orderExtraRpcService.selectByOrderIdInAndExtraKey(New.list(123), 321);
        Assert.assertTrue(response.isSuccess());
    }
}