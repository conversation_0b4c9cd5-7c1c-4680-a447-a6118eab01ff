package com.reagent.order.rpc.acceptcomment;

import com.reagent.order.OrderServerApplicationTests;
import com.reagent.order.base.order.dto.OrderAcceptCommentDTO;
import com.reagent.order.base.order.dto.OrderAcceptQueryDTO;
import com.reagent.order.base.order.service.OrderAcceptCommentRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import org.junit.Test;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/2/26 10:48
 * @Description
 **/
public class OrderAcceptCommentTest extends OrderServerApplicationTests {

    @Resource
    private OrderAcceptCommentRpcService orderAcceptCommentRpcService;

    @Test
    public void testSaveComment() {
        OrderAcceptCommentDTO orderAcceptCommentDTO = new OrderAcceptCommentDTO();
        orderAcceptCommentDTO.setOrgId(60);
        orderAcceptCommentDTO.setOrderId(456789);
        orderAcceptCommentDTO.setAcceptCommentTagList(New.list(1,2,3));
        RemoteResponse<Boolean> response = orderAcceptCommentRpcService.saveOrderComment(orderAcceptCommentDTO);
        Assert.isTrue(response != null && response.isSuccess() && response.getData().equals(true), "response failed");
    }

    @Test
    public void testGetComment() {
        Map<Integer, String> orgCommentMap = orderAcceptCommentRpcService.ORG_COMMENT.get(107);
        OrderAcceptQueryDTO orderAcceptQueryDTO = new OrderAcceptQueryDTO();
        orderAcceptQueryDTO.setOrgId(60);
        orderAcceptQueryDTO.setOrderIdList(New.list(176238,176419));
        RemoteResponse<List<OrderAcceptCommentDTO>> orderCommentResponse = orderAcceptCommentRpcService.getOrderComment(orderAcceptQueryDTO);
        Assert.isTrue(orderCommentResponse != null && orderCommentResponse.isSuccess() && orderCommentResponse.getData() != null, "get comment failed");

        Assert.isTrue(orderCommentResponse.getData().size() == 2, "comment item size not match");
        for (OrderAcceptCommentDTO comment : orderCommentResponse.getData()) {
            for (Integer commentId : comment.getAcceptCommentTagList()) {
                Assert.isTrue(orgCommentMap.get(commentId) != null, "this commentId" + commentId + "do not have corresponding value");
            }
        }
    }
}
