package com.reagent.order.rpc.order.detail.batches.impl;

import com.reagent.order.MockBaseTestCase;
import com.reagent.order.base.order.dto.OrderDetailBatchesDTO;
import com.reagent.order.base.order.dto.OrderDetailBatchesRequestDTO;
import com.reagent.order.base.order.service.OrderDetailBaseService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

public class OrderDetailBatchesRPCServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderDetailBatchesRPCServiceImpl orderDetailBatchesRPCService;

    @Mock
    private OrderDetailBaseService orderDetailBaseService;

    @Test
    public void saveOrderDetailBatches() {
        Mockito.when(orderDetailBaseService.saveOrderDetailBatches(Mockito.any())).thenReturn(1);
        OrderDetailBatchesDTO item = new OrderDetailBatchesDTO();
        item.setDetailId(1);
        item.setBatches("sss");
        RemoteResponse<Integer> response = orderDetailBatchesRPCService.saveOrderDetailBatches(Arrays.asList(item));
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void findByDetailIdIn() {
        OrderDetailBatchesDTO r = new OrderDetailBatchesDTO();
        Mockito.when(orderDetailBaseService.findByDetailIdIn(Mockito.any())).thenReturn(Arrays.asList(r));

        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setDetailIdList(Arrays.asList(1));
        RemoteResponse<List<OrderDetailBatchesDTO>> response = orderDetailBatchesRPCService.findByDetailIdIn(request);
        Assert.assertTrue(response.isSuccess());

    }
}