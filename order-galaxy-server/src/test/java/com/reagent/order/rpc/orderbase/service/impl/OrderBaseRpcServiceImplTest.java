package com.reagent.order.rpc.orderbase.service.impl;

import com.reagent.order.OrderServerApplicationTests;
import com.reagent.order.base.enums.OrderBusinessTypeEnum;
import com.reagent.order.base.enums.RentcarOrderStatusEnum;
import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.order.base.order.dto.OrderBasePageResultDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.service.BusinessDockingRpcService;
import com.reagent.order.base.order.service.OrderBaseRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class OrderBaseRpcServiceImplTest extends OrderServerApplicationTests {

    @Autowired
    private OrderBaseRpcService orderBaseRpcService;

    @Autowired
    private BusinessDockingRpcService businessDockingRpcService;

    @Test
    public void getTimeOutOrder() {
        OrderBaseParamDTO orderBaseParamDTO = new OrderBaseParamDTO();
        orderBaseParamDTO.setOrderStatusList(New.list(RentcarOrderStatusEnum.PAYING_ORDER.statusValue,
                RentcarOrderStatusEnum.WAITING_APPROVAL.statusValue,RentcarOrderStatusEnum.REJECTED_APPROVAL.statusValue));
        orderBaseParamDTO.setBusinessType(OrderBusinessTypeEnum.RENTCAR.typeValue);
        int pageSize = 10;
        int pageNumber = 1;
        orderBaseParamDTO.setTimeOutDay(3);
        orderBaseParamDTO.setPageSize(pageNumber);
        orderBaseParamDTO.setPageNumber(pageSize);
        RemoteResponse<OrderBasePageResultDTO> timeOutOrder = orderBaseRpcService.getTimeOutOrder(orderBaseParamDTO);
        OrderBasePageResultDTO data = timeOutOrder.getData();
        Long totalNum = data.getTotalNum();
        if (data.getTotalNum() >pageSize) {
            long forTime = totalNum / pageSize;
            for (int i = 0;i<forTime;i++){
                orderBaseParamDTO.setPageSize(++pageNumber);
                timeOutOrder = orderBaseRpcService.getTimeOutOrder(orderBaseParamDTO);
                System.out.println(JsonUtils.toJson(timeOutOrder));
            }
        }
    }

    @Test
    public void getBusinessOrderNo() {
        OrderBaseParamDTO request = new OrderBaseParamDTO();
        request.setOrderNumberList(Arrays.asList("ddfff123"));
        RemoteResponse<List<BusinessDockingDTO>> businessOrder = businessDockingRpcService.findByBusinessNo(request);
        Preconditions.notNull(businessOrder.getData(), "查无业务订单！");
    }

    @Test
    public void updateByBusinessNoTest() {
        BusinessDockingDTO request = new BusinessDockingDTO();
        request.setBusinessOrderNo("DC202008311494901");
        request.setExtraJson("[{\"field\":\"fundCardCode\",\"value\":\"111\"},{\"field\":\"fundCardManagerName\",\"value\":null},{\"field\":\"receiverName\",\"value\":\"锐竞测试\"},{\"field\":\"approverName\",\"value\":\"锐竞测试\"}]");
        request.setReagentStatus(6);
        businessDockingRpcService.updateByBusinessNo(request);
    }

    @Test
    public void saveBusinessOrdersTest() {
        List<BusinessDockingDTO> request = new ArrayList<>();
        BusinessDockingDTO item = new BusinessDockingDTO();
        item.setBusinessOrderNo("test1");
        item.setDockingNo("other1");
        request.add(item);

        item = new BusinessDockingDTO();
        item.setBusinessOrderNo("test2");
        item.setDockingNo("other2");
        request.add(item);

        businessDockingRpcService.saveBusinessOrders(request);
    }

    @Test
    public void updateBatchTest() {
        List<BusinessDockingDTO> request = new ArrayList<>();
        BusinessDockingDTO item = new BusinessDockingDTO();
        item.setBusinessOrderNo("test1");
        item.setReagentStatus(3);
        item.setExtraJson("test json1");
        request.add(item);

        item = new BusinessDockingDTO();
        item.setBusinessOrderNo("test2");
        item.setReagentStatus(3);
        item.setExtraJson("test json2");
        request.add(item);
        businessDockingRpcService.updateBatchByDockingNoList(request);
    }

    @Test
    public void findByOrgCodeAndStatusTest() {
        OrderBaseParamDTO request = new OrderBaseParamDTO();
        request.setOrderStatusList(Arrays.asList(6));
        request.setOrgCode("HUA_NAN_NONG_YE_DA_XUE");
        RemoteResponse<List<BusinessDockingDTO>> result = businessDockingRpcService.findByOrgCodeAndStatus(request);
        System.out.println(result);
    }
}