package com.reagent.order.rpc.order.detail.batches.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.model.MockScope;
import com.reagent.order.MockBaseTestCase;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.dto.OrderDetailBatchesRequestDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeStatisticsDTO;
import com.reagent.order.base.order.service.OrderUniqueBarCodeService;
import com.reagent.order.rpc.client.*;
import com.reagent.order.utils.IdGeneratorUtils;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.cache.RedisClient;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.*;

public class OrderUniqueBarCodeRPCServiceImplTest extends MockBaseTestCase {

    /**
     * 默认类型--单位条形码
     */
    private final List<Integer> DEFAULT_TYPE = New.list(1);

    @InjectMocks
    private OrderUniqueBarCodeRPCServiceImpl orderUniqueBarCodeRPCService;

    @org.mockito.Mock
    private OrderUniqueBarCodeService orderUniqueBarCodeService;

    @org.mockito.Mock
    private RedisClient redisClient;

    @org.mockito.Mock
    private OrderMasterRPCClient orderMasterRPCClient;

    @org.mockito.Mock
    private OrderDetailRPCClient orderDetailRPCClient;

    @org.mockito.Mock
    private OrderAcceptRPCClient orderAcceptRPCClient;

    @org.mockito.Mock
    private BizWarehouseRoomServiceClient bizWarehouseRoomServiceClient;

    @org.mockito.Mock
    private UserRPCClient userRPCClient;

    public static class Mock {
        @MockMethod(targetClass = IdGeneratorUtils.class, scope = MockScope.ASSOCIATED)
        public static List<Long> getBarCodeList(int count) {
            return Arrays.asList(1L, 2L, 3L, 4L, 5L);
        }

        @MockMethod(targetClass = UserRPCClient.class)
        public UserBaseInfoDTO getUserBaseInfoByGuid(String userGuid) {
            return new UserBaseInfoDTO();
        }
    }

    @Test
    public void findByDetailId() {
        Mockito.when(orderUniqueBarCodeService.findFirstByDetailId(Mockito.anyInt(), DEFAULT_TYPE)).thenReturn(null);
        Mockito.when(orderUniqueBarCodeService.findByDetailId(Mockito.anyInt(), DEFAULT_TYPE)).thenReturn(null);
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setDetailId(1);
        request.setLimit(1);
        RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByDetailId(request);
        Assert.assertTrue(response.isSuccess());

        request.setLimit(10);
        response = orderUniqueBarCodeRPCService.findByDetailId(request);
        Assert.assertTrue(response.isSuccess());

    }

    @Test
    public void findByBarCode() {
        Mockito.when(orderUniqueBarCodeService.findByBarCode(Mockito.anyString())).thenReturn(null);
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setUniBarCode("1");
        request.setLimit(1);
        RemoteResponse<OrderUniqueBarCodeDTO> response = orderUniqueBarCodeRPCService.findByBarCode(request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void addDetailBatches() {
        Mockito.doNothing().when(redisClient).getLock(Mockito.anyString(), Mockito.anyInt());
        Mockito.doNothing().when(redisClient).unlock(Mockito.anyString());
        OrderUniqueBarCodeDTO barCodeDTO = new OrderUniqueBarCodeDTO();
        barCodeDTO.setOrderDetailId(1);
        barCodeDTO.setProductName("test");
        barCodeDTO.setBarCode(1L);
        Mockito.when(orderUniqueBarCodeService.findByDetailId(Mockito.anyList(), DEFAULT_TYPE, Mockito.anyInt())).thenReturn(Collections.singletonList(barCodeDTO));
        Mockito.when(orderUniqueBarCodeService.batchUpdateOrderUniqueBarCode(Mockito.anyList())).thenReturn(1);

        OrderUniqueBarCodeDTO item = new OrderUniqueBarCodeDTO();
        item.setOrderDetailId(1);
        item.setProductName("test");
        item.setTotal(1);
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.addDetailBatches(New.list(item));
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void setDetailBatches() {
        // todo 单测..
        Mockito.when(orderUniqueBarCodeService.batchUpdateOrderUniqueBarCode(Mockito.anyList())).thenReturn(1);

        OrderUniqueBarCodeDTO r = new OrderUniqueBarCodeDTO();
        r.setBarCode(1L);
        r.setStatus(4);
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.setDetailBatches(Collections.singletonList(r));
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void generatedBarCodeByOrder() {
        Mockito.doNothing().when(redisClient).getLock(Mockito.anyString(), Mockito.anyInt());
        Mockito.doNothing().when(redisClient).unlock(Mockito.anyString());
        OrderMasterDTO o = new OrderMasterDTO();
        o.setForderno("test");
        Mockito.when(orderMasterRPCClient.findById(Mockito.anyInt())).thenReturn(o);
        Mockito.when(orderUniqueBarCodeService.findExistedByOrderNo(Mockito.anyString())).thenReturn(0);
        Mockito.when(orderUniqueBarCodeService.batchInsert(Mockito.anyList())).thenReturn(1);

        OrderDetailDTO d = new OrderDetailDTO();
        d.setFquantity(BigDecimal.valueOf(5.00));
        Mockito.when(orderDetailRPCClient.findByMasterId(Mockito.anyInt())).thenReturn(Collections.singletonList(d));

        OrderBaseParamDTO req = new OrderBaseParamDTO();
        req.setOrderId(1L);
        RemoteResponse<Boolean> response = orderUniqueBarCodeRPCService.generatedBarCodeByOrder(req);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void findBarCodeStatisticsByOrderNo() {
        OrderUniqueBarCodeDTO o1 = new OrderUniqueBarCodeDTO();
        o1.setOrderDetailId(1);
        o1.setProductName("p1");
        o1.setStatus(1);
        o1.setPrinted(1);
        OrderUniqueBarCodeDTO o2 = new OrderUniqueBarCodeDTO();
        o2.setOrderDetailId(1);
        o2.setProductName("p1");
        o2.setStatus(0);
        o2.setPrinted(1);
        OrderUniqueBarCodeDTO o3 = new OrderUniqueBarCodeDTO();
        o3.setOrderDetailId(3);
        o3.setProductName("p3");
        o3.setStatus(1);
        o3.setPrinted(1);
        Mockito.when(orderUniqueBarCodeService.findByOrderNo(Mockito.anyString(), DEFAULT_TYPE)).thenReturn(Arrays.asList(o1, o2, o3));

        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setOrderNo("test");
        RemoteResponse<List<OrderUniqueBarCodeStatisticsDTO>> response = orderUniqueBarCodeRPCService.findBarCodeStatisticsByOrderNo(request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void findBarCodeStatisticsByDetailId() {
        OrderUniqueBarCodeDTO o1 = new OrderUniqueBarCodeDTO();
        o1.setOrderDetailId(1);
        o1.setProductName("p1");
        o1.setStatus(1);
        o1.setPrinted(1);
        Mockito.when(orderUniqueBarCodeService.findByDetailId(Mockito.any(Integer.class), DEFAULT_TYPE)).thenReturn(Collections.singletonList(o1));


        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setDetailId(1);
        RemoteResponse<OrderUniqueBarCodeStatisticsDTO> response = orderUniqueBarCodeRPCService.findBarCodeStatisticsByDetailId(request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void findByOrderNo() {
        Mockito.when(orderUniqueBarCodeService.findByOrderNo(Mockito.anyString(), DEFAULT_TYPE)).thenReturn(Collections.emptyList());

        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setOrderNo("test");
        RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByOrderNo(request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void findByBarCodeList() {
        Mockito.when(orderUniqueBarCodeService.findByBarCodeList(Mockito.anyList())).thenReturn(Collections.emptyList());

        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setUniBarCodeList(Collections.singletonList("1"));
        RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByBarCodeList(request);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void findByBusinessNo() {
        List<Integer> defaultType = New.list(1);
        Mockito.when(orderUniqueBarCodeService.findByEntryNo(Mockito.anyString(), defaultType)).thenReturn(Collections.emptyList());
        Mockito.when(orderUniqueBarCodeService.findByApplyNo(Mockito.anyString(), defaultType)).thenReturn(Collections.emptyList());
        Mockito.when(orderUniqueBarCodeService.findByExitNo(Mockito.anyString(), defaultType)).thenReturn(Collections.emptyList());
        Mockito.when(orderUniqueBarCodeService.findByReturnNo(Mockito.anyString(), defaultType)).thenReturn(Collections.emptyList());

        OrderDetailBatchesRequestDTO request1 = new OrderDetailBatchesRequestDTO();
        request1.setEntryNo("test");
        RemoteResponse<List<OrderUniqueBarCodeDTO>> response = orderUniqueBarCodeRPCService.findByBusinessNo(request1);
        Assert.assertTrue(response.isSuccess());

        OrderDetailBatchesRequestDTO request2 = new OrderDetailBatchesRequestDTO();
        request2.setExitNo("test");
        response = orderUniqueBarCodeRPCService.findByBusinessNo(request2);
        Assert.assertTrue(response.isSuccess());

        OrderDetailBatchesRequestDTO request3 = new OrderDetailBatchesRequestDTO();
        request3.setApplyNo("test");
        response = orderUniqueBarCodeRPCService.findByBusinessNo(request3);
        Assert.assertTrue(response.isSuccess());

        OrderDetailBatchesRequestDTO request4 = new OrderDetailBatchesRequestDTO();
        request4.setReturnNo("test");
        response = orderUniqueBarCodeRPCService.findByBusinessNo(request4);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void compareAndSetByBusinessNoAndStatus() {
        Mockito.when(orderUniqueBarCodeService.compareAndSetByEntryNoAndStatus(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt(), DEFAULT_TYPE)).thenReturn(1);
        Mockito.when(orderUniqueBarCodeService.compareAndSetByApplyNoAndStatus(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt(), DEFAULT_TYPE)).thenReturn(1);
        Mockito.when(orderUniqueBarCodeService.compareAndSetByExitNoAndStatus(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt(), DEFAULT_TYPE)).thenReturn(1);
        Mockito.when(orderUniqueBarCodeService.compareAndSetByReturnNoAndStatus(Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt(), DEFAULT_TYPE)).thenReturn(1);

        OrderUniqueBarCodeDTO barcode = new OrderUniqueBarCodeDTO();
        barcode.setOrderNo("test");
        barcode.setStatus(4);
        Mockito.when(orderUniqueBarCodeService.findByOrderNoList(Mockito.anyList(), DEFAULT_TYPE)).thenReturn(Collections.singletonList(barcode));
        Mockito.when(orderUniqueBarCodeService.findByEntryNo(Mockito.anyString(), DEFAULT_TYPE)).thenReturn(Arrays.asList(barcode));
        OrderMasterDTO o1 = new OrderMasterDTO();
        o1.setForderno("test");
        Mockito.when(orderMasterRPCClient.findByOrderNoList(Mockito.anyList())).thenReturn(Arrays.asList(o1));
        Map<String, String> hashMap = new HashMap();
        hashMap.put("test", "test");
        Mockito.when(bizWarehouseRoomServiceClient.getOrderNoEntryContactGuidMap(Mockito.anyList())).thenReturn(hashMap);
        UserBaseInfoDTO u = new UserBaseInfoDTO();
        u.setGuid("test");
        Mockito.when(userRPCClient.getUserBaseInfoByGuidList(Mockito.anyList(), Mockito.any())).thenReturn(Arrays.asList(u));

        Mockito.when(orderAcceptRPCClient.orderAccept(Mockito.any())).thenReturn(new ReceiptOrderResponseDO());

        OrderDetailBatchesRequestDTO request1 = new OrderDetailBatchesRequestDTO();
        request1.setEntryNo("test");
        request1.setUpdatedStatus(4);
        request1.setExpectStatus(3);
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.compareAndSetByBusinessNoAndStatus(request1);
        Assert.assertTrue(response.isSuccess());

        OrderDetailBatchesRequestDTO request2 = new OrderDetailBatchesRequestDTO();
        request2.setExitNo("test");
        request2.setUpdatedStatus(2);
        request2.setExpectStatus(1);
        response = orderUniqueBarCodeRPCService.compareAndSetByBusinessNoAndStatus(request2);
        Assert.assertTrue(response.isSuccess());

        OrderDetailBatchesRequestDTO request3 = new OrderDetailBatchesRequestDTO();
        request3.setApplyNo("test");
        request3.setUpdatedStatus(2);
        request3.setExpectStatus(1);
        response = orderUniqueBarCodeRPCService.compareAndSetByBusinessNoAndStatus(request3);
        Assert.assertTrue(response.isSuccess());

        OrderDetailBatchesRequestDTO request4 = new OrderDetailBatchesRequestDTO();
        request4.setReturnNo("test");
        request4.setUpdatedStatus(2);
        request4.setExpectStatus(1);
        response = orderUniqueBarCodeRPCService.compareAndSetByBusinessNoAndStatus(request4);
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void deleteByOrderNo() {
        String orderNo = "test1123";
        OrderDetailBatchesRequestDTO request = new OrderDetailBatchesRequestDTO();
        request.setOrderNo(orderNo);
        RemoteResponse<Integer> response = orderUniqueBarCodeRPCService.deleteByOrderNo(request);
        Assert.assertTrue(response.isSuccess());
    }
}
