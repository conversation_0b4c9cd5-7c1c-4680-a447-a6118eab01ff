package com.reagent.order.rpc.fundcard;

import com.reagent.order.OrderServerApplicationTests;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.base.order.service.OrderFundCardCacheRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderFundCardCacheRpcServiceTest extends OrderServerApplicationTests {

    @Resource
    private OrderFundCardCacheRpcService orderFundCardCacheRpcService;

    @Test
    @Transactional
    public void saveTest() {
        List<OrderFundCardDTO> orderFundCardDTOList = new ArrayList<>();
        OrderFundCardDTO dto = new OrderFundCardDTO();
        dto.setOrderId(136552);
        dto.setFundCardId("a7897afb-d6a1-40af-9a8b-5d8e08e7ed5f");
        dto.setFundCardNo("1-666704");
        dto.setFreezeAmount(new BigDecimal("292.000"));
        dto.setCreateTime(new Date());
        dto.setUpdateTime(new Date());
        dto.setSequence(2);
        orderFundCardDTOList.add(dto);

        dto = new OrderFundCardDTO();
        dto.setOrderId(106841);
        dto.setFundCardId("d3b42036-18f0-4252-8cf3-7dee246df957");
        dto.setFundCardNo("1-666875");
        dto.setFreezeAmount(new BigDecimal("9922.000"));
        dto.setCreateTime(new Date());
        dto.setUpdateTime(new Date());
        dto.setSequence(2);
        orderFundCardDTOList.add(dto);

        orderFundCardCacheRpcService.saveOrderFundCard(orderFundCardDTOList);
    }

    @Test
    public void findByOrderIdDescTest() {
        List<Integer> orderIdList = New.list(1000, 2000);
        RemoteResponse<List<OrderFundCardDTO>> result = orderFundCardCacheRpcService.findByOrderIdDesc(orderIdList);

    }
}
