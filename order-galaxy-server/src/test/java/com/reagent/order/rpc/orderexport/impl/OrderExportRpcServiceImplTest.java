package com.reagent.order.rpc.orderexport.impl;

import com.reagent.order.OrderServerApplicationTests;
import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.reagent.order.base.order.dto.OrderExportDTO;
import com.reagent.order.base.order.dto.OrderExportQueryDTO;
import com.reagent.order.base.order.dto.OrderExportResultDTO;
import com.reagent.order.base.order.enums.ExportFileTypeEnum;
import com.reagent.order.base.order.enums.OrderExportStatusEnum;
import com.reagent.order.base.order.service.OrderExportRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 16:31
 * @Version 1.0
 * @Desc:描述
 */
public class OrderExportRpcServiceImplTest extends OrderServerApplicationTests {

    @Resource
    private OrderExportRpcService orderExportRpcService;

    @Test
    public void testSaveOrderExport() {
        OrderExportDTO orderExportDTO = new OrderExportDTO();
        orderExportDTO.setFileName("商品明细-20201106");
        orderExportDTO.setExportDate(new Date());
        orderExportDTO.setUserId(999999);
        orderExportDTO.setUserName("天啊啊");
        orderExportDTO.setStatus(OrderExportStatusEnum.EXPORTING.getValue());
        orderExportDTO.setFileType(ExportFileTypeEnum.HMS_PRODUCT_DETAIL.getValue());
        orderExportDTO.setFileUrl("");

        RemoteResponse<OrderExportResultDTO> response = orderExportRpcService.saveOrderExport(orderExportDTO);

    }

    @Test
    public void testUpdateById() {
        OrderExportDTO orderExportDTO = new OrderExportDTO();
        orderExportDTO.setId(4);
        orderExportDTO.setStatus(OrderExportStatusEnum.EXPORT_SUCCESS.getValue());
        orderExportDTO.setFileUrl("https://ss2.bdstatic.com/70cFvnSh_Q1YnxGkpoWK1HF6hhy/it/u=1252118850,3382836282&fm=26&gp=0.jpg");
        RemoteResponse<Boolean> remoteResponse = orderExportRpcService.updateById(orderExportDTO);
        System.out.println(remoteResponse);
    }

    @Test
    public void testDeleteById() {
        RemoteResponse<Boolean> remoteResponse = orderExportRpcService.deleteOrderExportInfoById(4);
        System.out.println(remoteResponse);
    }

    @Test
    public void testFindOrderExportList() {
        OrderExportQueryDTO orderExportQueryDTO = new OrderExportQueryDTO();
        orderExportQueryDTO.setPageNo(1);
        orderExportQueryDTO.setPageSize(10);
//        orderExportQueryDTO.setExportDateStart(new Date());
//        orderExportQueryDTO.setExportDateEnd(new Date());
        orderExportQueryDTO.setStatus(2);
        orderExportQueryDTO.setUserId(1630);
        orderExportQueryDTO.setOrgId(5);
        orderExportQueryDTO.setFileTypeList(New.list(1,2,3,4,5,6,7,8));

        RemoteResponse<BasePageResultDTO<OrderExportDTO>> orderExportList = orderExportRpcService.findOrderExportList(orderExportQueryDTO);
        System.out.println(orderExportList);
    }

    @Test
    public void testFindById() {
        RemoteResponse<OrderExportDTO> remoteResponse = orderExportRpcService.findById(22);
        System.out.println(remoteResponse);
    }
}
