package com.reagent.order.rpc.log.service.impl;
import	java.util.concurrent.atomic.AtomicBoolean;

import com.reagent.order.OrderServerApplicationTests;
import com.reagent.order.base.log.dto.OrderDockingLogDTO;
import com.reagent.order.base.log.service.OrderOtherLogRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class OrderOtherLogRpcServiceImplTest extends OrderServerApplicationTests {

    @Autowired
    private OrderOtherLogRpcService orderOtherLogRpcService;

    @Test
    public void insertOrderDockingLog() {
        OrderDockingLogDTO orderDockingLogDTO = new OrderDockingLogDTO();
        orderDockingLogDTO.setDockingNumber("123456");
        orderDockingLogDTO.setExtraInfo("666");
        RemoteResponse remoteResponse = orderOtherLogRpcService.insertOrderDockingLog(orderDockingLogDTO);
        System.out.println(remoteResponse);
    }
}