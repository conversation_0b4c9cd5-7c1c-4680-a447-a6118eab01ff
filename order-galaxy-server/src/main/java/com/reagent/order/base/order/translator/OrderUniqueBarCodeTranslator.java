package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.model.OrderUniqueBarCodeDO;
import com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;

public class OrderUniqueBarCodeTranslator {

    /**
     * do to dto
     * @param item
     * @return
     */
    public static OrderUniqueBarCodeDTO doToDTO(OrderUniqueBarCodeDO item) {
        OrderUniqueBarCodeDTO result = new OrderUniqueBarCodeDTO();
        result.setUniBarCode(item.getBarCode());
        result.setType(item.getBarCodeType());
        result.setOrderNo(item.getOrderNo());
        result.setOrderDetailId(item.getOrderDetailId());
        result.setProductName(item.getProductName());
        result.setProductCode(item.getProductCode());
        result.setProductionDate(item.getProductionDate());
        result.setSpec(item.getSpec());
        result.setBatches(item.getBatches());
        result.setExpiration(item.getExpiration());
        result.setManufacturer(item.getManufacturer());
        result.setExterior(item.getExterior());
        // -1为默认值，表示没有绑定气瓶
        result.setGasBottleBarcode(item.getGasBottleBarcode());
        result.setStatus(item.getStatus());
        result.setBatchesStatus(item.getBatchesStatus());
        result.setInventoryStatus(item.getInventoryStatus());
        result.setTransactionStatus(item.getTransactionStatus());
        result.setEntryNo(item.getEntryNo());
        result.setApplyNo(item.getApplyNo());
        result.setExitNo(item.getExitNo());
        result.setReturnNo(item.getReturnNo());
        result.setPrinted(item.getPrinted());
        result.setBrand(item.getBrand());
        result.setSupplierName(item.getSupplierName());
        result.setSupplierId(item.getSupplierId());
        result.setProductPicture(item.getProductPicture());
        result.setReturnReason(item.getReturnReason());
        result.setReturnDescription(item.getReturnDescription());
        result.setRoomId(item.getRoomId());
        result.setPrice(item.getPrice());
        result.setValid(item.getValid());

        return result;
    }

    /**
     * dto to do
     * @param item
     * @return
     */
    public static OrderUniqueBarCodeDO dtoToDo(OrderUniqueBarCodeDTO item) {
        OrderUniqueBarCodeDO result = new OrderUniqueBarCodeDO();
        String barCode = item.getUniBarCode();
        result.setBarCode(barCode);
        result.setBarCodeType(item.getType());
        result.setOrderNo(item.getOrderNo());
        result.setOrderDetailId(item.getOrderDetailId());
        result.setProductName(item.getProductName());
        result.setProductCode(item.getProductCode());
        result.setProductionDate(item.getProductionDate());
        result.setSpec(item.getSpec());
        result.setBatches(item.getBatches());
        result.setExpiration(item.getExpiration());
        result.setManufacturer(item.getManufacturer());
        result.setExterior(item.getExterior());
        result.setGasBottleBarcode(item.getGasBottleBarcode());
        result.setStatus(item.getStatus());
        result.setBatchesStatus(item.getBatchesStatus());
        result.setInventoryStatus(item.getInventoryStatus());
        result.setTransactionStatus(item.getTransactionStatus());
        result.setEntryNo(item.getEntryNo());
        result.setApplyNo(item.getApplyNo());
        result.setExitNo(item.getExitNo());
        result.setReturnNo(item.getReturnNo());
        result.setPrinted(item.getPrinted());
        result.setBrand(item.getBrand());
        result.setSupplierName(item.getSupplierName());
        result.setSupplierId(item.getSupplierId());
        result.setProductPicture(item.getProductPicture());
        result.setReturnReason(item.getReturnReason());
        result.setReturnDescription(item.getReturnDescription());
        result.setRoomId(item.getRoomId());
        result.setPrice(item.getPrice());
        result.setValid(item.getValid());

        return result;
    }

    /**
     * order wrapper unique barcode
     *
     * @param item
     * @return
     */
    public static OrderUniqueBarCodeDTO orderWrapperUniqueBarCode(OrderMasterDTO order, OrderDetailDTO item) {
        OrderUniqueBarCodeDTO result = new OrderUniqueBarCodeDTO();
        result.setOrderNo(order.getForderno());
        result.setOrderDetailId(item.getId());
        result.setProductName(item.getFgoodname());
        result.setSpec(item.getFspec());
        result.setBrand(item.getFbrand());
        result.setProductCode(item.getFgoodcode());
        result.setSupplierName(order.getFsuppname());
        result.setProductPicture(item.getFpicpath());
        result.setPrice(item.getFbidprice());
        result.setSupplierId(order.getFsuppid());

        return result;
    }
}
