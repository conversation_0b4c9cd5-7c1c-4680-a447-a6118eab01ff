<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderEventStatusMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderEventStatusDO">
    <!--@mbg.generated-->
    <!--@Table order_event_status-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="event_type" jdbcType="INTEGER" property="eventType" />
    <result column="event_status" jdbcType="INTEGER" property="eventStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_no, org_id, event_type, event_status, create_time, update_time
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_event_status
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from order_event_status
    where id = #{id,jdbcType=INTEGER}
  </delete>
  
  <insert id="insertList" keyColumn="id" keyProperty="id" >
    <!--@mbg.generated-->
    insert into order_event_status (order_no, org_id, event_type, 
      event_status
      ) values
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.orderNo,jdbcType=VARCHAR},
      #{element.orgId,jdbcType=INTEGER},
      #{element.eventType,jdbcType=INTEGER}, 
      #{element.eventStatus,jdbcType=INTEGER}
      )
    </foreach>
    
  </insert>
  
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderEventStatusDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_event_status (order_no, org_id, event_type, 
      event_status
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{orgId,jdbcType=INTEGER}, #{eventType,jdbcType=INTEGER}, 
      #{eventStatus,jdbcType=INTEGER}
      )
  </insert>
  
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderEventStatusDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_event_status
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="eventStatus != null">
        event_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventStatus != null">
        #{eventStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderEventStatusDO">
    <!--@mbg.generated-->
    update order_event_status
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventStatus != null">
        event_status = #{eventStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.OrderEventStatusDO">
    <!--@mbg.generated-->
    update order_event_status
    set order_no = #{orderNo,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=INTEGER},
      event_type = #{eventType,jdbcType=INTEGER},
      event_status = #{eventStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <select id="selectByOrderNoAndEventType" resultMap="BaseResultMap" parameterType="com.reagent.order.base.order.model.OrderEventStatusDO">
    select 
    <include refid="Base_Column_List" />
    from order_event_status
    where order_no = #{orderNo,jdbcType=VARCHAR}
    <if test="eventType != null">
        and event_type = #{eventType,jdbcType=INTEGER}
    </if>
  </select>

  <select id="selectByOrderNoListAndEventType" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from order_event_status
    where order_no in
    <foreach item="item" index="index" collection="orderNoList"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    <if test="eventType != null">
      and event_type = #{eventType,jdbcType=INTEGER}
    </if>
  </select>
  
  <delete id="deleteByOrderNoAndEventType" parameterType="com.reagent.order.base.order.model.OrderEventStatusDO">
    delete from order_event_status
    where order_no = #{orderNo,jdbcType=VARCHAR}
    <if test="eventType != null">
        and event_type = #{eventType,jdbcType=INTEGER}
    </if>
  </delete>
  
  <update id="updateByOrderNoAndEventType" parameterType="com.reagent.order.base.order.model.OrderEventStatusDO">
    update order_event_status
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=INTEGER},
      </if>
      <if test="eventStatus != null">
        event_status = #{eventStatus,jdbcType=INTEGER},
      </if>
    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR} 
    and event_type = #{eventType,jdbcType=INTEGER}
  </update>
</mapper>