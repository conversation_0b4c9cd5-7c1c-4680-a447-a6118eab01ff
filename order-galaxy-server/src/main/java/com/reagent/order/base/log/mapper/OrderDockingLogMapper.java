package com.reagent.order.base.log.mapper;

import com.reagent.order.base.log.model.OrderDockingLog;

public interface OrderDockingLogMapper {

    /**
     * 根据主键删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**插入
     * 选择
     * @param record
     * @return
     */
    int insertSelective(OrderDockingLog record);

    OrderDockingLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderDockingLog record);
}