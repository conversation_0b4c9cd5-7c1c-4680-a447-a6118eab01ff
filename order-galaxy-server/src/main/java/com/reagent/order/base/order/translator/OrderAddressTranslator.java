package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.model.OrderAddressDO;

/**
 * 订单配送地址转换器
 */
public class OrderAddressTranslator {

    public static OrderAddressDTO doToDto(OrderAddressDO item) {
        if (item == null) {
            return null;
        }
        OrderAddressDTO result = new OrderAddressDTO();
        result.setId(item.getId());
        result.setOrderNo(item.getOrderNo());
        result.setReceiverName(item.getReceiverName());
        result.setReceiverPhone(item.getReceiverPhone());
        result.setProvince(item.getProvince());
        result.setCity(item.getCity());
        result.setRegion(item.getRegion());
        result.setAddress(item.getAddress());
        result.setLabel(item.getLabel());
        result.setGeo(item.getGeo());
        result.setDeliveryType(item.getDeliveryType());
        result.setReceiverNameProxy(item.getReceiverNameProxy());
        result.setReceiverPhoneProxy(item.getReceiverPhoneProxy());
        result.setProvinceProxy(item.getProvinceProxy());
        result.setCityProxy(item.getCityProxy());
        result.setRegionProxy(item.getRegionProxy());
        result.setAddressProxy(item.getAddressProxy());
        result.setGeoProxy(item.getGeoProxy());
        result.setCreateTime(item.getCreateTime());
        result.setUpdateTime(item.getUpdateTime());
        result.setDeliveryStatus(item.getDeliveryStatus());
        result.setDeliveryUser(item.getDeliveryUser());
        result.setSortedUser(item.getSortedUser());
        result.setProxySourceType(item.getProxySourceType());
        return result;
    }

    public static OrderAddressDO dtoToDo(OrderAddressDTO dto) {
        OrderAddressDO result = new OrderAddressDO();
        result.setId(dto.getId());
        result.setOrderNo(dto.getOrderNo());
        result.setReceiverName(dto.getReceiverName());
        result.setReceiverPhone(dto.getReceiverPhone());
        result.setProvince(dto.getProvince());
        result.setCity(dto.getCity());
        result.setRegion(dto.getRegion());
        result.setAddress(dto.getAddress());
        result.setLabel(dto.getLabel());
        result.setGeo(dto.getGeo());
        result.setDeliveryType(dto.getDeliveryType());
        result.setReceiverNameProxy(dto.getReceiverNameProxy());
        result.setReceiverPhoneProxy(dto.getReceiverPhoneProxy());
        result.setProvinceProxy(dto.getProvinceProxy());
        result.setCityProxy(dto.getCityProxy());
        result.setRegionProxy(dto.getRegionProxy());
        result.setAddressProxy(dto.getAddressProxy());
        result.setGeoProxy(dto.getGeoProxy());
        result.setCreateTime(dto.getCreateTime());
        result.setUpdateTime(dto.getUpdateTime());
        result.setDeliveryStatus(dto.getDeliveryStatus());
        result.setDeliveryUser(dto.getDeliveryUser());
        result.setSortedUser(dto.getSortedUser());
        result.setProxySourceType(dto.getProxySourceType());
        return result;
    }
}
