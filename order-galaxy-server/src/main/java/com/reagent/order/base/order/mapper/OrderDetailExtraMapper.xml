<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderDetailExtraMapper">

    <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderDetailExtraDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="orderId" column="order_id" jdbcType="INTEGER"/>
            <result property="orderDetailId" column="order_detail_id" jdbcType="INTEGER"/>
            <result property="orgId" column="org_id" jdbcType="INTEGER"/>
            <result property="extraKey" column="extra_key" jdbcType="VARCHAR"/>
            <result property="extraKeyType" column="extra_key_type" jdbcType="INTEGER"/>
            <result property="extraValue" column="extra_value" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,order_detail_id,
        org_id,extra_key,extra_key_type,extra_value,
        create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from order_detail_extra
        where  id = #{id,jdbcType=INTEGER} 
    </select>

    <select id="listInOrderIdAndOrderDetailId"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from order_detail_extra
        where
        <if test="orderIdList != null">
            order_id in
            <foreach item="item" index="index" collection="orderIdList"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="orderDetailIdList != null">
            order_detail_id in
            <foreach item="item" index="index" collection="orderDetailIdList"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="extraKeyList != null and extraKeyList.size() > 0">
            and extra_key in
            <foreach item="item" index="index" collection="extraKeyList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from order_detail_extra
        where  id = #{id,jdbcType=INTEGER} 
    </delete>

    <delete id="deleteInOrderIdAndOrderDetailId">
        delete from order_detail_extra
        where
        <if test="orderIdList != null">
            order_id in
            <foreach item="item" index="index" collection="orderIdList"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="orderDetailIdList != null">
            order_detail_id in
            <foreach item="item" index="index" collection="orderDetailIdList"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderDetailExtraDO" useGeneratedKeys="true">
        insert into order_detail_extra
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="orderId != null">order_id,</if>
                <if test="orderDetailId != null">order_detail_id,</if>
                <if test="orgId != null">org_id,</if>
                <if test="extraKey != null">extra_key,</if>
                <if test="extraValue != null">extra_value,</if>
                <if test="extraKeyType != null">extra_key_type,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="orderId != null">#{orderId,jdbcType=INTEGER},</if>
                <if test="orderDetailId != null">#{orderDetailId,jdbcType=INTEGER},</if>
                <if test="orgId != null">#{orgId,jdbcType=INTEGER},</if>
                <if test="extraKey != null">#{extraKey,jdbcType=VARCHAR},</if>
                <if test="extraValue != null">#{extraValue,jdbcType=VARCHAR},</if>
                <if test="extraKeyType != null">#{extraKeyType,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <insert id="batchInsert">
        insert into order_detail_extra
        ( order_id,order_detail_id
        ,org_id,extra_key,extra_value,extra_key_type)
        values
        <foreach collection="list" item="element" index="index" separator=",">
            (
            <choose>
                <when test="element.orderId != null ">
                    #{element.orderId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.orderDetailId != null ">
                    #{element.orderDetailId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.orgId != null ">
                    #{element.orgId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.extraKey != null ">
                    #{element.extraKey,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.extraValue != null">
                    #{element.extraValue,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            <choose>
                <when test="element.extraKeyType != null">
                    #{element.extraKeyType,jdbcType=INTEGER}
                </when>
                <otherwise>
                    0
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderDetailExtraDO">
        update order_detail_extra
        <set>
                <if test="orderId != null">
                    order_id = #{orderId,jdbcType=INTEGER},
                </if>
                <if test="orderDetailId != null">
                    order_detail_id = #{orderDetailId,jdbcType=INTEGER},
                </if>
                <if test="orgId != null">
                    org_id = #{orgId,jdbcType=INTEGER},
                </if>
                <if test="extraKey != null">
                    extra_key = #{extraKey,jdbcType=VARCHAR},
                </if>
                <if test="extraValue != null">
                    extra_value = #{extraValue,jdbcType=VARCHAR},
                </if>
                <if test="extraKeyType != null">
                    extra_key_type = #{extraKeyType,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>

    <update id="batchUpdateExtraValue">
        <foreach collection="list" item="element" separator=";">
            update order_detail_extra set extra_value = #{element.extraValue,jdbcType=VARCHAR}
            where order_detail_id = #{element.orderDetailId,jdbcType=INTEGER}
            and extra_key = #{element.extraKey,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>
