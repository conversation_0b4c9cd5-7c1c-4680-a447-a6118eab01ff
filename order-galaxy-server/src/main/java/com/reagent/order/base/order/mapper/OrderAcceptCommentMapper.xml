<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderAcceptCommentMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderAcceptCommentDO">
    <!--@mbg.generated-->
    <!--@Table order_accept_comment-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="accept_comment_tags" jdbcType="VARCHAR" property="acceptCommentTags" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, accept_comment_tags, order_id, org_id, created_time, updated_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_accept_comment
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from order_accept_comment
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderAcceptCommentDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_accept_comment (accept_comment_tags, order_id, org_id, 
      created_time, updated_time)
    values (#{acceptCommentTags,jdbcType=VARCHAR}, #{orderId,jdbcType=INTEGER}, #{orgId,jdbcType=INTEGER}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderAcceptCommentDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_accept_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="acceptCommentTags != null">
        accept_comment_tags,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="acceptCommentTags != null">
        #{acceptCommentTags,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderAcceptCommentDO">
    <!--@mbg.generated-->
    update order_accept_comment
    <set>
      <if test="acceptCommentTags != null">
        accept_comment_tags = #{acceptCommentTags,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.OrderAcceptCommentDO">
    <!--@mbg.generated-->
    update order_accept_comment
    set accept_comment_tags = #{acceptCommentTags,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=INTEGER},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-02-26-->
  <select id="findByOrderIdInAndOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_accept_comment
    <where>order_id in
      <foreach item="item" index="index" collection="orderIdCollection"
               open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>

      <if test="orgId != null">
        and org_id=#{orgId,jdbcType=INTEGER}
      </if>
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-02-26-->
  <delete id="deleteByOrderId">
        delete from order_accept_comment
        where order_id=#{orderId,jdbcType=INTEGER}
    </delete>
</mapper>
