<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderUniqueBarCodeDOMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderUniqueBarCodeDO">
    <!--@mbg.generated-->
    <!--@Table order_unique_bar_code-->
    <id column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="bar_code_type" jdbcType="INTEGER" property="barCodeType" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_detail_id" jdbcType="INTEGER" property="orderDetailId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="production_date" jdbcType="TIMESTAMP" property="productionDate" />
    <result column="spec" jdbcType="VARCHAR" property="spec" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="batches" jdbcType="VARCHAR" property="batches" />
    <result column="expiration" jdbcType="VARCHAR" property="expiration" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="exterior" jdbcType="INTEGER" property="exterior" />
    <result column="gas_bottle_barcode" jdbcType="VARCHAR" property="gasBottleBarcode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="batches_status" jdbcType="INTEGER" property="batchesStatus" />
    <result column="inventory_status" jdbcType="INTEGER" property="inventoryStatus" />
    <result column="transaction_status" jdbcType="INTEGER" property="transactionStatus" />
    <result column="room_id" jdbcType="INTEGER" property="roomId" />
    <result column="entry_no" jdbcType="VARCHAR" property="entryNo" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="exit_no" jdbcType="VARCHAR" property="exitNo" />
    <result column="return_no" jdbcType="VARCHAR" property="returnNo" />
    <result column="product_picture" jdbcType="VARCHAR" property="productPicture" />
    <result column="return_reason" jdbcType="VARCHAR" property="returnReason" />
    <result column="return_description" jdbcType="VARCHAR" property="returnDescription" />
    <result column="printed" jdbcType="INTEGER" property="printed" />
    <result column="valid" jdbcType="INTEGER" property="valid" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    bar_code, bar_code_type, order_no, order_detail_id, product_name, product_code, production_date, spec, brand, supplier_id, supplier_name,
    product_picture, price, batches, expiration, manufacturer, exterior, gas_bottle_barcode,
    `status`, batches_status, inventory_status, transaction_status,
    room_id, entry_no, apply_no, exit_no, return_no, return_reason, return_description, printed, valid, created_time, updated_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_unique_bar_code
    where bar_code = #{barCode,jdbcType=VARCHAR}
  </select>
<!--auto generated by MybatisCodeHelper on 2021-10-12-->
  <select id="findByOrderDetailId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where order_detail_id=#{orderDetailId,jdbcType=INTEGER}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </select>

  <!--auto generated by MybatisCodeHelper on 2021-10-12-->
  <select id="findByOrderDetailIdFirst" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where order_detail_id=#{orderDetailId,jdbcType=INTEGER}
    limit 1
  </select>

<!--auto generated by MybatisCodeHelper on 2021-10-15-->
  <select id="findByOrderDetailIdInAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where order_detail_id in
    <foreach item="item" index="index" collection="orderDetailIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    <if test="status != null">
      and `status`=#{status,jdbcType=INTEGER}
    </if>
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-10-17-->
  <update id="batchUpdateByBarCode">
    <foreach collection="updatedList" item="updated" index="index"
             separator=";">
      update order_unique_bar_code
      <set>
        <if test="updated.batches != null">
          batches = #{updated.batches,jdbcType=VARCHAR},
        </if>
        <if test="updated.expiration != null">
          expiration = #{updated.expiration,jdbcType=VARCHAR},
        </if>
        <if test="updated.manufacturer != null">
          manufacturer = #{updated.manufacturer,jdbcType=VARCHAR},
        </if>
        <if test="updated.exterior != null">
          exterior = #{updated.exterior,jdbcType=INTEGER},
        </if>
        <if test="updated.gasBottleBarcode != null">
          gas_bottle_barcode = #{updated.gasBottleBarcode,jdbcType=INTEGER},
        </if>
        <if test="updated.status != null">
          status = #{updated.status,jdbcType=INTEGER},
        </if>
        <if test="updated.batchesStatus != null">
          batches_status = #{updated.batchesStatus, jdbcType=INTEGER},
        </if>
        <if test="updated.inventoryStatus != null">
          inventory_status = #{updated.inventoryStatus, jdbcType=INTEGER},
        </if>
        <if test="updated.transactionStatus != null">
          transaction_status = #{updated.transactionStatus, jdbcType=INTEGER},
        </if>
        <if test="updated.roomId != null">
          room_id = #{updated.roomId,jdbcType=INTEGER},
        </if>
        <if test="updated.entryNo != null">
          entry_no = #{updated.entryNo,jdbcType=VARCHAR},
        </if>
        <if test="updated.applyNo != null">
          apply_no = #{updated.applyNo,jdbcType=VARCHAR},
        </if>
        <if test="updated.exitNo != null">
          exit_no = #{updated.exitNo,jdbcType=VARCHAR},
        </if>
        <if test="updated.returnNo != null">
          return_no = #{updated.returnNo,jdbcType=VARCHAR},
        </if>
        <if test="updated.printed != null">
          printed = #{updated.printed,jdbcType=INTEGER},
        </if>
        <if test="updated.updatedTime != null">
          updated_time = #{updated.updatedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updated.returnReason != null">
          return_reason = #{updated.returnReason,jdbcType=VARCHAR},
        </if>
        <if test="updated.returnDescription != null">
          return_description = #{updated.returnDescription,jdbcType=VARCHAR},
        </if>
        <if test="updated.valid != null">
          valid = #{updated.valid,jdbcType=INTEGER},
        </if>
        <if test="updated.productionDate != null">
          production_date = #{updated.productionDate,jdbcType=TIMESTAMP},
        </if>
      </set>
      where bar_code=#{updated.barCode,jdbcType=VARCHAR}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2021-10-20-->
  <insert id="batchInsert">
        INSERT INTO order_unique_bar_code(
        bar_code,
        bar_code_type,
        order_no,
        order_detail_id,
        product_name,
        product_code,
        production_date,
        spec,
        brand,
        supplier_id,
        supplier_name,
        product_picture,
        price,
        batches,
        expiration,
        manufacturer,
        exterior,
        gas_bottle_barcode,
        status,
        batches_status,
        transaction_status,
        inventory_status,
        room_id,
        entry_no,
        apply_no,
        exit_no,
        return_no,
        return_reason,
        return_description,
        printed,
        valid
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.barCode,jdbcType=VARCHAR},
            <choose>
              <when test="element.barCodeType != null">
                #{element.barCodeType,jdbcType=INTEGER},
              </when>
              <otherwise>
                1,
              </otherwise>
            </choose>
            #{element.orderNo,jdbcType=VARCHAR},
            #{element.orderDetailId,jdbcType=INTEGER},
            #{element.productName,jdbcType=VARCHAR},
            #{element.productCode,jdbcType=VARCHAR},
            #{element.productionDate,jdbcType=TIMESTAMP},
            #{element.spec,jdbcType=VARCHAR},

          <choose>
            <when test="element.brand != null ">
              #{element.brand,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.supplierId != null ">
              #{element.supplierId,jdbcType=INTEGER},
            </when>
            <otherwise>
              0,
            </otherwise>
          </choose>

          <choose>
            <when test="element.supplierName != null ">
              #{element.supplierName,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.productPicture != null ">
              #{element.productPicture,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.price != null ">
              #{element.price,jdbcType=DECIMAL},
            </when>
            <otherwise>
              0.00,
            </otherwise>
          </choose>

          <choose>
            <when test="element.batches != null ">
              #{element.batches,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.expiration != null ">
              #{element.expiration,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.manufacturer != null">
              #{element.manufacturer,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.exterior != null ">
              #{element.exterior,jdbcType=INTEGER},
            </when>
            <otherwise>
              0,
            </otherwise>
          </choose>

          <choose>
            <when test="element.gasBottleBarcode != null ">
              #{element.gasBottleBarcode,jdbcType=VARCHAR},
            </when>
            <otherwise>
              "",
            </otherwise>
          </choose>

            #{element.status,jdbcType=INTEGER},

          <choose>
            <when test="element.batchesStatus != null">
              #{element.batchesStatus, jdbcType=INTEGER},
            </when>
            <otherwise>
              0,
            </otherwise>
          </choose>

          <choose>
            <when test="element.transactionStatus != null">
              #{element.transactionStatus, jdbcType=INTEGER},
            </when>
            <otherwise>
              1,
            </otherwise>
          </choose>

          <choose>
            <when test="element.inventoryStatus != null">
              #{element.inventoryStatus, jdbcType=INTEGER},
            </when>
            <otherwise>
              0,
            </otherwise>
          </choose>

          <choose>
            <when test="element.roomId != null ">
              #{element.roomId,jdbcType=INTEGER},
            </when>
            <otherwise>
              0,
            </otherwise>
          </choose>

          <choose>
            <when test="element.entryNo != null ">
              #{element.entryNo,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.applyNo != null ">
              #{element.applyNo,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.exitNo != null ">
              #{element.exitNo,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.returnNo != null ">
              #{element.returnNo,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.returnReason != null ">
              #{element.returnReason,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.returnDescription != null ">
              #{element.returnDescription,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.printed != null ">
              #{element.printed,jdbcType=INTEGER},
            </when>
            <otherwise>
              0,
            </otherwise>
          </choose>

          <choose>
            <when test="element.valid != null ">
              #{element.valid,jdbcType=INTEGER}
            </when>
            <otherwise>
              1
            </otherwise>
          </choose>
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2021-10-20-->
  <select id="countByOrderNo" resultType="java.lang.Integer">
    select count(1)
    from order_unique_bar_code
    where order_no=#{orderNo,jdbcType=VARCHAR}
    and bar_code_type = 1
  </select>

<!--auto generated by MybatisCodeHelper on 2021-10-21-->
  <select id="findByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where order_no=#{orderNo,jdbcType=VARCHAR}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-10-29-->
  <select id="findByBarCodeIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where bar_code in
    <foreach item="item" index="index" collection="barCodeCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-11-02-->
  <select id="findByEntryNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where entry_no=#{entryNo,jdbcType=VARCHAR}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-11-02-->
  <select id="findByExitNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where exit_no=#{exitNo,jdbcType=VARCHAR}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-11-02-->
  <select id="findByApplyNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where apply_no=#{applyNo,jdbcType=VARCHAR}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-11-02-->
  <select id="findByReturnNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where return_no=#{returnNo,jdbcType=VARCHAR}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-11-03-->
  <update id="updateByEntryNoAndStatus">
    update order_unique_bar_code
    <set>
      <if test="updated.batches != null">
        batches = #{updated.batches,jdbcType=VARCHAR},
      </if>
      <if test="updated.expiration != null">
        expiration = #{updated.expiration,jdbcType=VARCHAR},
      </if>
      <if test="updated.manufacturer != null">
        manufacturer = #{updated.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="updated.exterior != null">
        exterior = #{updated.exterior,jdbcType=INTEGER},
      </if>
      <if test="updated.status != null">
        status = #{updated.status,jdbcType=INTEGER},
      </if>
      <if test="updated.roomId != null">
        room_id = #{updated.roomId,jdbcType=INTEGER},
      </if>
      <if test="updated.entryNo != null">
        entry_no = #{updated.entryNo,jdbcType=VARCHAR},
      </if>
      <if test="updated.exitNo != null">
        exit_no = #{updated.exitNo,jdbcType=VARCHAR},
      </if>
      <if test="updated.productPicture != null">
        product_picture = #{updated.productPicture,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnReason != null">
        return_reason = #{updated.returnReason,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnDescription != null">
        return_description = #{updated.returnDescription,jdbcType=VARCHAR},
      </if>
      <if test="updated.printed != null">
        printed = #{updated.printed,jdbcType=INTEGER},
      </if>
      <if test="updated.valid != null">
        valid = #{updated.valid,jdbcType=INTEGER},
      </if>
      <if test="updated.createdTime != null">
        created_time = #{updated.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updatedTime != null">
        updated_time = #{updated.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.productionDate != null">
        production_date = #{updated.productionDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where entry_no=#{entryNo,jdbcType=VARCHAR}
    and `status`=#{expectStatus,jdbcType=INTEGER}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2021-11-03-->
  <update id="updateByExitNoAndStatus">
    update order_unique_bar_code
    <set>
      <if test="updated.batches != null">
        batches = #{updated.batches,jdbcType=VARCHAR},
      </if>
      <if test="updated.expiration != null">
        expiration = #{updated.expiration,jdbcType=VARCHAR},
      </if>
      <if test="updated.manufacturer != null">
        manufacturer = #{updated.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="updated.exterior != null">
        exterior = #{updated.exterior,jdbcType=INTEGER},
      </if>
      <if test="updated.status != null">
        status = #{updated.status,jdbcType=INTEGER},
      </if>
      <if test="updated.roomId != null">
        room_id = #{updated.roomId,jdbcType=INTEGER},
      </if>
      <if test="updated.exitNo != null">
        exit_no = #{updated.exitNo,jdbcType=VARCHAR},
      </if>
      <if test="updated.productPicture != null">
        product_picture = #{updated.productPicture,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnReason != null">
        return_reason = #{updated.returnReason,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnDescription != null">
        return_description = #{updated.returnDescription,jdbcType=VARCHAR},
      </if>
      <if test="updated.printed != null">
        printed = #{updated.printed,jdbcType=INTEGER},
      </if>
      <if test="updated.valid != null">
        valid = #{updated.valid,jdbcType=INTEGER},
      </if>
      <if test="updated.createdTime != null">
        created_time = #{updated.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updatedTime != null">
        updated_time = #{updated.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.productionDate != null">
        production_date = #{updated.productionDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where exit_no=#{exitNo,jdbcType=VARCHAR}
    and `status`=#{expectStatus,jdbcType=INTEGER}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </update>

  <!--auto generated by MybatisCodeHelper on 2021-11-03-->
  <update id="updateByApplyNoAndStatus">
    update order_unique_bar_code
    <set>
      <if test="updated.batches != null">
        batches = #{updated.batches,jdbcType=VARCHAR},
      </if>
      <if test="updated.expiration != null">
        expiration = #{updated.expiration,jdbcType=VARCHAR},
      </if>
      <if test="updated.manufacturer != null">
        manufacturer = #{updated.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="updated.exterior != null">
        exterior = #{updated.exterior,jdbcType=INTEGER},
      </if>
      <if test="updated.status != null">
        status = #{updated.status,jdbcType=INTEGER},
      </if>
      <if test="updated.roomId != null">
        room_id = #{updated.roomId,jdbcType=INTEGER},
      </if>
      <if test="updated.applyNo != null">
        apply_no = #{updated.applyNo,jdbcType=VARCHAR},
      </if>
      <if test="updated.productPicture != null">
        product_picture = #{updated.productPicture,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnReason != null">
        return_reason = #{updated.returnReason,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnDescription != null">
        return_description = #{updated.returnDescription,jdbcType=VARCHAR},
      </if>
      <if test="updated.printed != null">
        printed = #{updated.printed,jdbcType=INTEGER},
      </if>
      <if test="updated.valid != null">
        valid = #{updated.valid,jdbcType=INTEGER},
      </if>
      <if test="updated.createdTime != null">
        created_time = #{updated.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updatedTime != null">
        updated_time = #{updated.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.productionDate != null">
        production_date = #{updated.productionDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where apply_no=#{applyNo,jdbcType=VARCHAR}
    and `status`=#{expectStatus,jdbcType=INTEGER}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </update>

  <!--auto generated by MybatisCodeHelper on 2021-11-03-->
  <update id="updateStatusByBusinessNo">
    update order_unique_bar_code
    <set>
      <if test="updated.inventoryStatus != null">
        inventory_status = #{updated.inventoryStatus,jdbcType=INTEGER},
      </if>
      <if test="updated.transactionStatus != null">
        transaction_status = #{updated.transactionStatus, jdbcType=INTEGER},
      </if>
    </set>
    where
    <if test="updated.orderNo != null">
      order_no =#{updated.orderNo,jdbcType=VARCHAR} and
    </if>
    <if test="updated.returnNo != null">
      return_no =#{updated.returnNo,jdbcType=VARCHAR} and
    </if>
    <if test="updated.entryNo != null">
      entry_no = #{updated.entryNo,jdbcType=VARCHAR} and
    </if>
    bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </update>

  <!--auto generated by MybatisCodeHelper on 2021-11-03-->
  <update id="updateByReturnNoAndStatus">
    update order_unique_bar_code
    <set>
      <if test="updated.batches != null">
        batches = #{updated.batches,jdbcType=VARCHAR},
      </if>
      <if test="updated.expiration != null">
        expiration = #{updated.expiration,jdbcType=VARCHAR},
      </if>
      <if test="updated.manufacturer != null">
        manufacturer = #{updated.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="updated.exterior != null">
        exterior = #{updated.exterior,jdbcType=INTEGER},
      </if>
      <if test="updated.status != null">
        status = #{updated.status,jdbcType=INTEGER},
      </if>
      <if test="updated.productPicture != null">
        product_picture = #{updated.productPicture,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnReason != null">
        return_reason = #{updated.returnReason,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnDescription != null">
        return_description = #{updated.returnDescription,jdbcType=VARCHAR},
      </if>
      <if test="updated.printed != null">
        printed = #{updated.printed,jdbcType=INTEGER},
      </if>
      <if test="updated.valid != null">
        valid = #{updated.valid,jdbcType=INTEGER},
      </if>
      <if test="updated.createdTime != null">
        created_time = #{updated.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updatedTime != null">
        updated_time = #{updated.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.productionDate != null">
        production_date = #{updated.productionDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where return_no=#{returnNo,jdbcType=VARCHAR}
    and `status`=#{expectStatus,jdbcType=INTEGER}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="updateByOrderNoAndStatus">
    update order_unique_bar_code
    <set>
      <if test="updated.batches != null">
        batches = #{updated.batches,jdbcType=VARCHAR},
      </if>
      <if test="updated.expiration != null">
        expiration = #{updated.expiration,jdbcType=VARCHAR},
      </if>
      <if test="updated.manufacturer != null">
        manufacturer = #{updated.manufacturer, jdbcType=VARCHAR},
      </if>
      <if test="updated.exterior != null">
        exterior = #{updated.exterior,jdbcType=INTEGER},
      </if>
      <if test="updated.status != null">
        status = #{updated.status,jdbcType=INTEGER},
      </if>
      <if test="updated.productPicture != null">
        product_picture = #{updated.productPicture,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnReason != null">
        return_reason = #{updated.returnReason,jdbcType=VARCHAR},
      </if>
      <if test="updated.returnDescription != null">
        return_description = #{updated.returnDescription,jdbcType=VARCHAR},
      </if>
      <if test="updated.printed != null">
        printed = #{updated.printed,jdbcType=INTEGER},
      </if>
      <if test="updated.valid != null">
        valid = #{updated.valid,jdbcType=INTEGER},
      </if>
      <if test="updated.createdTime != null">
        created_time = #{updated.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updatedTime != null">
        updated_time = #{updated.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.productionDate != null">
        production_date = #{updated.productionDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where order_no =#{orderNo,jdbcType=VARCHAR}
    and `status`=#{expectStatus,jdbcType=INTEGER}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2021-11-11-->
  <delete id="deleteByOrderNo">
    delete from order_unique_bar_code
    where order_no=#{orderNo,jdbcType=VARCHAR}
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </delete>

<!--auto generated by MybatisCodeHelper on 2021-11-11-->
  <select id="findByOrderNoIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    where order_no in
    <foreach item="item" index="index" collection="orderNoCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    and bar_code_type in
    <foreach item="type" index="index" collection="typeList"
             open="(" separator="," close=")">
      #{type,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2022-01-11-->
  <select id="queryPageForInventory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    <where>
      <if test="params.roomId != null">
        and room_id=#{params.roomId,jdbcType=INTEGER}
      </if>
      <if test="params.supplierId != null">
        and supplier_id=#{params.supplierId,jdbcType=INTEGER}
      </if>
      <if test="params.productCode != null">
        and product_code=#{params.productCode,jdbcType=VARCHAR}
      </if>
      <if test="params.productName != null">
        and product_name=#{params.productName,jdbcType=VARCHAR}
      </if>
      <if test="params.status != null and param.status.size() > 0">
        and status in
        <foreach item="item" index="index" collection="params.status"
                 open="(" separator="," close=")">
          #{item,jdbcType=BIGINT}
        </foreach>
      </if>
      <if test="params.inventoryStatusList != null and params.inventoryStatusList.size() > 0">
        and inventory_status in
        <foreach item="inventoryStatus" index="index" collection="params.inventoryStatusList"
                 open="(" separator="," close=")">
          #{inventoryStatus,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="params.valid != null">
        and valid=#{params.valid,jdbcType=INTEGER}
      </if>
      <if test="params.spec != null">
        and spec = #{params.spec,jdbcType=VARCHAR}
      </if>
      <if test="params.brand != null">
        and brand = #{params.brand,jdbcType=VARCHAR}
      </if>
      and bar_code_type = 1
    </where>
  </select>

  <select id="rangeQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_unique_bar_code
    <where>
      <if test="orderNo != null">
        and order_no = #{orderNo,jdbcType=VARCHAR}
      </if>
      <if test="minBarcode != null">
        and bar_code <![CDATA[>]]> #{minBarcode,jdbcType=VARCHAR}
      </if>
    </where>
    order by bar_code
    limit #{limit,jdbcType=INTEGER}
  </select>
</mapper>