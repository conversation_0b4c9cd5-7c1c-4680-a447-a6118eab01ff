package com.reagent.order.base.order.mapper;
import java.util.Collection;
import org.apache.ibatis.annotations.Param;

import com.reagent.order.base.order.model.BusinessDockingDO;

import java.util.List;

public interface BusinessDockingMapper {
    int insert(BusinessDockingDO record);

    int insertSelective(BusinessDockingDO record);

    /**
     * 通过业务订单号查询对接信息
     * @param businessOrderNoList
     * @return
     */
    List<BusinessDockingDO> findByBusinessOrderNoIn(@Param("businessOrderNoList") List<String> businessOrderNoList);

    /**
     * 通过业务订单号更新记录
     * @param updated
     * @return
     */
    int updateByBusinessOrderNo(@Param("updated")BusinessDockingDO updated);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int insertList(@Param("list")List<BusinessDockingDO> list);

    /**
     * 根据orgCode和ReagentStatus 数组查询记录
     * @param orgCode
     * @param reagentStatusCollection
     * @return
     */
    List<BusinessDockingDO> findByOrgCodeAndReagentStatusIn(@Param("orgCode")String orgCode, @Param("reagentStatusCollection")Collection<Integer> reagentStatusCollection);

    /**
     * 根据订单号批量更新记录
     * @param list
     * @return
     */
    int updateBatchByBusinessOrderNoIn(@Param("list")List<BusinessDockingDO> list);

}