<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderFundCardMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderFundCardDO">
    <!--@mbg.generated-->
    <!--@Table order_fund_card-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="fund_card_id" jdbcType="VARCHAR" property="fundCardId" />
    <result column="fund_card_code" jdbcType="VARCHAR" property="fundCardCode" />
    <result column="fund_card_no" jdbcType="VARCHAR" property="fundCardNo" />
    <result column="freeze_amount" jdbcType="DECIMAL" property="freezeAmount" />
    <result column="is_froze" jdbcType="INTEGER" property="froze" />
    <result column="sequence" jdbcType="INTEGER" property="sequence" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, fund_card_id, fund_card_code, fund_card_no, freeze_amount, is_froze,
    `sequence`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_fund_card
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from order_fund_card
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderFundCardDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_fund_card (order_id, fund_card_id, fund_card_code
    fund_card_no,freeze_amount, is_froze,
      `sequence`, create_time, update_time
      )
    values (#{orderId,jdbcType=INTEGER},
      #{fundCardId,jdbcType=VARCHAR}, #{fundCardCode,jdbcType=VARCHAR}, #{fundCardNo,jdbcType=VARCHAR}, #{freezeAmount,jdbcType=DECIMAL}, #{froze,jdbcType=INTEGER},
      #{sequence,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderFundCardDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_fund_card
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="fundCardId != null">
        fund_card_id,
      </if>
      <if test="fundCardCode != null">
        fund_card_code,
      </if>
      <if test="fundCardNo != null">
        fund_card_no,
      </if>
      <if test="freezeAmount != null">
        freeze_amount,
      </if>
      <if test="froze != null">
        is_froze,
      </if>
      <if test="sequence != null">
        `sequence`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="fundCardId != null">
        #{fundCardId,jdbcType=VARCHAR},
      </if>
      <if test="fundCardCode != null">
        #{fundCardCode,jdbcType=VARCHAR},
      </if>
      <if test="fundCardNo != null">
        #{fundCardNo,jdbcType=VARCHAR},
      </if>
      <if test="freezeAmount != null">
        #{freezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="froze != null">
        #{froze,jdbcType=INTEGER},
      </if>
      <if test="sequence != null">
        #{sequence,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderFundCardDO">
    <!--@mbg.generated-->
    update order_fund_card
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="fundCardId != null">
        fund_card_id = #{fundCardId,jdbcType=VARCHAR},
      </if>
      <if test="fundCardCode != null">
        fund_card_code = #{fundCardCode,jdbcType=VARCHAR},
      </if>
      <if test="fundCardNo != null">
        fund_card_no = #{fundCardNo,jdbcType=VARCHAR},
      </if>
      <if test="freezeAmount != null">
        freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="froze != null">
        froze = #{froze,jdbcType=INTEGER},
      </if>
      <if test="sequence != null">
        `sequence` = #{sequence,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.OrderFundCardDO">
    <!--@mbg.generated-->
    update order_fund_card
    set order_id = #{orderId,jdbcType=INTEGER},
      fund_card_id = #{fundCardId,jdbcType=VARCHAR},
      fund_card_code = #{fundCardCode,jdbcType=VARCHAR},
      fund_card_no = #{fundCardNo,jdbcType=VARCHAR},
      freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
      is_froze = #{froze,jdbcType=INTEGER},
      `sequence` = #{sequence,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-01-04-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO order_fund_card(
    order_id,
    fund_card_id,
    fund_card_code,
    fund_card_no,
    freeze_amount,
    is_froze,
    sequence,
    create_time,
    update_time
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.orderId,jdbcType=INTEGER},
      #{element.fundCardId,jdbcType=VARCHAR},
      #{element.fundCardCode,jdbcType=VARCHAR},
      #{element.fundCardNo,jdbcType=VARCHAR},
      #{element.freezeAmount,jdbcType=DECIMAL},
      #{element.froze,jdbcType=INTEGER},
      #{element.sequence,jdbcType=INTEGER},
      #{element.createTime,jdbcType=TIMESTAMP},
      #{element.updateTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2020-01-04-->
  <select id="findByOrderIdInDesc" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_fund_card
    where order_id in
    <foreach item="item" index="index" collection="orderIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    order by id desc
  </select>

<!--auto generated by MybatisCodeHelper on 2020-01-06-->
  <delete id="deleteByOrderIdIn">
    delete from order_fund_card
    where order_id in
    <foreach item="item" index="index" collection="orderIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </delete>
</mapper>