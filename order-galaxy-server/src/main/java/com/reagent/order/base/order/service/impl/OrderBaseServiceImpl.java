package com.reagent.order.base.order.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.reagent.order.base.order.dto.*;
import com.reagent.order.base.order.mapper.OrderBaseMapper;
import com.reagent.order.base.order.mapper.OrderExtraQueryMapper;
import com.reagent.order.base.order.model.OrderBase;
import com.reagent.order.base.order.model.OrderExtraQuery;
import com.reagent.order.base.order.service.OrderBaseService;
import com.reagent.order.base.order.translator.OrderBaseTranslator;
import com.reagent.order.base.rentcar.dto.BuyerRentcarOrderBaseParamDTO;
import com.reagent.order.base.rentcar.dto.SuppRentcarOrderBaseParamDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.sequence.api.service.IdGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @description: 网约车订单服务
 * @author: zhukai
 * @create: 2019-07-29 11:23
 **/
@Service
@CatAnnotation
public class OrderBaseServiceImpl implements OrderBaseService {

    private static final String CAT_TYPE = "RentcarOrderServiceImpl";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * id生成器 业务名称
     */
    private static final String BID_NAME = "order-galaxy";

    private static final String START_LOG = "进入==> {},入参{}";

    private static final String END_LOG = "结束==> {},结果{}";

    @MSharpReference(remoteAppkey = "pearl-service",token = "${BIZ_TOKEN}")
    private IdGenerator idGenerator;

    @Resource
    private OrderBaseMapper orderBaseMapper;

    @Resource
    private OrderExtraQueryMapper orderExtraQueryMapper;


    /**
     * 获取 超时 日期订单
     * @param orderBaseParamDTO  rucan
     * @return
     */
    @Override
    public OrderBasePageResultDTO getTimeoutNoticeOrder(OrderBaseParamDTO orderBaseParamDTO){
         return  pageInvoke(()->
                orderBaseMapper.getTimeOutNoticeOrder(orderBaseParamDTO.getOrderStatusList(),orderBaseParamDTO.getBusinessType()
                        , orderBaseParamDTO.getTimeOutDay()),orderBaseParamDTO.getPageNumber(),orderBaseParamDTO.getPageSize());
    }

    /**
     * 根据订单号查询订单
     * @param orderNumber 订单号
     * @return 订单基础集合
     */
    @Override
    public OrderBase findOrderBaseByOrderNumber(String orderNumber){
        return orderBaseMapper.findByOrderNumber(orderNumber);
    }

    /**
     * 新增OrderBase  和 OrderExtraQuery
     * @param orderExtraQueryDTO 入参
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrderBaseAndExtraQuery(OrderBaseAndExtrasDTO orderExtraQueryDTO){
        OrderBaseDTO orderBaseDTO = orderExtraQueryDTO.getOrderBaseDTO();
        List<OrderExtraQueryDTO> extraQueryDTOList = orderExtraQueryDTO.getExtraQueryDTOList();
        List<OrderExtraQuery> orderExtraQueryList = extraQueryDTOList.stream().map(OrderBaseTranslator::dtoToOrderExtraQuery).collect(Collectors.toList());
        orderExtraQueryMapper.batchInsert(orderExtraQueryList);
        OrderBase orderBase = OrderBaseTranslator.dtoToOrderBase(orderBaseDTO);
        orderBaseMapper.insertSelective(orderBase);
    }


    /**
     * 根据主键 用非空值 更新订单
     *
     * @param orderBaseDTO 入参
     * @return 更新行数
     */
    @Override
    public Integer updateOrderBaseById(OrderBaseDTO orderBaseDTO) {
        final String methodName = "updateOrderBaseById";
        logger.info(START_LOG, methodName, orderBaseDTO.toString());
        OrderBase orderBase = OrderBaseTranslator.dtoToOrderBase(orderBaseDTO);
        int result = orderBaseMapper.updateByPrimaryKeySelective(orderBase);
        logger.info(END_LOG, methodName, result);
        return result;
    }

    /**
     * 根据用户id 和 订单状态查询订单条数
     *
     * @param orderBaseParamDTO 入参
     * @return count
     */
    @Override
    public RemoteResponse<Integer> countByBuyerIdAndStatus(OrderBaseParamDTO orderBaseParamDTO) {
        final String methodName = "countByBuyerIdAndStatus";
        logger.info(START_LOG, methodName, orderBaseParamDTO.toString());
        Long buyerId = orderBaseParamDTO.getBuyerId();
        Integer orderStatus = orderBaseParamDTO.getOrderStatus();
        if (buyerId == null) {
            return RemoteResponse.<Integer>custom().setIllegalParameter("buyerId不能为空！").build();
        }
        if (orderStatus == null) {
            return RemoteResponse.<Integer>custom().setIllegalParameter("orderStatus不能为空！").build();
        }

        Integer resultCount = orderBaseMapper.countByBuyerIdAndOrderStatus(buyerId, orderStatus);
        logger.info(END_LOG, methodName, resultCount);
        return RemoteResponse.<Integer>custom().setSuccess().setData(resultCount).build();
    }

    /**
     * 更新 订单状态
     *
     * @param orderBaseParamDTO
     * @return
     */
    @Override
    public RemoteResponse updateOrderBaseStatus(OrderBaseParamDTO orderBaseParamDTO) {

        final String methodName = "updateOrderBaseStatus";
        logger.info(START_LOG, methodName, orderBaseParamDTO.toString());
        Long orderId = orderBaseParamDTO.getOrderId();
        Integer orderStatus = orderBaseParamDTO.getOrderStatus();
        if (orderId == null) {
            return RemoteResponse.custom().setIllegalParameter("orderId不能为空！").build();
        }
        if (orderStatus == null) {
            return RemoteResponse.custom().setIllegalParameter("orderStatus不能为空！").build();
        }
        int result = orderBaseMapper.updateOrderStatusById(orderStatus, orderId);
        logger.info(END_LOG, methodName, result);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 根据 BusinessType 生成订单号
     *
     * @return 订单号
     */
    @Override
    public RemoteResponse<Long> getOrderId() {

        final String methodName = "getOrderId";
        logger.info(START_LOG, methodName, "");
        //生成订单编号
        RemoteResponse<Long> response = null;
        try {
            response = idGenerator.nextId(BID_NAME);
            if (!response.isSuccess()) {
                return RemoteResponse.<Long> custom().setFailure("生成订单id失败！").build();
            }
            Long generateId = response.getData();
            return RemoteResponse.<Long> custom().setSuccess().setData(generateId).build();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "idGenerator.nextId", "生成订单序列号异常", e);
            logger.error("生成订单序列号异常", e);
            return RemoteResponse.<Long> custom().setFailure("生成订单序列号异常！" + e.getMessage()).build();
        }
    }

    /**
     * 新增orderBase
     *
     * @param orderBaseDTO 入参
     * @return orderNumber 订单编号
     */
    @Override
    public RemoteResponse createOrderBase(OrderBaseDTO orderBaseDTO) {

        final String methodName = "createOrderBase";
        logger.info(START_LOG, methodName, orderBaseDTO.toString());

        OrderBase orderBase = OrderBaseTranslator.dtoToOrderBase(orderBaseDTO);
        orderBaseMapper.insertSelective(orderBase);
        return RemoteResponse.custom().setSuccess().build();
    }


    /**
     * 供应商 网约车 orderBase 列表
     *
     * @param suppRentcarParamDTO 入参
     * @return SuppRentcarOrderBaseResultDTO
     */
    @Override
    public RemoteResponse<OrderBasePageResultDTO> rentcarOrderBaseListForSupp(SuppRentcarOrderBaseParamDTO suppRentcarParamDTO) {

        final String methodName = "rentcarOrderBaseListForSupp";
        logger.info(START_LOG, methodName, suppRentcarParamDTO.toString());

        Long supplierId = suppRentcarParamDTO.getSupplierId();
        Integer businessType = suppRentcarParamDTO.getBusinessType();

        //参数校验
        if (supplierId == null) {
            return RemoteResponse.<OrderBasePageResultDTO>custom().setIllegalParameter("supplierId不能为空！").build();
        }

        if (businessType == null) {
            return RemoteResponse.<OrderBasePageResultDTO>custom().setIllegalParameter("businessType不能为空！").build();
        }
        if (suppRentcarParamDTO.getPageNumber() == null) {
            return RemoteResponse.<OrderBasePageResultDTO>custom().setIllegalParameter("pageNumber不能为空！").build();
        }
        if (suppRentcarParamDTO.getPageSize() == null) {
            return RemoteResponse.<OrderBasePageResultDTO>custom().setIllegalParameter("pageSize不能为空！").build();
        }

        OrderBasePageResultDTO resultDTO;
        try {
            resultDTO = pageInvoke(()->orderBaseMapper.findRentcarOrderForSupp(suppRentcarParamDTO)
                    ,suppRentcarParamDTO.getPageNumber(), suppRentcarParamDTO.getPageSize());
            logger.info(END_LOG, methodName, resultDTO.toString());
            return RemoteResponse.<OrderBasePageResultDTO>custom().setSuccess().setData(resultDTO).build();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, methodName, "查询orderBase异常", e);
            logger.error("查询orderBase异常", e);
            return RemoteResponse.<OrderBasePageResultDTO>custom().setFailure("查询失败" + e.getMessage()).build();
        }

    }

    /**
     * 获取行程记录列表
     * @param buyerParamDTO 入参
     * @return OrderBasePageResultDTO
     */
    @Override
    public OrderBasePageResultDTO getTravelRecordList(BuyerRentcarOrderBaseParamDTO buyerParamDTO){
        logger.info(START_LOG, "getTravelRecordList", buyerParamDTO.toString());
        Long buyerId = buyerParamDTO.getBuyerId();
        Integer businessType = buyerParamDTO.getBusinessType();
        OrderBasePageResultDTO resultDTO = pageInvoke(() -> orderBaseMapper.getTravelRecordList(buyerId, businessType)
                , buyerParamDTO.getPageNumber(), buyerParamDTO.getPageSize());
        logger.info(END_LOG, "getTravelRecordList", resultDTO.toString());
        return resultDTO;
    }

    /**
     * 采购人 网约车 orderBase 列表
     *
     * @param buyerParamDTO 入参
     * @return SuppRentcarOrderBaseResultDTO
     */
    @Override
    public RemoteResponse<OrderBasePageResultDTO> rentcarOrderBaseListForBuyer(BuyerRentcarOrderBaseParamDTO buyerParamDTO) {

        final String methodName = "rentcarOrderBaseListForSupp";
        logger.info(START_LOG, methodName, buyerParamDTO.toString());

        Long buyerId = buyerParamDTO.getBuyerId();
        List<Long> piDepartmentIds = buyerParamDTO.getPiDepartmentIds();
        Integer businessType = buyerParamDTO.getBusinessType();

        //参数校验
        if (buyerId == null) {
            return RemoteResponse.<OrderBasePageResultDTO>custom().setIllegalParameter("buyerId不能为空！").build();
        }
        if (businessType == null) {
            return RemoteResponse.<OrderBasePageResultDTO>custom().setIllegalParameter("businessType不能为空！").build();
        }
        if (buyerParamDTO.getPageNumber() == null) {
            return RemoteResponse.<OrderBasePageResultDTO>custom().setIllegalParameter("pageNumber不能为空！").build();
        }
        if (buyerParamDTO.getPageSize() == null) {
            return RemoteResponse.<OrderBasePageResultDTO>custom().setIllegalParameter("pageSize不能为空！").build();
        }

        OrderBasePageResultDTO resultDTO;
        try {
            resultDTO = pageInvoke(()-> orderBaseMapper.findRentcarOrderForBuyer(buyerId, piDepartmentIds, businessType
                    , buyerParamDTO.getStarTime(), buyerParamDTO.getEndTime(), buyerParamDTO.getSearchContext()),buyerParamDTO.getPageNumber(), buyerParamDTO.getPageSize());
            return RemoteResponse.<OrderBasePageResultDTO>custom().setSuccess().setData(resultDTO).build();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, methodName, "查询orderBase异常", e);
            logger.error("查询orderBase异常", e);
            return RemoteResponse.<OrderBasePageResultDTO>custom().setFailure("查询失败" + e.getMessage()).build();
        }
    }

    /**
     * 根据 id 查询 orderBase
     *
     * @return
     */
    @Override
    public RemoteResponse<OrderBaseDTO> findOrderBaseById(OrderBaseParamDTO orderBaseParamDTO) {

        Long orderId = orderBaseParamDTO.getOrderId();

        final String methodName = "findOrderBaseById";
        logger.info(START_LOG, methodName, orderId);

        if (orderId == null) {
            return RemoteResponse.<OrderBaseDTO>custom().setIllegalParameter("orderId不能为空！").build();
        }

        try {
            OrderBase orderBase = orderBaseMapper.selectByPrimaryKey(orderId);
            if (orderBase == null) {
                return RemoteResponse.<OrderBaseDTO>custom().setFailure("查询不到订单"+orderId).build();
            }
            OrderBaseDTO orderBaseDTO = OrderBaseTranslator.orderBaseToDTO(orderBase);
            logger.info("结果：{}", orderBaseDTO.toString());
            return RemoteResponse.<OrderBaseDTO>custom().setSuccess().setData(orderBaseDTO).build();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, methodName, "查询orderBase异常", e);
            logger.error("查询orderBase异常", e);
            return RemoteResponse.<OrderBaseDTO>custom().setFailure("查询失败orderBase" + e.getMessage()).build();
        }
    }

    /**
     * 根据订单集合 查询订单列表
     *
     * @param orderBaseParamDTO
     * @return
     */
    @Override
    public RemoteResponse<OrderBaseResultDTO> findOrderBaseByIdList(OrderBaseParamDTO orderBaseParamDTO) {

        List<Long> orderIdList = orderBaseParamDTO.getOrderIdList();

        final String methodName = "findOrderBaseByIdList";
        logger.info(START_LOG, methodName, orderIdList.size());

        if (CollectionUtils.isEmpty(orderIdList)) {
            return RemoteResponse.<OrderBaseResultDTO>custom().setIllegalParameter("orderIdList不能为空！").build();
        }

        try {
            List<OrderBase> orderBaseList = orderBaseMapper.findByIdIn(orderIdList);
            List<OrderBaseDTO> resultList = orderBaseList.stream().map(OrderBaseTranslator::orderBaseToDTO).collect(Collectors.toList());
            OrderBaseResultDTO orderBaseResultDTO = new OrderBaseResultDTO();
            orderBaseResultDTO.setOrderBaseDTOList(resultList);
            logger.info("结果：{}条", resultList.size());
            return RemoteResponse.<OrderBaseResultDTO>custom().setSuccess().setData(orderBaseResultDTO).build();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, methodName, "查询orderBase异常", e);
            logger.error("查询orderBase异常", e);
            return RemoteResponse.<OrderBaseResultDTO>custom().setFailure("查询失败" + e.getMessage()).build();
        }
    }

    /**
     * 根据采购人 和 订单状态集合查询订单
     *
     * @param buyerId         用户id
     * @param orderStatusList 订单状态集合
     * @param businessType    业务类型
     * @param size            大小
     * @return orderBase 订单集合
     */
    @Override
    public List<OrderBase> findOrdersByStatusAndBuyerId(Long buyerId, List<Integer> orderStatusList, Integer businessType, Integer size) {
        return orderBaseMapper.findByBuyerIdAndOrderStatusList(buyerId, orderStatusList, businessType, size);
    }

    /**
     * 查询公示栏 订单
     *
     * @param publicityOrderParamDTO orderStatusList 订单状态集合
     * @return
     */
    @Override
    public OrderBasePageResultDTO findPublicityOrder(PublicityOrderParamDTO publicityOrderParamDTO) {
        final String methodName = "findPublicityOrder";
        logger.info(START_LOG, methodName, publicityOrderParamDTO.toString());
        OrderBasePageResultDTO resultDTO = pageInvoke(() -> orderBaseMapper.findPublicityOrder(publicityOrderParamDTO), publicityOrderParamDTO.getPageNumber(), publicityOrderParamDTO.getPageSize());
        logger.info(END_LOG, methodName, resultDTO.toString());
        return resultDTO;
    }

    /**
     * 分页方法 包装
     * @param supplier    需要分页的方法
     * @param pageNumber  页面参数
     * @param pageSize    页面大小
     * @return OrderBasePageResultDTO
     */
    private OrderBasePageResultDTO pageInvoke(Supplier<List<OrderBase>> supplier, Integer pageNumber , Integer pageSize) {

        PageHelper.startPage(pageNumber, pageSize);
        List<OrderBase> resultList = supplier.get();
        PageInfo<OrderBase> pageInfo = new PageInfo<>(resultList);
        List<OrderBase> orderBaseList = pageInfo.getList();
        List<OrderBaseDTO> orderBaseDTOList = orderBaseList.stream().map(OrderBaseTranslator::orderBaseToDTO).collect(Collectors.toList());
        OrderBasePageResultDTO resultDTO = new OrderBasePageResultDTO();
        resultDTO.setOrderBaseDTOList(orderBaseDTOList);
        resultDTO.setPageNumber(pageInfo.getPageNum());
        resultDTO.setPageSize(pageInfo.getPageSize());
        resultDTO.setTotalNum(pageInfo.getTotal());
        return resultDTO;
    }
}
