<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.log.mapper.OrderDockingLogMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.log.model.OrderDockingLog">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="docking_number" jdbcType="VARCHAR" property="dockingNumber" />
    <result column="param_info" jdbcType="VARCHAR" property="paramInfo" />
    <result column="extra_info" jdbcType="VARCHAR" property="extraInfo" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, docking_number, param_info, extra_info, `operation`, org_code, `result`, remark, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_docking_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from order_docking_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.log.model.OrderDockingLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_docking_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dockingNumber != null">
        docking_number,
      </if>
      <if test="paramInfo != null">
        param_info,
      </if>
      <if test="extraInfo != null">
        extra_info,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="result != null">
        `result`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dockingNumber != null">
        #{dockingNumber,jdbcType=VARCHAR},
      </if>
      <if test="paramInfo != null">
        #{paramInfo,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.log.model.OrderDockingLog">
    <!--@mbg.generated-->
    update order_docking_log
    <set>
      <if test="dockingNumber != null">
        docking_number = #{dockingNumber,jdbcType=VARCHAR},
      </if>
      <if test="paramInfo != null">
        param_info = #{paramInfo,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null">
        extra_info = #{extraInfo,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        org_code = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        `result` = #{result,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>