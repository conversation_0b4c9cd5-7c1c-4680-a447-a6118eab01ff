package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderBankAccountSnapshotDTO;
import com.reagent.order.base.order.model.OrderBankAccountSnapshotDO;

/**
 * <AUTHOR>
 * @CreateTime 2023-11-29 18:45
 * @Description
 */
public class OrderBankAccountSnapshotTranslator {

    public static OrderBankAccountSnapshotDO dto2do(OrderBankAccountSnapshotDTO dto){
        OrderBankAccountSnapshotDO snapshotDO = new OrderBankAccountSnapshotDO();
        snapshotDO.setOrderId(dto.getOrderId());
        snapshotDO.setOrderNo(dto.getOrderNo());
        snapshotDO.setOrgId(dto.getOrgId());
        snapshotDO.setBankId(dto.getBankId());
        snapshotDO.setBankAccountName(dto.getBankAccountName());
        snapshotDO.setBankName(dto.getBankName());
        snapshotDO.setBankBranch(dto.getBankBranch());
        snapshotDO.setBankCode(dto.getBankCode());
        snapshotDO.setProvinceCode(dto.getProvinceCode());
        snapshotDO.setCityCode(dto.getCityCode());
        snapshotDO.setBankCardNumber(dto.getBankCardNumber());
        snapshotDO.setAccountType(dto.getAccountType());
        return snapshotDO;
    }

    public static OrderBankAccountSnapshotDTO do2dto(OrderBankAccountSnapshotDO snapshotDO){
        return new OrderBankAccountSnapshotDTO()
                .setOrderId(snapshotDO.getOrderId())
                .setOrderNo(snapshotDO.getOrderNo())
                .setOrgId(snapshotDO.getOrgId())
                .setBankId(snapshotDO.getBankId())
                .setBankAccountName(snapshotDO.getBankAccountName())
                .setBankName(snapshotDO.getBankName())
                .setBankBranch(snapshotDO.getBankBranch())
                .setBankCode(snapshotDO.getBankCode())
                .setProvinceCode(snapshotDO.getProvinceCode())
                .setCityCode(snapshotDO.getCityCode())
                .setBankCardNumber(snapshotDO.getBankCardNumber())
                .setAccountType(snapshotDO.getAccountType());
    }
}
