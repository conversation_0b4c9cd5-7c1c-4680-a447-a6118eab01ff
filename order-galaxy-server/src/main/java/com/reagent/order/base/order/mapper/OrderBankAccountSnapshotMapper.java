package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.OrderBankAccountSnapshotDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderBankAccountSnapshotMapper {

    int insertList(@Param("list") List<OrderBankAccountSnapshotDO> recordList);

    int updateList(@Param("list") List<OrderBankAccountSnapshotDO> recordList);

    List<OrderBankAccountSnapshotDO> selectByOrderIdList(@Param("orderIdList") List<Integer> orderIdList);

    int deleteByOrderIdList(@Param("orderIdList") List<Integer> orderIdList);
}