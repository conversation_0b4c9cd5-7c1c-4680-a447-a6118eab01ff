<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.ExportTemplateMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.ExportTemplateDO">
    <!--@mbg.generated-->
    <!--@Table export_template-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="share_status" jdbcType="INTEGER" property="shareStatus" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="template_json" jdbcType="VARCHAR" property="templateJson" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, user_type, org_id, share_status, template_name, template_json, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from export_template
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from export_template
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.ExportTemplateDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into export_template (user_id, user_type, org_id, 
      share_status, template_name, template_json, 
      create_time, update_time)
    values (#{userId,jdbcType=BIGINT}, #{userType,jdbcType=INTEGER}, #{orgId,jdbcType=INTEGER}, 
      #{shareStatus,jdbcType=INTEGER}, #{templateName,jdbcType=VARCHAR}, #{templateJson,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.ExportTemplateDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into export_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="shareStatus != null">
        share_status,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="templateJson != null">
        template_json,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="shareStatus != null">
        #{shareStatus,jdbcType=INTEGER},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateJson != null">
        #{templateJson,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.ExportTemplateDO">
    <!--@mbg.generated-->
    update export_template
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="shareStatus != null">
        share_status = #{shareStatus,jdbcType=INTEGER},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateJson != null">
        template_json = #{templateJson,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.ExportTemplateDO">
    <!--@mbg.generated-->
    update export_template
    set user_id = #{userId,jdbcType=BIGINT},
      user_type = #{userType,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=INTEGER},
      share_status = #{shareStatus,jdbcType=INTEGER},
      template_name = #{templateName,jdbcType=VARCHAR},
      template_json = #{templateJson,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2022-09-01-->
  <select id="selectByUserTypeAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from export_template
    where user_type=#{userType,jdbcType=INTEGER} and user_id=#{userId,jdbcType=BIGINT}
  </select>

  <select id="countByUserTypeAndUserId" resultType="java.lang.Integer">
    select
    count(*)
    from export_template
    where user_type=#{userType,jdbcType=INTEGER} and user_id=#{userId,jdbcType=BIGINT}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-01-10-->
  <select id="countByOrgIdAndShareStatus" resultType="java.lang.Integer">
    select count(1)
    from export_template
    where org_id=#{orgId,jdbcType=INTEGER} and share_status=#{shareStatus,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-01-10-->
  <select id="selectByOrgIdAndShareStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from export_template
    where org_id=#{orgId,jdbcType=INTEGER} and share_status=#{shareStatus,jdbcType=INTEGER}
  </select>
</mapper>