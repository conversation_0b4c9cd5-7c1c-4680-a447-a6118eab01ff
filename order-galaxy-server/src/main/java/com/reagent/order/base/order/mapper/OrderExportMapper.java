package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.dto.OrderExportDTO;
import com.reagent.order.base.order.model.OrderExportDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/20 0020 17:12
 * @Version 1.0
 * @Desc:描述
 */
public interface OrderExportMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(OrderExportDO record);

    int insertSelective(OrderExportDO record);

    OrderExportDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderExportDO record);

    int updateByPrimaryKey(OrderExportDO record);

    List<OrderExportDO> selectByExportDateBefore(@Param("maxExportDate") LocalDate maxExportDate);

    int deleteByIdIn(@Param("idCollection") Collection<Integer> idCollection);

    /**
     * @description: 通过userid、orgid、filetype列表（前三必选）、status、exportdate搜索导出条目
     * @date: 2021/1/12 11:17
     * @author: zengyanru
     * @param userId
     * @param orgId
     * @param fileTypeCollection
     * @param status
     * @param minExportDate
     * @param maxExportDate
     * @return java.util.List<com.reagent.order.base.order.dto.OrderExportDTO>
     */
    List<OrderExportDTO> selectOrderExportList(@Param("userId")Integer userId, @Param("orgId")Integer orgId, @Param("fileTypeCollection")Collection<Integer> fileTypeCollection, @Param("status")Integer status, @Param("minExportDate")Date minExportDate, @Param("maxExportDate")Date maxExportDate);
}