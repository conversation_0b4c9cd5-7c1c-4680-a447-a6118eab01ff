package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.OrderDetailAcceptanceFileDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderDetailAcceptanceFileMapper {

    /**
     * 根据订单ID删除关联附件信息
     *
     * @param orderId 订单ID
     */
    int deleteByOrderId(@Param("orderId") Integer orderId);

    /**
     * 根据订单ID查询详情关联附件信息
     *
     * @param orderId 订单ID
     */
    List<OrderDetailAcceptanceFileDO> selectByOrderId(@Param("orderId") Integer orderId);
    
    /**
     * 根据订单ID集合批量查询详情关联附件信息
     *
     * @param orderIds 订单ID集合
     * @return 详情关联附件信息列表
     */
    List<OrderDetailAcceptanceFileDO> selectByOrderIds(@Param("orderIds") List<Integer> orderIds);

    int insertSelective(OrderDetailAcceptanceFileDO record);


    int updateByPrimaryKeySelective(OrderDetailAcceptanceFileDO record);

    int batchInsert(@Param("list") List<OrderDetailAcceptanceFileDO> list);

}