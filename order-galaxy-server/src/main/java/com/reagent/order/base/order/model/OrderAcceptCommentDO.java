package com.reagent.order.base.order.model;

import java.util.Date;

public class OrderAcceptCommentDO {
    /**
    * 评价条目主键
    */
    private Integer id;

    /**
    * 订单验收评价id列表
    */
    private String acceptCommentTags;

    /**
    * 订单主表id
    */
    private Integer orderId;

    /**
    * 单位id
    */
    private Integer orgId;

    /**
    * 创建时间
    */
    private Date createdTime;

    /**
    * 修改时间
    */
    private Date updatedTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAcceptCommentTags() {
        return acceptCommentTags;
    }

    public void setAcceptCommentTags(String acceptCommentTags) {
        this.acceptCommentTags = acceptCommentTags;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", acceptCommentTags=").append(acceptCommentTags);
        sb.append(", orderId=").append(orderId);
        sb.append(", orgId=").append(orgId);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append("]");
        return sb.toString();
    }
}
