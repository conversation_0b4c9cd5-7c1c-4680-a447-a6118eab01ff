package com.reagent.order.base.order.mapper;
import java.util.Collection;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.reagent.order.base.order.model.OrderFundCardDO;

/**
 * <AUTHOR>
 */
public interface OrderFundCardMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(OrderFundCardDO record);

    int insertSelective(OrderFundCardDO record);

    OrderFundCardDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderFundCardDO record);

    int updateByPrimaryKey(OrderFundCardDO record);

    /**
     * 批量新增记录
     * @param list
     * @return
     */
    int insertList(@Param("list")List<OrderFundCardDO> list);

    /**
     * 通过订单id查询记录, 根据id倒序排序
     * @param orderIdCollection
     * @return
     */
    List<OrderFundCardDO> findByOrderIdInDesc(@Param("orderIdCollection")Collection<Integer> orderIdCollection);

    /**
     * 通过订单id批量删除
     * @param orderIdCollection
     * @return
     */
    int deleteByOrderIdIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection);
}