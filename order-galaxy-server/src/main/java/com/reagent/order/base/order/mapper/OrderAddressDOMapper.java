package com.reagent.order.base.order.mapper;
import java.util.Collection;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.request.OrderAddressRequestDTO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.reagent.order.base.order.model.OrderAddressDO;

/**
 * 订单地址mapper
 */
public interface OrderAddressDOMapper {

    /**
     * 批量新增
     * @param list
     * @return
     */
    int insertList(@Param("list")List<OrderAddressDO> list);

    /**
     * 根据id批量查询
     * @param idCollection
     * @return
     */
    List<OrderAddressDO> findByIdIn(@Param("idCollection")Collection<Integer> idCollection);

    /**
     * 更新订单地址
     * @param updated
     * @return
     */
    int updateById(@Param("updated")OrderAddressDO updated);

    /**
     * 查询是否存在记录
     * @param id
     * @return
     */
    Integer countById(@Param("id")Integer id);

    /**
     * 查询是否存在记录多个id
     * @param idCollection
     * @return
     */
    Integer countByIdIn(@Param("idCollection")Collection<Integer> idCollection);

    /**
     * 查询订单关联地址是否存在
     * @param orderNoCollection
     * @return
     */
    List<String> findByOrderNoIn(@Param("orderNoCollection")Collection<String> orderNoCollection);

    /**
     * 根据订单号批量更新地址信息
     * @param updatedList
     * @return
     */
    int batchUpdateByOrderNo(@Param("list")List<OrderAddressDO> updatedList);

    /**
     * 根据订单号查询地址信息
     * @param orderNo
     * @return
     */
    OrderAddressDO findByOrderNo(@Param("orderNo")String orderNo);

    /**
     * 查询操作过得代配送订单id
     * @param requestDTO
     * @return
     */
    List<OrderAddressDO> findOrderIdInOperatorIdAndDeliveryStatus(OrderAddressRequestDTO requestDTO);
}
