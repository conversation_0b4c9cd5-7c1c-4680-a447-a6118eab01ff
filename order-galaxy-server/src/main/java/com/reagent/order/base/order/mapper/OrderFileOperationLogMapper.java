package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.OrderFileOperationLogDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单文件操作日志Mapper接口
 */
public interface OrderFileOperationLogMapper {

    /**
     * 根据订单ID列表查询订单文件操作日志
     *
     * @param orderIds 订单ID列表
     * @return 订单文件操作日志列表
     */
    List<OrderFileOperationLogDO> selectByOrderIds(@Param("orderIds") List<Integer> orderIds);
    
    /**
     * 根据日志ID列表查询订单文件操作日志
     *
     * @param logIds 日志ID列表
     * @return 订单文件操作日志列表
     */
    List<OrderFileOperationLogDO> selectByLogIds(@Param("logIds") List<Integer> logIds);
    
    /**
     * 批量插入订单文件操作日志
     *
     * @param list 订单文件操作日志列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<OrderFileOperationLogDO> list);
}
