package com.reagent.order.base.order.translator;


import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.base.order.model.OrderFundCardDO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单关联经费卡转换类
 * <AUTHOR>
 */
public class OrderFundCardTranslator {

    private static final int INIT_INT = 0;

    /**
     * dto to do
     * @param dto
     * @return do
     */
    public static OrderFundCardDO dtoToDo(OrderFundCardDTO dto) {
        if (dto == null) {
            return null;
        }

        OrderFundCardDO result = new OrderFundCardDO();
        result.setId(dto.getId());
        result.setOrderId(dto.getOrderId() == null ? INIT_INT : dto.getOrderId());
        result.setFundCardId(dto.getFundCardId() == null ? "" : dto.getFundCardId());
        result.setFundCardCode(dto.getFundCardCode() == null ? "" : dto.getFundCardCode());
        result.setFundCardNo(dto.getFundCardNo() == null ? "" : dto.getFundCardNo());
        result.setFreezeAmount(dto.getFreezeAmount() == null ? new BigDecimal("0") : dto.getFreezeAmount());
        result.setSequence(dto.getSequence() == null ? INIT_INT : dto.getSequence());
        result.setCreateTime(dto.getCreateTime() == null ? new Date() : dto.getCreateTime());
        result.setUpdateTime(dto.getUpdateTime() == null ? new Date() : dto.getUpdateTime());
        result.setFroze(dto.getFroze() == null ? INIT_INT : dto.getFroze());

        return result;
    }

    /**
     * do to dto
     * @return
     */
    public static OrderFundCardDTO doToDto(OrderFundCardDO request) {
        OrderFundCardDTO dto = new OrderFundCardDTO();
        dto.setId(request.getId());
        dto.setOrderId(request.getOrderId());
        dto.setFundCardId(request.getFundCardId());
        dto.setFundCardCode(request.getFundCardCode());
        dto.setFundCardNo(request.getFundCardNo());
        dto.setFreezeAmount(request.getFreezeAmount());
        dto.setSequence(request.getSequence());
        dto.setCreateTime(request.getCreateTime());
        dto.setUpdateTime(request.getUpdateTime());
        dto.setFroze(request.getFroze());

        return dto;
    }
}
