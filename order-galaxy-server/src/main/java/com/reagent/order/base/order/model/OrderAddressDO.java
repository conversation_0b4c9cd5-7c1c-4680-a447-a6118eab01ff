package com.reagent.order.base.order.model;

import java.util.Date;
import java.util.StringJoiner;

public class OrderAddressDO {
    /**
    * 这个是订单id，不做自动递增
    */
    private Integer id;

    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 收货人
    */
    private String receiverName;

    /**
    * 收货人电话
    */
    private String receiverPhone;

    /**
    * 省份
    */
    private String province;

    /**
    * 城市
    */
    private String city;

    /**
    * 市辖区域/县
    */
    private String region;

    /**
    * 详细地址
    */
    private String address;

    /**
     * 地址标签
     */
    private String label;

    /**
     * 配送类型
     */
    private Integer deliveryType;

    /**
    * 代收人
    */
    private String receiverNameProxy;

    /**
    * 代收人电话
    */
    private String receiverPhoneProxy;

    /**
    * 代配送省份
    */
    private String provinceProxy;

    /**
    * 代配送城市
    */
    private String cityProxy;

    /**
    * 代配送市辖区/县
    */
    private String regionProxy;

    /**
    * 代配送详细地址
    */
    private String addressProxy;

    /**
    * create_time
    */
    private Date createTime;

    /**
    * update_time
    */
    private Date updateTime;

    /**
     * 配送坐标
     */
    private String geo;

    /**
     * 代配送坐标
     */
    private String geoProxy;

    /**
     * 代配送状态
     */
    private Integer deliveryStatus;

    /**
     * 分拣员
     */
    private String sortedUser;

    /**
     * 配送员
     */
    private String deliveryUser;

    /**
     * 代配送来源类型，0-无、1-商家、2采购人
     */
    private Integer proxySourceType;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getReceiverNameProxy() {
        return receiverNameProxy;
    }

    public void setReceiverNameProxy(String receiverNameProxy) {
        this.receiverNameProxy = receiverNameProxy;
    }

    public String getReceiverPhoneProxy() {
        return receiverPhoneProxy;
    }

    public void setReceiverPhoneProxy(String receiverPhoneProxy) {
        this.receiverPhoneProxy = receiverPhoneProxy;
    }

    public String getProvinceProxy() {
        return provinceProxy;
    }

    public void setProvinceProxy(String provinceProxy) {
        this.provinceProxy = provinceProxy;
    }

    public String getCityProxy() {
        return cityProxy;
    }

    public void setCityProxy(String cityProxy) {
        this.cityProxy = cityProxy;
    }

    public String getRegionProxy() {
        return regionProxy;
    }

    public void setRegionProxy(String regionProxy) {
        this.regionProxy = regionProxy;
    }

    public String getAddressProxy() {
        return addressProxy;
    }

    public void setAddressProxy(String addressProxy) {
        this.addressProxy = addressProxy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getGeo() {
        return geo;
    }

    public void setGeo(String geo) {
        this.geo = geo;
    }

    public String getGeoProxy() {
        return geoProxy;
    }

    public void setGeoProxy(String geoProxy) {
        this.geoProxy = geoProxy;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getSortedUser() {
        return sortedUser;
    }

    public void setSortedUser(String sortedUser) {
        this.sortedUser = sortedUser;
    }

    public String getDeliveryUser() {
        return deliveryUser;
    }

    public void setDeliveryUser(String deliveryUser) {
        this.deliveryUser = deliveryUser;
    }

    public Integer getProxySourceType() {
        return proxySourceType;
    }

    public void setProxySourceType(Integer proxySourceType) {
        this.proxySourceType = proxySourceType;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderAddressDO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("orderNo='" + orderNo + "'")
                .add("receiverName='" + receiverName + "'")
                .add("receiverPhone='" + receiverPhone + "'")
                .add("province='" + province + "'")
                .add("city='" + city + "'")
                .add("region='" + region + "'")
                .add("address='" + address + "'")
                .add("label='" + label + "'")
                .add("deliveryType=" + deliveryType)
                .add("receiverNameProxy='" + receiverNameProxy + "'")
                .add("receiverPhoneProxy='" + receiverPhoneProxy + "'")
                .add("provinceProxy='" + provinceProxy + "'")
                .add("cityProxy='" + cityProxy + "'")
                .add("regionProxy='" + regionProxy + "'")
                .add("addressProxy='" + addressProxy + "'")
                .add("createTime=" + createTime)
                .add("updateTime=" + updateTime)
                .add("geo='" + geo + "'")
                .add("geoProxy='" + geoProxy + "'")
                .add("deliveryStatus=" + deliveryStatus)
                .add("sortedUser='" + sortedUser + "'")
                .add("deliveryUser='" + deliveryUser + "'")
                .add("proxySourceType=" + proxySourceType)
                .toString();
    }
}
