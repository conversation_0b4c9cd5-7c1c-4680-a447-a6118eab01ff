<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderBaseMapper">
    <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderBase">
        <!--@mbg.generated-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="parent_Number" jdbcType="VARCHAR" property="parentNumber"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="source_id" jdbcType="BIGINT" property="sourceId"/>
        <result column="source_number" jdbcType="VARCHAR" property="sourceNumber"/>
        <result column="buyer_id" jdbcType="BIGINT" property="buyerId"/>
        <result column="buyer_name" jdbcType="VARCHAR" property="buyerName"/>
        <result column="buyer_phone" jdbcType="VARCHAR" property="buyerPhone"/>
        <result column="buyer_email" jdbcType="VARCHAR" property="buyerEmail"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="org_code" jdbcType="VARCHAR" property="orgCode"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="department_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="college_id" jdbcType="BIGINT" property="collegeId"/>
        <result column="college_name" jdbcType="VARCHAR" property="collegeName"/>
        <result column="business_type" jdbcType="INTEGER" property="businessType"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="actual_amount" jdbcType="BIGINT" property="actualAmount"/>
        <result column="original_price" jdbcType="BIGINT" property="originalPrice"/>
        <result column="discounts_amount" jdbcType="BIGINT" property="discountsAmount"/>
        <result column="process_type" jdbcType="INTEGER" property="processType"/>
        <result column="description_supplier" jdbcType="VARCHAR" property="descriptionSupplier"/>
        <result column="description_buyer" jdbcType="VARCHAR" property="descriptionBuyer"/>
        <result column="supplier_id" jdbcType="BIGINT" property="supplierId"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_email" jdbcType="VARCHAR" property="supplierEmail"/>
        <result column="supplier_phone" jdbcType="VARCHAR" property="supplierPhone"/>
        <result column="carry_fee" jdbcType="BIGINT" property="carryFee"/>
        <result column="receiver_name" jdbcType="VARCHAR" property="receiverName"/>
        <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone"/>
        <result column="receiver_address" jdbcType="VARCHAR" property="receiverAddress"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, order_number, parent_id, parent_Number, source_type, source_id, source_number,
        buyer_id, buyer_name, buyer_phone, buyer_email, org_id, org_code, org_name, department_id,
        department_name, college_id, college_name, business_type, order_status, actual_amount,
        original_price, discounts_amount, process_type, description_supplier, description_buyer,
        supplier_id, supplier_name, supplier_code, supplier_email, supplier_phone, carry_fee,
        receiver_name, receiver_phone, receiver_address, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from order_base
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from order_base
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.reagent.order.base.order.model.OrderBase">
        <!--@mbg.generated-->
        insert into order_base (id, order_number, parent_id,
        parent_Number, source_type, source_id,
        source_number, buyer_id, buyer_name,
        buyer_phone, buyer_email, org_id,
        org_code, org_name, department_id,
        department_name, college_id, college_name,
        business_type, order_status, actual_amount,
        original_price, discounts_amount, process_type,
        description_supplier, description_buyer,
        supplier_id, supplier_name, supplier_code,
        supplier_email, supplier_phone, carry_fee,
        receiver_name, receiver_phone, receiver_address,
        create_time, update_time)
        values (#{id,jdbcType=BIGINT}, #{orderNumber,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},
        #{parentNumber,jdbcType=VARCHAR}, #{sourceType,jdbcType=INTEGER}, #{sourceId,jdbcType=BIGINT},
        #{sourceNumber,jdbcType=VARCHAR}, #{buyerId,jdbcType=BIGINT}, #{buyerName,jdbcType=VARCHAR},
        #{buyerPhone,jdbcType=VARCHAR}, #{buyerEmail,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT},
        #{orgCode,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{departmentId,jdbcType=BIGINT},
        #{departmentName,jdbcType=VARCHAR}, #{collegeId,jdbcType=BIGINT}, #{collegeName,jdbcType=VARCHAR},
        #{businessType,jdbcType=INTEGER}, #{orderStatus,jdbcType=INTEGER}, #{actualAmount,jdbcType=BIGINT},
        #{originalPrice,jdbcType=BIGINT}, #{discountsAmount,jdbcType=BIGINT}, #{processType,jdbcType=INTEGER},
        #{descriptionSupplier,jdbcType=VARCHAR}, #{descriptionBuyer,jdbcType=VARCHAR},
        #{supplierId,jdbcType=BIGINT}, #{supplierName,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR},
        #{supplierEmail,jdbcType=VARCHAR}, #{supplierPhone,jdbcType=VARCHAR}, #{carryFee,jdbcType=BIGINT},
        #{receiverName,jdbcType=VARCHAR}, #{receiverPhone,jdbcType=VARCHAR}, #{receiverAddress,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.reagent.order.base.order.model.OrderBase">
        <!--@mbg.generated-->
        insert into order_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orderNumber != null">
                order_number,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="parentNumber != null">
                parent_Number,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="sourceId != null">
                source_id,
            </if>
            <if test="sourceNumber != null">
                source_number,
            </if>
            <if test="buyerId != null">
                buyer_id,
            </if>
            <if test="buyerName != null">
                buyer_name,
            </if>
            <if test="buyerPhone != null">
                buyer_phone,
            </if>
            <if test="buyerEmail != null">
                buyer_email,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="orgCode != null">
                org_code,
            </if>
            <if test="orgName != null">
                org_name,
            </if>
            <if test="departmentId != null">
                department_id,
            </if>
            <if test="departmentName != null">
                department_name,
            </if>
            <if test="collegeId != null">
                college_id,
            </if>
            <if test="collegeName != null">
                college_name,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="actualAmount != null">
                actual_amount,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="discountsAmount != null">
                discounts_amount,
            </if>
            <if test="processType != null">
                process_type,
            </if>
            <if test="descriptionSupplier != null">
                description_supplier,
            </if>
            <if test="descriptionBuyer != null">
                description_buyer,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="supplierName != null">
                supplier_name,
            </if>
            <if test="supplierCode != null">
                supplier_code,
            </if>
            <if test="supplierEmail != null">
                supplier_email,
            </if>
            <if test="supplierPhone != null">
                supplier_phone,
            </if>
            <if test="carryFee != null">
                carry_fee,
            </if>
            <if test="receiverName != null">
                receiver_name,
            </if>
            <if test="receiverPhone != null">
                receiver_phone,
            </if>
            <if test="receiverAddress != null">
                receiver_address,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                #{orderNumber,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="parentNumber != null and parentNumber != ''">
                #{parentNumber,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceId != null">
                #{sourceId,jdbcType=BIGINT},
            </if>
            <if test="sourceNumber != null and sourceNumber != ''">
                #{sourceNumber,jdbcType=VARCHAR},
            </if>
            <if test="buyerId != null">
                #{buyerId,jdbcType=BIGINT},
            </if>
            <if test="buyerName != null and buyerName != ''">
                #{buyerName,jdbcType=VARCHAR},
            </if>
            <if test="buyerPhone != null and buyerPhone != ''">
                #{buyerPhone,jdbcType=VARCHAR},
            </if>
            <if test="buyerEmail != null and buyerEmail != ''">
                #{buyerEmail,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=BIGINT},
            </if>
            <if test="orgCode != null and orgCode != ''">
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null and orgName != ''">
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="departmentId != null">
                #{departmentId,jdbcType=BIGINT},
            </if>
            <if test="departmentName != null and departmentName != ''">
                #{departmentName,jdbcType=VARCHAR},
            </if>
            <if test="collegeId != null">
                #{collegeId,jdbcType=BIGINT},
            </if>
            <if test="collegeName != null and collegeName != ''">
                #{collegeName,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="actualAmount != null">
                #{actualAmount,jdbcType=BIGINT},
            </if>
            <if test="originalPrice != null">
                #{originalPrice,jdbcType=BIGINT},
            </if>
            <if test="discountsAmount != null">
                #{discountsAmount,jdbcType=BIGINT},
            </if>
            <if test="processType != null">
                #{processType,jdbcType=INTEGER},
            </if>
            <if test="descriptionSupplier != null and descriptionSupplier != ''">
                #{descriptionSupplier,jdbcType=VARCHAR},
            </if>
            <if test="descriptionBuyer != null and descriptionBuyer != ''">
                #{descriptionBuyer,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                #{supplierId,jdbcType=BIGINT},
            </if>
            <if test="supplierName != null and supplierName != ''">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null and supplierCode != ''">
                #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierEmail != null and supplierEmail != ''">
                #{supplierEmail,jdbcType=VARCHAR},
            </if>
            <if test="supplierPhone != null and supplierPhone != ''">
                #{supplierPhone,jdbcType=VARCHAR},
            </if>
            <if test="carryFee != null">
                #{carryFee,jdbcType=BIGINT},
            </if>
            <if test="receiverName != null and receiverName != ''">
                #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="receiverPhone != null and receiverPhone != ''">
                #{receiverPhone,jdbcType=VARCHAR},
            </if>
            <if test="receiverAddress != null and receiverAddress != ''">
                #{receiverAddress,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderBase">
        <!--@mbg.generated-->
        update order_base
        <set>
            <if test="orderNumber != null and orderNumber != ''">
                order_number = #{orderNumber,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="parentNumber != null and parentNumber != ''">
                parent_Number = #{parentNumber,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=INTEGER},
            </if>
            <if test="sourceId != null">
                source_id = #{sourceId,jdbcType=BIGINT},
            </if>
            <if test="sourceNumber != null and sourceNumber != ''">
                source_number = #{sourceNumber,jdbcType=VARCHAR},
            </if>
            <if test="buyerId != null">
                buyer_id = #{buyerId,jdbcType=BIGINT},
            </if>
            <if test="buyerName != null and buyerName != ''">
                buyer_name = #{buyerName,jdbcType=VARCHAR},
            </if>
            <if test="buyerPhone != null and buyerPhone != ''">
                buyer_phone = #{buyerPhone,jdbcType=VARCHAR},
            </if>
            <if test="buyerEmail != null and buyerEmail != ''">
                buyer_email = #{buyerEmail,jdbcType=VARCHAR},
            </if>
            <if test="orgId != null">
                org_id = #{orgId,jdbcType=BIGINT},
            </if>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="departmentId != null">
                department_id = #{departmentId,jdbcType=BIGINT},
            </if>
            <if test="departmentName != null and departmentName != ''">
                department_name = #{departmentName,jdbcType=VARCHAR},
            </if>
            <if test="collegeId != null">
                college_id = #{collegeId,jdbcType=BIGINT},
            </if>
            <if test="collegeName != null and collegeName != ''">
                college_name = #{collegeName,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="actualAmount != null">
                actual_amount = #{actualAmount,jdbcType=BIGINT},
            </if>
            <if test="originalPrice != null">
                original_price = #{originalPrice,jdbcType=BIGINT},
            </if>
            <if test="discountsAmount != null">
                discounts_amount = #{discountsAmount,jdbcType=BIGINT},
            </if>
            <if test="processType != null">
                process_type = #{processType,jdbcType=INTEGER},
            </if>
            <if test="descriptionSupplier != null and descriptionSupplier != ''">
                description_supplier = #{descriptionSupplier,jdbcType=VARCHAR},
            </if>
            <if test="descriptionBuyer != null and descriptionBuyer != ''">
                description_buyer = #{descriptionBuyer,jdbcType=VARCHAR},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId,jdbcType=BIGINT},
            </if>
            <if test="supplierName != null and supplierName != ''">
                supplier_name = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null and supplierCode != ''">
                supplier_code = #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierEmail != null and supplierEmail != ''">
                supplier_email = #{supplierEmail,jdbcType=VARCHAR},
            </if>
            <if test="supplierPhone != null and supplierPhone != ''">
                supplier_phone = #{supplierPhone,jdbcType=VARCHAR},
            </if>
            <if test="carryFee != null">
                carry_fee = #{carryFee,jdbcType=BIGINT},
            </if>
            <if test="receiverName != null and receiverName != '' ">
                receiver_name = #{receiverName,jdbcType=VARCHAR},
            </if>
            <if test="receiverPhone != null and receiverPhone != '' ">
                receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
            </if>
            <if test="receiverAddress != null and receiverAddress != ''">
                receiver_address = #{receiverAddress,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.OrderBase">
        <!--@mbg.generated-->
        update order_base
        set order_number = #{orderNumber,jdbcType=VARCHAR},
        parent_id = #{parentId,jdbcType=BIGINT},
        parent_Number = #{parentNumber,jdbcType=VARCHAR},
        source_type = #{sourceType,jdbcType=INTEGER},
        source_id = #{sourceId,jdbcType=BIGINT},
        source_number = #{sourceNumber,jdbcType=VARCHAR},
        buyer_id = #{buyerId,jdbcType=BIGINT},
        buyer_name = #{buyerName,jdbcType=VARCHAR},
        buyer_phone = #{buyerPhone,jdbcType=VARCHAR},
        buyer_email = #{buyerEmail,jdbcType=VARCHAR},
        org_id = #{orgId,jdbcType=BIGINT},
        org_code = #{orgCode,jdbcType=VARCHAR},
        org_name = #{orgName,jdbcType=VARCHAR},
        department_id = #{departmentId,jdbcType=BIGINT},
        department_name = #{departmentName,jdbcType=VARCHAR},
        college_id = #{collegeId,jdbcType=BIGINT},
        college_name = #{collegeName,jdbcType=VARCHAR},
        business_type = #{businessType,jdbcType=INTEGER},
        order_status = #{orderStatus,jdbcType=INTEGER},
        actual_amount = #{actualAmount,jdbcType=BIGINT},
        original_price = #{originalPrice,jdbcType=BIGINT},
        discounts_amount = #{discountsAmount,jdbcType=BIGINT},
        process_type = #{processType,jdbcType=INTEGER},
        description_supplier = #{descriptionSupplier,jdbcType=VARCHAR},
        description_buyer = #{descriptionBuyer,jdbcType=VARCHAR},
        supplier_id = #{supplierId,jdbcType=BIGINT},
        supplier_name = #{supplierName,jdbcType=VARCHAR},
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
        supplier_email = #{supplierEmail,jdbcType=VARCHAR},
        supplier_phone = #{supplierPhone,jdbcType=VARCHAR},
        carry_fee = #{carryFee,jdbcType=BIGINT},
        receiver_name = #{receiverName,jdbcType=VARCHAR},
        receiver_phone = #{receiverPhone,jdbcType=VARCHAR},
        receiver_address = #{receiverAddress,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into order_base
        (id, order_number, parent_id, parent_Number, source_type, source_id, source_number,
        buyer_id, buyer_name, buyer_phone, buyer_email, org_id, org_code, org_name, department_id,
        department_name, college_id, college_name, business_type, order_status, actual_amount,
        original_price, discounts_amount, process_type, description_supplier, description_buyer,
        supplier_id, supplier_name, supplier_code, supplier_email, supplier_phone, carry_fee,
        receiver_name, receiver_phone, receiver_address, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.orderNumber,jdbcType=VARCHAR}, #{item.parentId,jdbcType=BIGINT},
            #{item.parentNumber,jdbcType=VARCHAR}, #{item.sourceType,jdbcType=INTEGER},
            #{item.sourceId,jdbcType=BIGINT},
            #{item.sourceNumber,jdbcType=VARCHAR}, #{item.buyerId,jdbcType=BIGINT}, #{item.buyerName,jdbcType=VARCHAR},
            #{item.buyerPhone,jdbcType=VARCHAR}, #{item.buyerEmail,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT},
            #{item.orgCode,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR}, #{item.departmentId,jdbcType=BIGINT},
            #{item.departmentName,jdbcType=VARCHAR}, #{item.collegeId,jdbcType=BIGINT},
            #{item.collegeName,jdbcType=VARCHAR},
            #{item.businessType,jdbcType=INTEGER}, #{item.orderStatus,jdbcType=INTEGER},
            #{item.actualAmount,jdbcType=BIGINT},
            #{item.originalPrice,jdbcType=BIGINT}, #{item.discountsAmount,jdbcType=BIGINT},
            #{item.processType,jdbcType=INTEGER}, #{item.descriptionSupplier,jdbcType=VARCHAR},
            #{item.descriptionBuyer,jdbcType=VARCHAR}, #{item.supplierId,jdbcType=BIGINT},
            #{item.supplierName,jdbcType=VARCHAR}, #{item.supplierCode,jdbcType=VARCHAR},
            #{item.supplierEmail,jdbcType=VARCHAR},
            #{item.supplierPhone,jdbcType=VARCHAR}, #{item.carryFee,jdbcType=BIGINT},
            #{item.receiverName,jdbcType=VARCHAR},
            #{item.receiverPhone,jdbcType=VARCHAR}, #{item.receiverAddress,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2019-07-29-->
    <select id="findRentcarOrderForBuyer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_base
        where 1=1
        <if test="buyerId!=null">
            and (
            buyer_id=#{buyerId,jdbcType=BIGINT}
            <if test="piDepartmentIds!=null and piDepartmentIds.size()>0">
                or department_id in
                <foreach item="item" index="index" collection="piDepartmentIds"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            )
        </if>
        <if test="businessType!=null">
            and business_type=#{businessType,jdbcType=TINYINT}
        </if>
        <if test="startTime!=null">
            and create_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime!=null">
            and create_time <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="searchContext != null and searchContext != ''">
            and (
            order_number = #{searchContext,jdbcType=VARCHAR}
            or buyer_name = #{searchContext,jdbcType=VARCHAR}
            or department_name = #{searchContext,jdbcType=VARCHAR}
            or supplier_name = #{searchContext,jdbcType=VARCHAR}
            )
        </if>
        and ( order_status not in (58,56))
        order by create_time desc
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-07-30-->
    <select id="findRentcarOrderForSupp" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_base
        where 1=1
        <if test="supplierId!=null">
            and supplier_id=#{supplierId,jdbcType=BIGINT}
        </if>
        <if test="orderStatusList!=null and orderStatusList.size()>0">
            and order_status in
            <foreach item="item" index="index" collection="orderStatusList"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="businessType!=null">
            and business_type=#{businessType,jdbcType=TINYINT}
        </if>
        <if test="orderNumber!=null and orderNumber!=''">
            and order_number  = #{orderNumber,jdbcType=VARCHAR}
        </if>
        <if test="buyerName!=null and buyerName!='' ">
            and buyer_name = #{buyerName,jdbcType=VARCHAR}
        </if>
        <if test="orgName!=null and orgName!=''">
            and org_name = #{orgName,jdbcType=VARCHAR}
        </if>
        <if test="startTime!=null">
            and create_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime!=null">
            and create_time <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        and ( order_status not in (58,56))
        order by create_time desc
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-08-05-->
    <update id="updateOrderStatusById">
        update order_base
        set order_status=#{orderStatus,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2019-08-05-->
    <select id="findByIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_base
        where 1=1
        <if test="idCollection!=null and idCollection.size()>0">
            and id in
            <foreach item="item" index="index" collection="idCollection"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
            order by field
            <foreach item="item" index="index" collection="idCollection"
                     open="(id," separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-08-08-->
    <select id="countByBuyerIdAndOrderStatus" resultType="java.lang.Integer">
        select count(1)
        from order_base
        where 1=1
        <if test="buyerId!=null">
            and buyer_id=#{buyerId,jdbcType=BIGINT}
        </if>
        <if test="orderStatus!=null">
            and order_status=#{orderStatus,jdbcType=TINYINT}
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-08-15-->
    <update id="updateOrderStatusByOrderNumber">
        update order_base
        set order_status=#{orderStatus,jdbcType=INTEGER}
        where 1=1
        <if test="orderNumber!=null">
            and order_number=#{orderNumber,jdbcType=VARCHAR}
        </if>
    </update>

    <!--auto generated by MybatisCodeHelper on 2019-08-17-->
    <select id="findByBuyerIdAndOrderStatusList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_base
        where 1=1
        <if test="buyerId!=null">
            and buyer_id=#{buyerId,jdbcType=BIGINT}
        </if>
        <if test="businessType != null">
            and business_type = #{businessType,jdbcType=INTEGER}
        </if>
        <if test="orderStatusCollection!=null and orderStatusCollection.size()>0">
            and order_status in
            <foreach item="item" index="index" collection="orderStatusCollection"
                     open="(" separator="," close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        order by create_time desc
        <if test="size != null">
            limit #{size,jdbcType=INTEGER}
        </if>

    </select>

    <!--auto generated by MybatisCodeHelper on 2019-08-23-->
    <select id="findPublicityOrder" resultMap="BaseResultMap">
        select
            a.id,
            a.order_number,
            a.parent_id,
            a.parent_Number,
            a.source_type,
            a.source_id,
            a.source_number,
            a.buyer_id,
            a.buyer_name,
            a.buyer_phone,
            a.buyer_email,
            a.org_id,
            a.org_code,
            a.org_name,
            a.department_id,
            a.department_name,
            a.college_id,
            a.college_name,
            a.business_type,
            a.order_status,
            a.actual_amount,
            a.original_price,
            a.discounts_amount,
            a.process_type,
            a.description_supplier,
            a.description_buyer,
            a.supplier_id,
            a.supplier_name,
            a.supplier_code,
            a.supplier_email,
            a.supplier_phone,
            a.carry_fee,
            a.receiver_name,
            a.receiver_phone,
            a.receiver_address,
            a.create_time,
            a.update_time
        from order_base a
        <if test="rideType != null">
            , order_extra_query b
        </if>
        where 1=1
        <if test="statusList!=null and statusList.size()>0">
            and order_status in
            <foreach item="item" index="index" collection="statusList"
                     open="(" separator="," close=")">
                #{item,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="supplierName!=null and supplierName!=''">
            and supplier_name = #{supplierName,jdbcType=VARCHAR}
        </if>
        <if test="collegeName!=null and collegeName!='' ">
            and college_name = #{collegeName,jdbcType=VARCHAR}
        </if>
        <if test="processType!=null">
            and process_type=#{processType,jdbcType=TINYINT}
        </if>
        <if test="businessType!=null">
            and a.business_type=#{businessType,jdbcType=TINYINT}
        </if>
        <if test="rideType != null">
            and a.id = b.order_id
            and b.`key`='rideType'
            and b.value = #{rideType,jdbcType=VARCHAR}
        </if>
            order by a.create_time desc
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-08-30-->
    <select id="getTravelRecordList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_base
        where 1=1
        <if test="buyerId!=null">
            and buyer_id=#{buyerId,jdbcType=BIGINT}
        </if>
        <if test="businessType!=null">
            and business_type=#{businessType,jdbcType=TINYINT}
        </if>
        and ( order_status not in (58,56))
        order by if(order_status = 62,0,1) asc, create_time desc
    </select>

<!--auto generated by MybatisCodeHelper on 2019-09-07-->
    <select id="findByOrderNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_base
        where order_number=#{orderNumber,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2019-11-04-->
    <select id="getTimeOutNoticeOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_base
        where 1=1
        <if test="orderStatusCollection!=null and orderStatusCollection.size()>0">
            and order_status in
            <foreach item="item" index="index" collection="orderStatusCollection"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="businessType != null">
            and business_type = #{businessType,jdbcType=INTEGER}
        </if>
        <if test="timeOutDay!=null">
            and DATEDIFF(now(),update_time) <![CDATA[>=]]> #{timeOutDay,jdbcType=INTEGER}
        </if>
    </select>

</mapper>