package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.order.base.order.model.BusinessDockingDO;

/**
 * BusinessDocking 转换类
 * <AUTHOR>
 * @Date 2020/8/18 6:23 下午
 */
public class BusinessDockingTranslator {

    /**
     * do 转 dto
     * @param item
     * @return
     */
    public static BusinessDockingDTO doToDto(BusinessDockingDO item) {
        BusinessDockingDTO result = new BusinessDockingDTO();
        result.setId(item.getId());
        result.setBusinessOrderNo(item.getBusinessOrderNo());
        result.setDockingNo(item.getDockingNo());
        result.setReagentStatus(item.getReagentStatus());
        result.setExtraJson(item.getExtraJson());
        result.setExtraStatus(item.getExtraStatus());
        result.setOrgId(item.getOrgId());
        result.setOrgCode(item.getOrgCode());
        result.setRemark(item.getRemark());
        result.setCreateTime(item.getCreateTime());
        result.setUpdateTime(item.getUpdateTime());

        return result;
    }

    /**
     * dto 转 do
     * @param item
     * @return
     */
    public static BusinessDockingDO dtoToDo(BusinessDockingDTO item) {
        BusinessDockingDO result = new BusinessDockingDO();
        result.setId(item.getId());
        result.setBusinessOrderNo(item.getBusinessOrderNo());
        result.setDockingNo(item.getDockingNo());
        result.setReagentStatus(item.getReagentStatus());
        result.setExtraJson(item.getExtraJson());
        result.setExtraStatus(item.getExtraStatus());
        result.setOrgId(item.getOrgId());
        result.setOrgCode(item.getOrgCode());
        result.setRemark(item.getRemark() != null ? item.getRemark() : "");
        result.setCreateTime(item.getCreateTime());
        result.setUpdateTime(item.getUpdateTime());

        return result;
    }
}
