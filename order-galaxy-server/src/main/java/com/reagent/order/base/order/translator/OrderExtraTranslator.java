package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.model.OrderExtraDO;
import com.ruijing.fundamental.common.collections.New;

import java.util.List;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/6/28 16:09
 */
public class OrderExtraTranslator {

    /**
     * do to dto
     * @param input
     * @return
     */
    public static BaseOrderExtraDTO doToDto(OrderExtraDO input) {
        BaseOrderExtraDTO output = new BaseOrderExtraDTO();
        output.setId(input.getId());
        output.setOrderId(input.getOrderId());
        output.setOrderNo(input.getOrderNo());
        output.setOrgId(input.getOrgId());
        output.setExtraKey(input.getExtraKey());
        output.setExtraKeyDesc(input.getExtraKeyDesc());
        output.setExtraValue(input.getExtraValue());
        output.setCreateTime(input.getCreateTime());
        output.setUpdateTime(input.getUpdateTime());
        return output;
    }

    /**
     * do list to dto list
     * @param inputList
     * @return
     */
    public static List<BaseOrderExtraDTO> doListToDtoList(List<OrderExtraDO> inputList) {
        List<BaseOrderExtraDTO> outputList = New.list();
        for (OrderExtraDO orderExtraDO : inputList) {
            outputList.add(doToDto(orderExtraDO));
        }
        return outputList;
    }

    /**
     * dto to do
     * @param input
     * @return
     */
    public static OrderExtraDO dtoToDo(BaseOrderExtraDTO input) {
        OrderExtraDO output = new OrderExtraDO();
        output.setId(input.getId());
        output.setOrderId(input.getOrderId());
        output.setOrderNo(input.getOrderNo());
        output.setOrgId(input.getOrgId());
        output.setExtraKey(input.getExtraKey());
        output.setExtraKeyDesc(input.getExtraKeyDesc());
        output.setExtraValue(input.getExtraValue());
        output.setCreateTime(input.getCreateTime());
        output.setUpdateTime(input.getUpdateTime());
        return output;
    }

    /**
     * dto list to do list
     * @param inputList
     * @return
     */
    public static List<OrderExtraDO> dtoListToDoList(List<BaseOrderExtraDTO> inputList) {
        List<OrderExtraDO> outputList = New.list();
        for (BaseOrderExtraDTO input : inputList) {
            outputList.add(dtoToDo(input));
        }
        return outputList;
    }
}
