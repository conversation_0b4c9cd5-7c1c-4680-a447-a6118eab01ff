package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.RefOrderFundCard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ref_order_fund_card(订单关联经费卡表)】的数据库操作Mapper
* @createDate 2024-02-05 14:37:26
* @Entity com.reagent.order.base.order.model.RefOrderFundCard
*/
@Mapper
public interface RefOrderFundCardMapper {

    int deleteByPrimaryKey(Long id);

    int insert(RefOrderFundCard record);

    int insertSelective(RefOrderFundCard record);

    RefOrderFundCard selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RefOrderFundCard record);

    int updateByPrimaryKey(RefOrderFundCard record);

    void batchInsertSelective(@Param("col") Collection<RefOrderFundCard> col);

    List<RefOrderFundCard> listInOrderId(@Param("orderIdList") List<Integer> orderIdList);

    void batchUpdateSelective(@Param("col") Collection<RefOrderFundCard> col);

    void updateByOrderIdSelective(RefOrderFundCard record);

    void deleteInId(@Param("orderIdList") List<Integer> orderIdList);
}
