<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.RefOrderFundCardMapper">

    <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.RefOrderFundCard">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="orderId" column="order_id" jdbcType="INTEGER"/>
            <result property="fundCardId" column="fund_card_id" jdbcType="VARCHAR"/>
            <result property="fundCardNo" column="fund_card_no" jdbcType="VARCHAR"/>
            <result property="freezeAmount" column="freeze_amount" jdbcType="DECIMAL"/>
            <result property="useAmount" column="use_amount" jdbcType="DECIMAL"/>
            <result property="expenseApplyNo" column="expense_apply_no" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,fund_card_id,
        fund_card_no,freeze_amount,use_amount,
        expense_apply_no,create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ref_order_fund_card
        where  id = #{id,jdbcType=INTEGER} 
    </select>

    <select id="listInOrderId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from ref_order_fund_card
        where order_id in
        <foreach collection="orderIdList" item="item" index="index" separator="," open="(" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from ref_order_fund_card
        where  id = #{id,jdbcType=INTEGER} 
    </delete>
    <delete id="deleteInId">
        delete from ref_order_fund_card
        where  order_id in
        <foreach collection="orderIdList" item="item" index="index" separator="," open="(" close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.RefOrderFundCard" useGeneratedKeys="true">
        insert into ref_order_fund_card
        ( id,order_id,fund_card_id
        ,fund_card_no,freeze_amount,use_amount
        ,expense_apply_no,create_time,update_time
        )
        values (#{id,jdbcType=INTEGER},#{orderId,jdbcType=INTEGER},#{fundCardId,jdbcType=VARCHAR}
        ,#{fundCardNo,jdbcType=VARCHAR},#{freezeAmount,jdbcType=DECIMAL},#{useAmount,jdbcType=DECIMAL}
        ,#{expenseApplyNo,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.RefOrderFundCard" useGeneratedKeys="true">
        insert into ref_order_fund_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="orderId != null">order_id,</if>
                <if test="fundCardId != null">fund_card_id,</if>
                <if test="fundCardNo != null">fund_card_no,</if>
                <if test="freezeAmount != null">freeze_amount,</if>
                <if test="useAmount != null">use_amount,</if>
                <if test="expenseApplyNo != null">expense_apply_no,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="orderId != null">#{orderId,jdbcType=INTEGER},</if>
                <if test="fundCardId != null">#{fundCardId,jdbcType=VARCHAR},</if>
                <if test="fundCardNo != null">#{fundCardNo,jdbcType=VARCHAR},</if>
                <if test="freezeAmount != null">#{freezeAmount,jdbcType=DECIMAL},</if>
                <if test="useAmount != null">#{useAmount,jdbcType=DECIMAL},</if>
                <if test="expenseApplyNo != null">#{expenseApplyNo,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <insert id="batchInsertSelective">
        insert into ref_order_fund_card
        (order_id,fund_card_id
        ,fund_card_no,freeze_amount,use_amount
        ,expense_apply_no
        )
        values
        <foreach collection="col" item="item" index="index" separator=",">
            (
             <choose>
                 <when test="item.orderId != null">
                     #{item.orderId,jdbcType=INTEGER},
                 </when>
                 <otherwise>
                     -1,
                 </otherwise>
             </choose>
            <choose>
                <when test="item.fundCardId != null">
                    #{item.fundCardId,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    "",
                </otherwise>
            </choose>
            <choose>
                <when test="item.fundCardNo != null">
                    #{item.fundCardNo,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    "",
                </otherwise>
            </choose>
            <choose>
                <when test="item.freezeAmount != null">
                    #{item.freezeAmount,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    0.00,
                </otherwise>
            </choose>
            <choose>
                <when test="item.useAmount != null">
                    #{item.useAmount,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    0.00,
                </otherwise>
            </choose>
            <choose>
                <when test="item.expenseApplyNo != null">
                    #{item.expenseApplyNo,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    ""
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.RefOrderFundCard">
        update ref_order_fund_card
        <set>
                <if test="orderId != null">
                    order_id = #{orderId,jdbcType=INTEGER},
                </if>
                <if test="fundCardId != null">
                    fund_card_id = #{fundCardId,jdbcType=VARCHAR},
                </if>
                <if test="fundCardNo != null">
                    fund_card_no = #{fundCardNo,jdbcType=VARCHAR},
                </if>
                <if test="freezeAmount != null">
                    freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
                </if>
                <if test="useAmount != null">
                    use_amount = #{useAmount,jdbcType=DECIMAL},
                </if>
                <if test="expenseApplyNo != null">
                    expense_apply_no = #{expenseApplyNo,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.RefOrderFundCard">
        update ref_order_fund_card
        set 
            order_id =  #{orderId,jdbcType=INTEGER},
            fund_card_id =  #{fundCardId,jdbcType=VARCHAR},
            fund_card_no =  #{fundCardNo,jdbcType=VARCHAR},
            freeze_amount =  #{freezeAmount,jdbcType=DECIMAL},
            use_amount =  #{useAmount,jdbcType=DECIMAL},
            expense_apply_no =  #{expenseApplyNo,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="batchUpdateSelective" parameterType="com.reagent.order.base.order.model.RefOrderFundCard">
        <foreach collection="col" item="element" index="index" separator=";">
            update ref_order_fund_card
            <set>
                <if test="element.orderId != null">
                    order_id = #{element.orderId,jdbcType=INTEGER},
                </if>
                <if test="element.fundCardId != null">
                    fund_card_id = #{element.fundCardId,jdbcType=VARCHAR},
                </if>
                <if test="element.fundCardNo != null">
                    fund_card_no = #{element.fundCardNo,jdbcType=VARCHAR},
                </if>
                <if test="element.freezeAmount != null">
                    freeze_amount = #{element.freezeAmount,jdbcType=DECIMAL},
                </if>
                <if test="element.useAmount != null">
                    use_amount = #{element.useAmount,jdbcType=DECIMAL},
                </if>
                <if test="element.expenseApplyNo != null">
                    expense_apply_no = #{element.expenseApplyNo,jdbcType=VARCHAR},
                </if>
                <if test="element.createTime != null">
                    create_time = #{element.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="element.updateTime != null">
                    update_time = #{element.updateTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where   id = #{element.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateByOrderIdSelective">
        update ref_order_fund_card
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=INTEGER},
            </if>
            <if test="fundCardId != null">
                fund_card_id = #{fundCardId,jdbcType=VARCHAR},
            </if>
            <if test="fundCardNo != null">
                fund_card_no = #{fundCardNo,jdbcType=VARCHAR},
            </if>
            <if test="freezeAmount != null">
                freeze_amount = #{freezeAmount,jdbcType=DECIMAL},
            </if>
            <if test="useAmount != null">
                use_amount = #{useAmount,jdbcType=DECIMAL},
            </if>
            <if test="expenseApplyNo != null">
                expense_apply_no = #{expenseApplyNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where   order_id = #{orderId,jdbcType=INTEGER}
    </update>
</mapper>
