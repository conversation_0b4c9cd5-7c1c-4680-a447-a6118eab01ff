package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderFileOperationLogDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogRequestDTO;
import com.reagent.order.base.order.model.OrderFileOperationLogDO;
import com.ruijing.fundamental.common.collections.New;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单文件操作日志对象转换工具类
 */
public class OrderFileOperationLogTranslator {

    /**
     * DO对象转DTO对象
     * 
     * @param orderFileOperationLogDO DO对象
     * @return DTO对象
     */
    public static OrderFileOperationLogDTO do2Dto(OrderFileOperationLogDO orderFileOperationLogDO) {
        if (Objects.isNull(orderFileOperationLogDO)) {
            return null;
        }
        OrderFileOperationLogDTO dto = new OrderFileOperationLogDTO();
        dto.setId(orderFileOperationLogDO.getId());
        dto.setOrderId(orderFileOperationLogDO.getOrderId());
        dto.setLogId(orderFileOperationLogDO.getLogId());
        dto.setUrl(orderFileOperationLogDO.getUrl());
        dto.setFileName(orderFileOperationLogDO.getFileName());
        dto.setCreateTime(orderFileOperationLogDO.getCreateTime());
        dto.setUpdateTime(orderFileOperationLogDO.getUpdateTime());
        return dto;
    }

    /**
     * DO对象列表转DTO对象列表
     * 
     * @param orderFileOperationLogDOList DO对象列表
     * @return DTO对象列表
     */
    public static List<OrderFileOperationLogDTO> listDo2Dto(List<OrderFileOperationLogDO> orderFileOperationLogDOList) {
        if (CollectionUtils.isEmpty(orderFileOperationLogDOList)) {
            return New.emptyList();
        }
        return orderFileOperationLogDOList.stream()
                .map(OrderFileOperationLogTranslator::do2Dto)
                .collect(Collectors.toList());
    }

    /**
     * RequestDTO对象转DO对象
     * 
     * @param requestDTO RequestDTO对象
     * @return DO对象
     */
    public static OrderFileOperationLogDO reqDto2Do(OrderFileOperationLogRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)) {
            return null;
        }
        OrderFileOperationLogDO doObj = new OrderFileOperationLogDO();
        doObj.setOrderId(requestDTO.getOrderId());
        doObj.setLogId(requestDTO.getLogId());
        doObj.setUrl(requestDTO.getUrl());
        doObj.setFileName(requestDTO.getFileName());
        return doObj;
    }

    /**
     * RequestDTO对象列表转DO对象列表
     * 
     * @param requestDTOList RequestDTO对象列表
     * @return DO对象列表
     */
    public static List<OrderFileOperationLogDO> listReqDto2Do(List<OrderFileOperationLogRequestDTO> requestDTOList) {
        if (CollectionUtils.isEmpty(requestDTOList)) {
            return New.emptyList();
        }
        return requestDTOList.stream()
                .map(OrderFileOperationLogTranslator::reqDto2Do)
                .collect(Collectors.toList());
    }
}
