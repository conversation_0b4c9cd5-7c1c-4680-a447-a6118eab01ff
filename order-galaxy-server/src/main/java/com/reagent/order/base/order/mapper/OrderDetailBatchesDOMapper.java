package com.reagent.order.base.order.mapper;
import java.util.Collection;

import com.reagent.order.base.order.model.OrderDetailBatchesDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderDetailBatchesDOMapper {
    /**
     * 批量出入明细批次信息
     * @param list
     * @return 成功数量
     */
    int insertList(@Param("list")List<OrderDetailBatchesDO> list);

    /**
     * 更新订单明细批次
     * @param orderDetailBatchesDO
     * @return 成功数量
     */
    int updateByDetailId(@Param("orderDetailBatchesDO") OrderDetailBatchesDO orderDetailBatchesDO);

    /**
     * 查询商品订单明细批次
     * @param detailIdCollection
     * @return 批次记录
     */
    List<OrderDetailBatchesDO> findByDetailIdIn(@Param("detailIdCollection")Collection<Integer> detailIdCollection);

}