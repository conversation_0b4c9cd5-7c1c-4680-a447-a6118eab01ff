package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderMaterialCodeDTO;
import com.reagent.order.base.order.model.OrderMaterialCode;

/**
 * Name: OrderMaterialCodeTranslator
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/3/11
 */
public class OrderMaterialCodeTranslator {


    public static OrderMaterialCode dto2DO(OrderMaterialCodeDTO orderMaterialCodeDTO) {
        OrderMaterialCode materialCode = new OrderMaterialCode();
        materialCode.setId(orderMaterialCodeDTO.getId());
        materialCode.setBrand(orderMaterialCodeDTO.getBrand());
        materialCode.setGoodCode(orderMaterialCodeDTO.getGoodCode());
        materialCode.setSpec(orderMaterialCodeDTO.getSpec());
        materialCode.setMaterialCode(orderMaterialCodeDTO.getMaterialCode());
        return materialCode;
    }

    public static OrderMaterialCodeDTO do2DTO(OrderMaterialCode orderMaterialCode) {
        OrderMaterialCodeDTO materialCodeDTO = new OrderMaterialCodeDTO();
        materialCodeDTO.setId(orderMaterialCode.getId());
        materialCodeDTO.setBrand(orderMaterialCode.getBrand());
        materialCodeDTO.setGoodCode(orderMaterialCode.getGoodCode());
        materialCodeDTO.setSpec(orderMaterialCode.getSpec());
        materialCodeDTO.setMaterialCode(orderMaterialCode.getMaterialCode());
        return materialCodeDTO;
    }
}
