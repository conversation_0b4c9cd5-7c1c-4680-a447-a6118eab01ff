<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderExtraMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderExtraDO">
    <!--@mbg.generated-->
    <!--@Table order_extra-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="extra_key" jdbcType="INTEGER" property="extraKey" />
    <result column="extra_key_desc" jdbcType="VARCHAR" property="extraKeyDesc" />
    <result column="extra_value" jdbcType="VARCHAR" property="extraValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, order_no, org_id, extra_key, extra_key_desc, extra_value, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_extra
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from order_extra
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <delete id="deleteInOrderId">
    delete from order_extra
    where order_id in
    <foreach item="item" index="index" collection="orderIdCol"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderExtraDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_extra (order_id, order_no, org_id, 
      extra_key, extra_key_desc, extra_value, 
      create_time, update_time)
    values (#{orderId,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{orgId,jdbcType=INTEGER}, 
      #{extraKey,jdbcType=INTEGER}, #{extraKeyDesc,jdbcType=VARCHAR}, #{extraValue,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderExtraDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="extraKey != null">
        extra_key,
      </if>
      <if test="extraKeyDesc != null">
        extra_key_desc,
      </if>
      <if test="extraValue != null">
        extra_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="extraKey != null">
        #{extraKey,jdbcType=INTEGER},
      </if>
      <if test="extraKeyDesc != null">
        #{extraKeyDesc,jdbcType=VARCHAR},
      </if>
      <if test="extraValue != null">
        #{extraValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderExtraDO">
    <!--@mbg.generated-->
    update order_extra
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="extraKey != null">
        extra_key = #{extraKey,jdbcType=INTEGER},
      </if>
      <if test="extraKeyDesc != null">
        extra_key_desc = #{extraKeyDesc,jdbcType=VARCHAR},
      </if>
      <if test="extraValue != null">
        extra_value = #{extraValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.OrderExtraDO">
    <!--@mbg.generated-->
    update order_extra
    set order_id = #{orderId,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=INTEGER},
      extra_key = #{extraKey,jdbcType=INTEGER},
      extra_key_desc = #{extraKeyDesc,jdbcType=VARCHAR},
      extra_value = #{extraValue,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-06-28-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO order_extra(
    order_id,
    order_no,
    org_id,
    extra_key,
    extra_key_desc,
    extra_value
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.orderId,jdbcType=INTEGER},
      #{element.orderNo,jdbcType=VARCHAR},
      #{element.orgId,jdbcType=INTEGER},
      #{element.extraKey,jdbcType=INTEGER},
      #{element.extraKeyDesc,jdbcType=VARCHAR},
      #{element.extraValue,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="batchUpdateExtraValue">
    <foreach collection="list" item="element" separator=";">
      update order_extra set extra_value = #{element.extraValue,jdbcType=VARCHAR}
      where order_id = #{element.orderId,jdbcType=INTEGER} and extra_key = #{element.extraKey,jdbcType=INTEGER}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2021-06-28-->
  <select id="selectByOrderIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_extra
    where order_id in
    <foreach item="item" index="index" collection="orderIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-06-28-->
  <select id="selectByOrderNoIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_extra
    where order_no in
    <foreach item="item" index="index" collection="orderNoCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="selectByOrderNoListAndExtraKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_extra
    where order_no in
    <foreach item="item" index="index" collection="orderNoCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
    <if test="extraKey != null">
      and extra_key=#{extraKey,jdbcType=INTEGER}
    </if>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-06-28-->
  <select id="selectByOrderIdAndExtraKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_extra
    <where>order_id=#{orderId,jdbcType=INTEGER}
      <if test="extraKey != null">
        and extra_key=#{extraKey,jdbcType=INTEGER}
      </if>
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-06-28-->
  <select id="selectByOrderIdInAndExtraKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_extra
        <where>order_id in
            <foreach item="item" index="index" collection="orderIdCollection"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>

            <if test="extraKey != null">
                and extra_key=#{extraKey,jdbcType=INTEGER}
            </if>
        </where>
    </select>

  <!-- 根据订单ID集合和extraKey集合查询条目 -->
  <select id="selectByOrderIdInAndExtraKeyIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_extra
    <where>
      order_id in
      <foreach item="item" collection="orderIdCollection" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
      and extra_key in
      <foreach item="ek" collection="extraKeyCollection" open="(" separator="," close=")">
        #{ek,jdbcType=INTEGER}
      </foreach>
    </where>
  </select>
</mapper>