<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.log.mapper.OrderOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.log.model.OrderOperationLog">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="operation" jdbcType="TINYINT" property="operation" />
    <result column="operation_desc" jdbcType="VARCHAR" property="operationDesc" />
    <result column="operation_note" jdbcType="VARCHAR" property="operationNote" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_type" jdbcType="TINYINT" property="operatorType" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operator_depart_id" jdbcType="BIGINT" property="operatorDepartId" />
    <result column="operator_depart_name" jdbcType="VARCHAR" property="operatorDepartName" />
    <result column="operator_org_id" jdbcType="BIGINT" property="operatorOrgId" />
    <result column="operator_org_code" jdbcType="VARCHAR" property="operatorOrgCode" />
    <result column="operator_org_name" jdbcType="VARCHAR" property="operatorOrgName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, order_number, business_type, `operation`, operation_desc, operation_note, 
    operator_id, operator_type, operator_name, operator_depart_id, operator_depart_name, 
    operator_org_id, operator_org_code, operator_org_name, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from order_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.log.model.OrderOperationLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_operation_log (order_id, order_number, business_type, 
      `operation`, operation_desc, operation_note, 
      operator_id, operator_type, operator_name, 
      operator_depart_id, operator_depart_name, operator_org_id, 
      operator_org_code, operator_org_name, create_time, 
      update_time)
    values (#{orderId,jdbcType=BIGINT}, #{orderNumber,jdbcType=VARCHAR}, #{businessType,jdbcType=TINYINT}, 
      #{operation,jdbcType=TINYINT}, #{operationDesc,jdbcType=VARCHAR}, #{operationNote,jdbcType=VARCHAR}, 
      #{operatorId,jdbcType=BIGINT}, #{operatorType,jdbcType=TINYINT}, #{operatorName,jdbcType=VARCHAR}, 
      #{operatorDepartId,jdbcType=BIGINT}, #{operatorDepartName,jdbcType=VARCHAR}, #{operatorOrgId,jdbcType=BIGINT}, 
      #{operatorOrgCode,jdbcType=VARCHAR}, #{operatorOrgName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.log.model.OrderOperationLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderNumber != null">
        order_number,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="operationDesc != null">
        operation_desc,
      </if>
      <if test="operationNote != null">
        operation_note,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operatorDepartId != null">
        operator_depart_id,
      </if>
      <if test="operatorDepartName != null">
        operator_depart_name,
      </if>
      <if test="operatorOrgId != null">
        operator_org_id,
      </if>
      <if test="operatorOrgCode != null">
        operator_org_code,
      </if>
      <if test="operatorOrgName != null">
        operator_org_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNumber != null">
        #{orderNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=TINYINT},
      </if>
      <if test="operationDesc != null">
        #{operationDesc,jdbcType=VARCHAR},
      </if>
      <if test="operationNote != null">
        #{operationNote,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorDepartId != null">
        #{operatorDepartId,jdbcType=BIGINT},
      </if>
      <if test="operatorDepartName != null">
        #{operatorDepartName,jdbcType=VARCHAR},
      </if>
      <if test="operatorOrgId != null">
        #{operatorOrgId,jdbcType=BIGINT},
      </if>
      <if test="operatorOrgCode != null">
        #{operatorOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="operatorOrgName != null">
        #{operatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.log.model.OrderOperationLog">
    <!--@mbg.generated-->
    update order_operation_log
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNumber != null">
        order_number = #{orderNumber,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=TINYINT},
      </if>
      <if test="operationDesc != null">
        operation_desc = #{operationDesc,jdbcType=VARCHAR},
      </if>
      <if test="operationNote != null">
        operation_note = #{operationNote,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorDepartId != null">
        operator_depart_id = #{operatorDepartId,jdbcType=BIGINT},
      </if>
      <if test="operatorDepartName != null">
        operator_depart_name = #{operatorDepartName,jdbcType=VARCHAR},
      </if>
      <if test="operatorOrgId != null">
        operator_org_id = #{operatorOrgId,jdbcType=BIGINT},
      </if>
      <if test="operatorOrgCode != null">
        operator_org_code = #{operatorOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="operatorOrgName != null">
        operator_org_name = #{operatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.log.model.OrderOperationLog">
    <!--@mbg.generated-->
    update order_operation_log
    set order_id = #{orderId,jdbcType=BIGINT},
      order_number = #{orderNumber,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=TINYINT},
      `operation` = #{operation,jdbcType=TINYINT},
      operation_desc = #{operationDesc,jdbcType=VARCHAR},
      operation_note = #{operationNote,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=BIGINT},
      operator_type = #{operatorType,jdbcType=TINYINT},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operator_depart_id = #{operatorDepartId,jdbcType=BIGINT},
      operator_depart_name = #{operatorDepartName,jdbcType=VARCHAR},
      operator_org_id = #{operatorOrgId,jdbcType=BIGINT},
      operator_org_code = #{operatorOrgCode,jdbcType=VARCHAR},
      operator_org_name = #{operatorOrgName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2019-07-31-->
  <select id="findByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_operation_log
    where order_id=#{orderId,jdbcType=BIGINT}
    order by create_time
  </select>
</mapper>