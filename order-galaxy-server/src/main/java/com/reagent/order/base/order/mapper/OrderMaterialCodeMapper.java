package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.OrderMaterialCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderMaterialCodeMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(OrderMaterialCode record);

    int insertSelectiveBatch(@Param("list") List<OrderMaterialCode> record);

    OrderMaterialCode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderMaterialCode record);

    List<OrderMaterialCode> queryByParam(@Param("brand") String brand,
                                         @Param("goodCode") String goodCode,
                                         @Param("spec") String spec);
}