package com.reagent.order.base.log.mapper;

import com.reagent.order.base.log.model.OrderOperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderOperationLogMapper {

    /**
     * 根据主键删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增 orderBase
     * @param record 入参
     * @return int
     */
    int insert(OrderOperationLog record);

    /**
     * 选择性新增
     * @param record 入参
     * @return int
     */
    int insertSelective(OrderOperationLog record);

    /**
     * 根据主键查询
     * @param id 主键
     * @return
     */
    OrderOperationLog selectByPrimaryKey(Long id);

    /**
     * 根据主键选择性更新
     * @param record 入参
     * @return 结果
     */
    int updateByPrimaryKeySelective(OrderOperationLog record);

    /**
     * 根据主键更新
     * @param record 入参
     * @return 结果
     */
    int updateByPrimaryKey(OrderOperationLog record);

    /**
     * 根据订单id查询日志
     *
     * @param orderId 订单Id
     * @return 日志列表
     */
    List<OrderOperationLog> findByOrderId(@Param("orderId") Long orderId);
}