package com.reagent.order.base.log.model;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2022/12/7 14:22
 * @description
 */
public class DataOperationLog {

    /**
     * 主键
     */
    private Integer id;

    /**
    * 订单单号
    */
    private String orderNo;

    /**
    * 数据修正理由
    */
    private String fixReason;

    /**
    * 操作内容
    */
    private String operation;

    /**
    * 操作人guid
    */
    private String operatorGuid;

    /**
    * 操作人名字
    */
    private String operatorName;

    /**
    * 单位id
    */
    private Integer orgId;

    /**
    * 单位名称
    */
    private String orgName;

    /**
    * 操作类型
    */
    private Integer operationType;

    /**
     * 修改前状态
     */
    @Deprecated
    private Integer prevStatus;

    /**
     * 修改前的描述
     */
    private String preDesc;

    /**
    * 钉钉单号
    */
    private String approveNumber;

    private Date createdTime;

    private Date updatedTime;

    /**
    * 操作时间
    */
    private Date operateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getFixReason() {
        return fixReason;
    }

    public void setFixReason(String fixReason) {
        this.fixReason = fixReason;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getApproveNumber() {
        return approveNumber;
    }

    public void setApproveNumber(String approveNumber) {
        this.approveNumber = approveNumber;
    }

    public Integer getPrevStatus() {
        return prevStatus;
    }

    public void setPrevStatus(Integer prevStatus) {
        this.prevStatus = prevStatus;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getPreDesc() {
        return preDesc;
    }

    public DataOperationLog setPreDesc(String preDesc) {
        this.preDesc = preDesc;
        return this;
    }
}