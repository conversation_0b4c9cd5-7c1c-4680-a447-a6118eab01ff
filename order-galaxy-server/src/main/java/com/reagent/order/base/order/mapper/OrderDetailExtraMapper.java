package com.reagent.order.base.order.mapper;


import com.reagent.order.base.order.dto.request.OrderDetailExtraListRequestDTO;
import com.reagent.order.base.order.model.OrderDetailExtraDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_detail_extra(订单详情额外信息表)】的数据库操作Mapper
* @createDate 2023-07-18 10:22:27
* @Entity com.ruijing.store.order.base.core.model.OrderDetailExtraDO
*/
@Mapper
public interface OrderDetailExtraMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(OrderDetailExtraDO record);

    OrderDetailExtraDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrderDetailExtraDO record);

    List<OrderDetailExtraDO> listInOrderIdAndOrderDetailId(@Param("orderIdList") List<Integer> orderIdList,
                                                           @Param("orderDetailIdList") List<Integer> orderDetailIdList,
                                                           @Param("extraKeyList") List<String> extraKeyList);

    void batchInsert(@Param("list") List<OrderDetailExtraDO> collect);

    void deleteInOrderIdAndOrderDetailId(@Param("orderIdList") List<Integer> orderIdList, @Param("orderDetailIdList") List<Integer> orderDetailIdList);

    int batchUpdateExtraValue(@Param("list") List<OrderDetailExtraDO> collect);
}
