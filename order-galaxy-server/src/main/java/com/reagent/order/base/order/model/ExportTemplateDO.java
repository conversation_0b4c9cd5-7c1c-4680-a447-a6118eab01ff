package com.reagent.order.base.order.model;

import java.util.Date;

/**
    * excel文件导出模板信息
    */
public class ExportTemplateDO {
    /**
    * id
    */
    private Integer id;

    /**
    * 用户id
    */
    private Long userId;

    /**
    * 用户类型，1-采购，3-oms
    */
    private Integer userType;

    /**
    * 采购单位id
    */
    private Integer orgId;

    /**
    * 模板分享状态，0-无分享；1-开启分享
    */
    private Integer shareStatus;

    /**
    * 模板名称
    */
    private String templateName;

    /**
    * 模板自定义信息json
    */
    private String templateJson;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getShareStatus() {
        return shareStatus;
    }

    public void setShareStatus(Integer shareStatus) {
        this.shareStatus = shareStatus;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateJson() {
        return templateJson;
    }

    public void setTemplateJson(String templateJson) {
        this.templateJson = templateJson;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", userType=").append(userType);
        sb.append(", orgId=").append(orgId);
        sb.append(", shareStatus=").append(shareStatus);
        sb.append(", templateName=").append(templateName);
        sb.append(", templateJson=").append(templateJson);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}