package com.reagent.order.base.order.dto;

import java.io.Serializable;
import java.util.Set;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2024-06-03 09:45
 * @description:
 */
public class BackDoorRequestDTO implements Serializable {

    private static final long serialVersionUID = 7855726462330947955L;

    private Set<String> orderNos;

    public Set<String> getOrderNos() {
        return orderNos;
    }

    public BackDoorRequestDTO setOrderNos(Set<String> orderNos) {
        this.orderNos = orderNos;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", BackDoorRequestDTO.class.getSimpleName() + "[", "]")
                .add("orderNos=" + orderNos)
                .toString();
    }
}
