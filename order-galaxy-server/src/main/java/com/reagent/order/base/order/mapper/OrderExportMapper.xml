<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderExportMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderExportDO">
    <!--@mbg.generated-->
    <!--@Table order_export-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="export_date" jdbcType="TIMESTAMP" property="exportDate" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="file_type" jdbcType="INTEGER" property="fileType" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
    <resultMap id="PageResultMap" type="com.reagent.order.base.order.dto.OrderExportDTO">
        <!--@mbg.generated-->
        <!--@Table order_export-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="file_name" jdbcType="VARCHAR" property="fileName" />
        <result column="export_date" jdbcType="TIMESTAMP" property="exportDate" />
        <result column="user_id" jdbcType="INTEGER" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="file_type" jdbcType="INTEGER" property="fileType" />
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
        <result column="fail_reason" jdbcType="VARCHAR" property="failReason"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, file_name, export_date, org_id, org_code, org_name, user_id, user_name, `status`, 
    file_type, file_url, fail_reason, created_time, updated_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_export
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from order_export
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderExportDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_export (file_name, export_date, org_id, 
      org_code, org_name, user_id, 
      user_name, `status`, file_type, 
      file_url, fail_reason, created_time, 
      updated_time)
    values (#{fileName,jdbcType=VARCHAR}, #{exportDate,jdbcType=TIMESTAMP}, #{orgId,jdbcType=INTEGER}, 
      #{orgCode,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{userName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{fileType,jdbcType=INTEGER}, 
      #{fileUrl,jdbcType=VARCHAR}, #{failReason,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderExportDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_export
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileName != null">
        file_name,
      </if>
      <if test="exportDate != null">
        export_date,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="exportDate != null">
        #{exportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderExportDO">
    <!--@mbg.generated-->
    update order_export
    <set>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="exportDate != null">
        export_date = #{exportDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgCode != null">
        org_code = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.OrderExportDO">
    <!--@mbg.generated-->
    update order_export
    set file_name = #{fileName,jdbcType=VARCHAR},
      export_date = #{exportDate,jdbcType=TIMESTAMP},
      org_id = #{orgId,jdbcType=INTEGER},
      org_code = #{orgCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      user_name = #{userName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      file_type = #{fileType,jdbcType=INTEGER},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <!--auto generated by MybatisCodeHelper on 2020-11-11-->
    <select id="selectByExportDateBefore" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from order_export
        where export_date <![CDATA[<]]> #{maxExportDate,jdbcType=TIMESTAMP}
    </select>

    <!--auto generated by MybatisCodeHelper on 2020-11-11-->
    <delete id="deleteByIdIn">
        delete from order_export
        where id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </delete>

<!--auto generated by MybatisCodeHelper on 2021-01-12-->
  <select id="selectOrderExportList" resultMap="PageResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_export
    <where>user_id=#{userId,jdbcType=INTEGER} and org_id=#{orgId,jdbcType=INTEGER} and file_type in
      <foreach item="item" index="index" collection="fileTypeCollection"
               open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>

      <if test="status != null">
        and `status`=#{status,jdbcType=INTEGER}
      </if>
      <if test="minExportDate != null">
        and export_date <![CDATA[>]]> #{minExportDate,jdbcType=TIMESTAMP}
      </if>
      <if test="maxExportDate != null">
        and export_date <![CDATA[<]]> #{maxExportDate,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by export_date desc
  </select>
</mapper>