<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.ZdGoodLibraryOrderDetailMapper">

    <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.ZdGoodLibraryOrderDetail">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="goodName" column="good_name" jdbcType="VARCHAR"/>
            <result property="matchDegree" column="match_degree" jdbcType="INTEGER"/>
            <result property="orgId" column="org_id" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_no,good_name,
        match_degree,org_id,create_time,
        update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from zd_good_library_order_detail
        where  id = #{id,jdbcType=INTEGER} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from zd_good_library_order_detail
        where  id = #{id,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.ZdGoodLibraryOrderDetail" useGeneratedKeys="true">
        insert into zd_good_library_order_detail
        ( id,order_no,good_name
        ,match_degree,org_id,create_time
        ,update_time)
        values (#{id,jdbcType=INTEGER},#{orderNo,jdbcType=VARCHAR},#{goodName,jdbcType=VARCHAR}
        ,#{matchDegree,jdbcType=INTEGER},#{orgId,jdbcType=INTEGER},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.ZdGoodLibraryOrderDetail" useGeneratedKeys="true">
        insert into zd_good_library_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="orderNo != null">order_no,</if>
                <if test="goodName != null">good_name,</if>
                <if test="matchDegree != null">match_degree,</if>
                <if test="orgId != null">org_id,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
                <if test="goodName != null">#{goodName,jdbcType=VARCHAR},</if>
                <if test="matchDegree != null">#{matchDegree,jdbcType=INTEGER},</if>
                <if test="orgId != null">#{orgId,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <insert id="batchInsertSelective">
        <foreach collection="col" item="element" index="index" separator=";">
        insert into zd_good_library_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="element.id != null">id,</if>
            <if test="element.orderNo != null">order_no,</if>
            <if test="element.goodName != null">good_name,</if>
            <if test="element.matchDegree != null">match_degree,</if>
            <if test="element.orgId != null">org_id,</if>
            <if test="element.createTime != null">create_time,</if>
            <if test="element.updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="element.id != null">#{element.id,jdbcType=INTEGER},</if>
            <if test="element.orderNo != null">#{element.orderNo,jdbcType=VARCHAR},</if>
            <if test="element.goodName != null">#{element.goodName,jdbcType=VARCHAR},</if>
            <if test="element.matchDegree != null">#{element.matchDegree,jdbcType=INTEGER},</if>
            <if test="element.orgId != null">#{element.orgId,jdbcType=INTEGER},</if>
            <if test="element.createTime != null">#{element.createTime,jdbcType=TIMESTAMP},</if>
            <if test="element.updateTime != null">#{element.updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.ZdGoodLibraryOrderDetail">
        update zd_good_library_order_detail
        <set>
                <if test="orderNo != null">
                    order_no = #{orderNo,jdbcType=VARCHAR},
                </if>
                <if test="goodName != null">
                    good_name = #{goodName,jdbcType=VARCHAR},
                </if>
                <if test="matchDegree != null">
                    match_degree = #{matchDegree,jdbcType=INTEGER},
                </if>
                <if test="orgId != null">
                    org_id = #{orgId,jdbcType=INTEGER},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.ZdGoodLibraryOrderDetail">
        update zd_good_library_order_detail
        set 
            order_no =  #{orderNo,jdbcType=VARCHAR},
            good_name =  #{goodName,jdbcType=VARCHAR},
            match_degree =  #{matchDegree,jdbcType=INTEGER},
            org_id =  #{orgId,jdbcType=INTEGER},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=INTEGER} 
    </update>
</mapper>
