package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.OrderDetailAcceptancePicDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderDetailAcceptancePicMapper {

    /**
     * 根据订单ID删除关联图片信息
     *
     * @param orderId 订单ID
     */
    int deleteByOrderId(@Param("orderId") Integer orderId);

    /**
     * 根据订单ID查询详情关联图片信息
     *
     * @param orderId 订单ID
     */
    List<OrderDetailAcceptancePicDO> selectByOrderId(@Param("orderId") Integer orderId);
    
    /**
     * 根据订单ID集合批量实体
     *
     * @param orderIds 订单ID集合
     * @return 详情关联图片信息列表
     */
    List<OrderDetailAcceptancePicDO> selectByOrderIds(@Param("orderIds") List<Integer> orderIds);

    int insertSelective(OrderDetailAcceptancePicDO record);

    int updateByPrimaryKeySelective(OrderDetailAcceptancePicDO record);

    int batchInsert(@Param("list") List<OrderDetailAcceptancePicDO> list);
}