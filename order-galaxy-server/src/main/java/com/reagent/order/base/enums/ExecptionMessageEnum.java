package com.reagent.order.base.enums;

import com.ruijing.order.constant.CommonConstant;
import com.ruijing.order.enums.IBaseTemplateEnum;

/**
 * @author: chenzhanliang
 * @createTime: 2024-10-24 17:29
 * @description:
 **/
public enum ExecptionMessageEnum implements IBaseTemplateEnum {

    INPUT_QUANTITY_EXCEEDS_REMAINING("input.quantity.exceeds.remaining","{{0}} 录入数量大于剩余商品数量，请重试"),
    USER_NO_PURCHASING_CENTER("user.no.purchasing.center","当前用户不存在采购中心，请重新登录或使用正确用户"),
    USER_DISABLED_CONTACT_ADMIN("user.disabled.contact.admin","此用户已被禁用，暂时无法进行相关操作。可联系相关负责人进行账户激活"),
    ;

    /**
     * 模板code
     */
    private final String templateCode;

    /**
     * 模板模板
     */
    private final String templateContent;

    /**
     * 获取完整模板Code
     */
    @Override
    public String getTemplateCode() {
        return templateCode;
    }

    /**
     * 获取模板Code前缀
     */
    @Override
    public String getTemplateCodePrefix() {
        return CommonConstant.LOCALE_PREFIX_ORDER_GALAXY;
    }

    @Override
    public String getTemplateContent() {
        return templateContent;
    }

    ExecptionMessageEnum(String templateCode, String templateContent) {
        this.templateCode = templateCode;
        this.templateContent = templateContent;
    }

}
