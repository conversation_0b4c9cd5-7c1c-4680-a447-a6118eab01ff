package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.dto.PublicityOrderParamDTO;import com.reagent.order.base.order.model.OrderBase;
import java.util.Collection;import java.util.Date;import java.util.List;
import com.reagent.order.base.rentcar.dto.SuppRentcarOrderBaseParamDTO;import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description: orderBaseMapper
 */
public interface OrderBaseMapper {

    /**
     * 根据主键删除
     *
     * @param id 主键
     * @return int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 根据订单号 查询订单信息
     * @param orderNumber 订单号
     * @return 订单基础信息List
     */
    OrderBase findByOrderNumber(@Param("orderNumber")String orderNumber);

    /**
     * 新增 orderBase
     *
     * @param record 入参
     * @return int
     */
    int insert(OrderBase record);

    /**
     * 选择性新增
     *
     * @param record 入参
     * @return int
     */
    int insertSelective(OrderBase record);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return OrderBase
     */
    OrderBase selectByPrimaryKey(Long id);

    /**
     * 根据主键选择性更新
     *
     * @param record 入参
     * @return 结果
     */
    int updateByPrimaryKeySelective(OrderBase record);

    /**
     * 根据主键更新
     *
     * @param record 入参
     * @return 结果
     */
    int updateByPrimaryKey(OrderBase record);

    /**
     * 批量插入orderBase
     * @param list 订单集合
     * @return
     */
    int batchInsert(@Param("list") List<OrderBase> list);

    /**
     * 采购人查询orderbase
     *
     * @param buyerId       采购人id
     * @param piDepartmentIds  有pi权限课题组id
     * @param businessType  业务类型
     * @param startTime     起始时间
     * @param endTime       截止时间
     * @param searchContext 搜索内容
     * @return orderBaseList
     */
    List<OrderBase> findRentcarOrderForBuyer(@Param("buyerId") Long buyerId, @Param("piDepartmentIds") List<Long> piDepartmentIds
            , @Param("businessType") Integer businessType, @Param("startTime") Date startTime, @Param("endTime") Date endTime
            , @Param("searchContext") String searchContext);

    /**
     * 供应商查询 orderbase
     *
     * @param suppRentcarOrderBaseParamDTO 入参
     * @return orderList
     */
    List<OrderBase> findRentcarOrderForSupp(SuppRentcarOrderBaseParamDTO suppRentcarOrderBaseParamDTO);

    /**
     * 根据 id 修改订单状态
     *
     * @param orderStatus 订单状态
     * @param id          id
     * @return int
     */
    int updateOrderStatusById(@Param("orderStatus") Integer orderStatus, @Param("id") Long id);

    /**
     * 根据 订单号 修改订单状态
     *
     * @param orderStatus 订单状态
     * @param orderNumber id
     * @return int
     */
    int updateOrderStatusByOrderNumber(@Param("orderStatus") Integer orderStatus, @Param("orderNumber") String orderNumber);

    /**
     * 根据 id 集合查询orderBase列表
     *
     * @param idCollection
     * @return
     */
    List<OrderBase> findByIdIn(@Param("idCollection") Collection<Long> idCollection);

    /**
     * 根据采购人id 和订单状态 查询订单个数
     *
     * @param buyerId     采购人id
     * @param orderStatus 订单状态
     * @return count 订单个数
     */
    Integer countByBuyerIdAndOrderStatus(@Param("buyerId") Long buyerId, @Param("orderStatus") Integer orderStatus);

    /**
     * 根据采购人id 和订单状态集合查询订单
     *
     * @param buyerId               采购人id
     * @param orderStatusCollection 订单状态
     * @param businessType          业务类型
     * @param size                  范围
     * @return orderBaseList
     */
    List<OrderBase> findByBuyerIdAndOrderStatusList(@Param("buyerId") Long buyerId, @Param("orderStatusCollection") Collection<Integer> orderStatusCollection, @Param("businessType") Integer businessType, @Param("size") Integer size);

    /**
     * 公示 展示 订单接口
     *
     * @param publicityOrderParamDTO orderStatusCollection  订单状态集合
     * @param publicityOrderParamDTO containingSupplierName  供应商名称
     * @param publicityOrderParamDTO processType   流程类型
     * @param publicityOrderParamDTO businessType 业务类型
     * @param publicityOrderParamDTO rideType      车类型
     * @return
     */
    List<OrderBase> findPublicityOrder(PublicityOrderParamDTO publicityOrderParamDTO);

    /**
     * 根据行程记录的查询和排序规则，查询行程记录
     * @param buyerId 采购人id
     * @param businessType 业务类型
     * @return 订单列表
     */
    List<OrderBase> getTravelRecordList(@Param("buyerId")Long buyerId,@Param("businessType")Integer businessType);

    /**
     * 根据订单状态 查询 超时订单
     * @param orderStatusCollection 订单状态
     * @param timeOutDay 超时天数
     * @param businessType 业务类型
     * @return 订单列表
     */
    List<OrderBase> getTimeOutNoticeOrder(@Param("orderStatusCollection")Collection<Integer> orderStatusCollection,@Param("businessType")Integer businessType,@Param("timeOutDay")Integer timeOutDay);



}