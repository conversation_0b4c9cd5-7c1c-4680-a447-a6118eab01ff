<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.BusinessDockingMapper">
  <sql id="Base_Column_List">
	id,
	business_order_no,
	docking_no,
	reagent_status,
	extra_json,
	extra_status,
	org_id,
	org_code,
	remark,
	create_time,
	update_time
</sql>
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.BusinessDockingDO">
    <!--@mbg.generated-->
    <!--@Table business_docking-->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="business_order_no" jdbcType="VARCHAR" property="businessOrderNo" />
    <result column="docking_no" jdbcType="VARCHAR" property="dockingNo" />
    <result column="reagent_status" jdbcType="INTEGER" property="reagentStatus" />
    <result column="extra_json" jdbcType="VARCHAR" property="extraJson" />
    <result column="extra_status" jdbcType="INTEGER" property="extraStatus" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <insert id="insert" parameterType="com.reagent.order.base.order.model.BusinessDockingDO">
    <!--@mbg.generated-->
    insert into business_docking (id, business_order_no, docking_no, 
      reagent_status, extra_json, extra_status, 
      org_id, org_code, remark, 
      create_time, update_time)
    values (#{id,jdbcType=INTEGER}, #{businessOrderNo,jdbcType=VARCHAR}, #{dockingNo,jdbcType=VARCHAR}, 
      #{reagentStatus,jdbcType=INTEGER}, #{extraJson,jdbcType=VARCHAR}, #{extraStatus,jdbcType=INTEGER}, 
      #{orgId,jdbcType=INTEGER}, #{orgCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.reagent.order.base.order.model.BusinessDockingDO">
    <!--@mbg.generated-->
    insert into business_docking
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessOrderNo != null">
        business_order_no,
      </if>
      <if test="dockingNo != null">
        docking_no,
      </if>
      <if test="reagentStatus != null">
        reagent_status,
      </if>
      <if test="extraJson != null">
        extra_json,
      </if>
      <if test="extraStatus != null">
        extra_status,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="businessOrderNo != null">
        #{businessOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="dockingNo != null">
        #{dockingNo,jdbcType=VARCHAR},
      </if>
      <if test="reagentStatus != null">
        #{reagentStatus,jdbcType=INTEGER},
      </if>
      <if test="extraJson != null">
        #{extraJson,jdbcType=VARCHAR},
      </if>
      <if test="extraStatus != null">
        #{extraStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2020-08-18-->
  <select id="findByBusinessOrderNoIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from business_docking
    where business_order_no in
    <foreach collection="businessOrderNoList" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=VARCHAR}
    </foreach>

  </select>

<!--auto generated by MybatisCodeHelper on 2020-08-24-->
  <update id="updateByBusinessOrderNo">
        update business_docking
        <set>
            <if test="updated.id != null">
                id = #{updated.id,jdbcType=INTEGER},
            </if>
            <if test="updated.businessOrderNo != null">
                business_order_no = #{updated.businessOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="updated.dockingNo != null">
                docking_no = #{updated.dockingNo,jdbcType=VARCHAR},
            </if>
            <if test="updated.reagentStatus != null">
                reagent_status = #{updated.reagentStatus,jdbcType=INTEGER},
            </if>
            <if test="updated.extraJson != null">
                extra_json = #{updated.extraJson,jdbcType=VARCHAR},
            </if>
            <if test="updated.extraStatus != null">
                extra_status = #{updated.extraStatus,jdbcType=INTEGER},
            </if>
            <if test="updated.orgId != null">
                org_id = #{updated.orgId,jdbcType=INTEGER},
            </if>
            <if test="updated.orgCode != null">
                org_code = #{updated.orgCode,jdbcType=VARCHAR},
            </if>
            <if test="updated.remark != null">
                remark = #{updated.remark,jdbcType=VARCHAR},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where business_order_no=#{updated.businessOrderNo,jdbcType=VARCHAR}
    </update>

<!--auto generated by MybatisCodeHelper on 2020-08-25-->
  <insert id="insertList">
        INSERT INTO business_docking(
        business_order_no,
        docking_no,
        reagent_status,
        extra_json,
        extra_status,
        org_id,
        org_code,
        remark
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.businessOrderNo,jdbcType=VARCHAR},
            #{element.dockingNo,jdbcType=VARCHAR},
            <choose>
                <when test="element.reagentStatus != null ">
                    #{element.reagentStatus,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            <choose>
                <when test="element.extraJson != null ">
                    #{element.extraJson,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>

            <choose>
                <when test="element.extraStatus != null ">
                    #{element.extraStatus,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>

            <choose>
                <when test="element.orgId != null ">
                    #{element.orgId,jdbcType=INTEGER},
                </when>
                <otherwise>
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="element.orgCode != null ">
                    #{element.orgCode,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    '',
                </otherwise>
            </choose>
            #{element.remark,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2020-09-11-->
  <select id="findByOrgCodeAndReagentStatusIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from business_docking
      where 1 = 1
      <if test="orgCode != null ">
          and org_code = #{orgCode,jdbcType=VARCHAR}
      </if>

      <if test="reagentStatusCollection != null and reagentStatusCollection.size() != 0">
          and reagent_status in
          <foreach item="item" index="index" collection="reagentStatusCollection"
                   open="(" separator="," close=")">
              #{item,jdbcType=INTEGER}
          </foreach>
      </if>
    </select>

<!--auto generated by MybatisCodeHelper on 2020-09-11-->
  <update id="updateBatchByBusinessOrderNoIn">
      update business_docking
      <trim prefix="set" suffixOverrides=",">
          <trim prefix="reagent_status =case" suffix="end,">
              <foreach collection="list" item="item" index="index">
                  when business_order_no=#{item.businessOrderNo,jdbcType=VARCHAR} then #{item.reagentStatus}
              </foreach>
          </trim>

          <trim prefix="extra_json =case" suffix="end,">
              <foreach collection="list" item="item" index="index">
                  when business_order_no=#{item.businessOrderNo,jdbcType=VARCHAR} then #{item.extraJson,jdbcType=VARCHAR}
              </foreach>
          </trim>
      </trim>
      where business_order_no in
      <foreach item="item" index="index" collection="list"
               open="(" separator="," close=")">
          #{item.businessOrderNo,jdbcType=VARCHAR}
      </foreach>
    </update>
</mapper>