package com.reagent.order.base.order.model;

import java.util.Date;

public class OrderBankAccountSnapshotDO {
    private Integer id;

    /**
    * 订单id
    */
    private Integer orderId;

    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 单位id
    */
    private Integer orgId;

    /**
     * 银行id
     */
    private Integer bankId;

    /**
    * 银行开户名
    */
    private String bankAccountName;

    /**
    * 所属银行
    */
    private String bankName;

    /**
    * 开户行名称
    */
    private String bankBranch;

    /**
    * 支行联行号
    */
    private String bankCode;

    /**
    * 省份代码
    */
    private String provinceCode;

    /**
    * 城市代码
    */
    private String cityCode;

    /**
    * 银行卡号
    */
    private String bankCardNumber;

    /**
     * 账户类型
     */
    private Integer accountType;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getBankId() {
        return bankId;
    }

    public void setBankId(Integer bankId) {
        this.bankId = bankId;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getBankCardNumber() {
        return bankCardNumber;
    }

    public void setBankCardNumber(String bankCardNumber) {
        this.bankCardNumber = bankCardNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public OrderBankAccountSnapshotDO setAccountType(Integer accountType) {
        this.accountType = accountType;
        return this;
    }
}