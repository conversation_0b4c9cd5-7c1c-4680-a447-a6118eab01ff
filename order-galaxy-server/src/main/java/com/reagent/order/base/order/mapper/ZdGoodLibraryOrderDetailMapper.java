package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.ZdGoodLibraryOrderDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zd_good_library_order_detail(中大非服务类匹配订单)】的数据库操作Mapper
* @createDate 2023-11-10 15:04:23
* @Entity com.reagent.order.base.order.model.ZdGoodLibraryOrderDetail
*/
public interface ZdGoodLibraryOrderDetailMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ZdGoodLibraryOrderDetail record);

    int insertSelective(ZdGoodLibraryOrderDetail record);

    ZdGoodLibraryOrderDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZdGoodLibraryOrderDetail record);

    int updateByPrimaryKey(ZdGoodLibraryOrderDetail record);

    void batchInsertSelective(@Param("col") List<ZdGoodLibraryOrderDetail> collect);
}
