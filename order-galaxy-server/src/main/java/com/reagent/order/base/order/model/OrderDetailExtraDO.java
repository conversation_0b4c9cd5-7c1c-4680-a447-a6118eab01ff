package com.reagent.order.base.order.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单详情额外信息表
 * @TableName order_detail_extra
 */
public class OrderDetailExtraDO implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 订单主表id，0无意义
     */
    private Integer orderId;

    /**
     * 订单详情id
     */
    private Integer orderDetailId;

    /**
     * 单位id，0无意义
     */
    private Integer orgId;

    /**
     * 单位个性化操作类型,由枚举维护
     */
    private String extraKey;

    /**
     * key类型
     */
    private Integer extraKeyType;

    /**
     * 单位个性化描述字串
     */
    private String extraValue;

    /**
     * 生成时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Integer getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 订单主表id，0无意义
     */
    public Integer getOrderId() {
        return orderId;
    }

    /**
     * 订单主表id，0无意义
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * 订单详情id
     */
    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    /**
     * 订单详情id
     */
    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    /**
     * 单位id，0无意义
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * 单位id，0无意义
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * 单位个性化操作类型,由枚举维护
     */
    public String getExtraKey() {
        return extraKey;
    }

    /**
     * 单位个性化操作类型,由枚举维护
     */
    public void setExtraKey(String extraKey) {
        this.extraKey = extraKey;
    }

    /**
     * 单位个性化描述字串
     */
    public String getExtraValue() {
        return extraValue;
    }

    /**
     * 单位个性化描述字串
     */
    public void setExtraValue(String extraValue) {
        this.extraValue = extraValue;
    }

    /**
     * 生成时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 生成时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getExtraKeyType() {
        return extraKeyType;
    }

    public void setExtraKeyType(Integer extraKeyType) {
        this.extraKeyType = extraKeyType;
    }
}