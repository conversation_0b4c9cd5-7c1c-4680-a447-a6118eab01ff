package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderEventStatusDTO;
import com.reagent.order.base.order.model.OrderEventStatusDO;
import com.ruijing.fundamental.common.collections.New;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 14:48
 * @description
 */
public class OrderEventStatusTranslator {
    /**
     * do to dto
     * @param input
     * @return
     */
    public static OrderEventStatusDTO doToDto(OrderEventStatusDO input) {
        if(input == null){
            return null;
        }         
        OrderEventStatusDTO output = new OrderEventStatusDTO();
        output.setId(input.getId());
        output.setEventStatus(input.getEventStatus());
        output.setEventType(input.getEventType());
        output.setOrderNo(input.getOrderNo());
        output.setOrgId(input.getOrgId());
        output.setUpdateTime(input.getUpdateTime());
        output.setCreateTime(output.getCreateTime());
        return output;
    }

    /**
     * do list to dto list
     * @param inputList
     * @return
     */
    public static List<OrderEventStatusDTO> doListToDtoList(List<OrderEventStatusDO> inputList) {
        List<OrderEventStatusDTO> outputList = New.list();
        for (OrderEventStatusDO OrderEventStatusDO : inputList) {
            outputList.add(doToDto(OrderEventStatusDO));
        }
        return outputList;
    }

    /**
     * dto to do
     * @param input
     * @return
     */
    public static OrderEventStatusDO dtoToDo(OrderEventStatusDTO input) {
        if(input == null){
            return null;
        }
        OrderEventStatusDO output = new OrderEventStatusDO();
        output.setId(input.getId());
        output.setEventStatus(input.getEventStatus());
        output.setEventType(input.getEventType());
        output.setOrderNo(input.getOrderNo());
        output.setOrgId(input.getOrgId());
        output.setUpdateTime(input.getUpdateTime());
        output.setCreateTime(output.getCreateTime());
        return output;
    }

    /**
     * dto list to do list
     * @param inputList
     * @return
     */
    public static List<OrderEventStatusDO> dtoListToDoList(List<OrderEventStatusDTO> inputList) {
        List<OrderEventStatusDO> outputList = New.list();
        for (OrderEventStatusDTO input : inputList) {
            outputList.add(dtoToDo(input));
        }
        return outputList;
    }
}
