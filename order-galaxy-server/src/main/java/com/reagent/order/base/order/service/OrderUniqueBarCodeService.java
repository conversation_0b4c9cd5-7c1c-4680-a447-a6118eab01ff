package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.request.OrderQRCodePageRequestDTO;
import com.reagent.order.base.order.model.OrderUniqueBarCodeDO;
import com.ruijing.fundamental.api.remote.PageableResponse;

import java.util.List;

public interface OrderUniqueBarCodeService {

    /**
     * 查询商品订单明细批次
     * @param detailId
     * @return 批次记录
     */
    List<OrderUniqueBarCodeDTO> findByDetailId(Integer detailId, List<Integer> typeList);

    /**
     * 只查询一条批次信息
     * @param detailId
     * @return 批次记录
     */
    OrderUniqueBarCodeDTO findFirstByDetailId(Integer detailId, List<Integer> typeList);

    /**
     * 查询商品订单明细批次
     * @param detailIdList
     * @param status
     * @return 批次记录
     */
    List<OrderUniqueBarCodeDTO> findByDetailId(List<Integer> detailIdList, List<Integer> typeList, Integer status);

    /**
     * 查询商品订单明细批次
     * @param detailIdList
     * @return 批次记录
     */
    List<OrderUniqueBarCodeDTO> findByDetailId(List<Integer> detailIdList, List<Integer> typeList);

    /**
     * 根据barCode查询一条批次信息
     */
    OrderUniqueBarCodeDTO findByBarCode(String barCode);

    /**
     * 批量更新二维码信息
     * @param updatedList
     * @return 更新数量
     */
    int batchUpdateOrderUniqueBarCode(List<OrderUniqueBarCodeDTO> updatedList);

    /**
     * 批量插入二维码信息
     * @param list
     * @return 插入条数
     */
    int batchInsert(List<OrderUniqueBarCodeDTO> list);

    /**
     * 查询订单是否已存在二维码，只查单位生成的
     * @param orderNo   订单号
     * @return          数量
     */
    int findExistedByOrderNo(String orderNo);

    /**
     * 查询订单批次码信息
     * @param orderNo   订单号
     * @return          批次码信息
     */
    List<OrderUniqueBarCodeDTO> findByOrderNo(String orderNo, List<Integer> typeList);

    /**
     * 查询订单批次码信息
     *
     * @param orderNoList 订单号
     * @param typeList
     * @return 批次码信息
     */
    List<OrderUniqueBarCodeDTO> findByOrderNoList(List<String> orderNoList, List<Integer> typeList);

    /**
     * 查询订单批次码信息
     * @param barCodeList   条形码信息
     * @return              批次码信息
     */
    List<OrderUniqueBarCodeDTO> findByBarCodeList(List<String> barCodeList);

    /**
     * 查询订单批次码信息
     *
     * @param entryNo  入库单号
     * @param typeList
     * @return 批次码信息
     */
    List<OrderUniqueBarCodeDTO> findByEntryNo(String entryNo, List<Integer> typeList);

    /**
     * 查询订单批次码信息
     *
     * @param applyNo  申领单号
     * @param typeList
     * @return 批次码信息
     */
    List<OrderUniqueBarCodeDTO> findByApplyNo(String applyNo, List<Integer> typeList);

    /**
     * 查询订单批次码信息
     *
     * @param exitNo   出库单号
     * @param typeList
     * @return 批次码信息
     */
    List<OrderUniqueBarCodeDTO> findByExitNo(String exitNo, List<Integer> typeList);

    /**
     * 查询订单批次码信息r
     *
     * @param returnNo 退货单号
     * @param typeList
     * @return 批次码信息
     */
    List<OrderUniqueBarCodeDTO> findByReturnNo(String returnNo, List<Integer> typeList);

    /**
     * 根据入库单更新码的状态
     *
     * @param entryNo       入库单号
     * @param exitNo        出库单号
     * @param updatedStatus 更新状态
     * @param expectStatus  期望状态
     * @param typeList
     * @return 更新数量
     */
    int compareAndSetByEntryNoAndStatus(String entryNo, String exitNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList);

    /**
     * 根据申领单更新码的状态
     * @param applyNo       申领单号
     * @param updatedStatus 更新状态
     * @param expectStatus  期望状态
     * @return              更新数量
     */
    int compareAndSetByApplyNoAndStatus(String applyNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList);

    /**
     * 根据出库单更新码的状态
     *
     * @param exitNo        出库单号
     * @param updatedStatus 更新状态
     * @param expectStatus  期望状态
     * @param typeList
     * @return 更新数量
     */
    int compareAndSetByExitNoAndStatus(String exitNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList);

    /**
     * 根据退货单更新码的状态
     * @param returnNo      退货单号
     * @param updatedStatus 更新状态
     * @param expectStatus  期望状态
     * @return              更新数量
     */
    int compareAndSetByReturnNoAndStatus(String returnNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList);

    /**
     * 根据退货单更新码的状态
     *
     * @param orderNo       退货单号
     * @param updatedStatus 更新状态
     * @param expectStatus  期望状态
     * @param typeList
     * @return 更新数量
     */
    int compareAndSetByOrderNoAndStatus(String orderNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList);

    /**
     * 通过业务号更新状态
     * @param updateParam 更新参数
     * @return 更新条目
     */
    int updateStatusByBusinessNo(OrderUniqueBarCodeDO updateParam, List<Integer> typeList);

    /**
     * 删除订单关联的二维码
     * @param orderNo       订单号
     * @return              删除数量
     */
    int deleteByOrderNo(String orderNo, List<Integer> typeList);

    /**
     * 分页查询二维码信息
     * @param request       分页参数
     * @return              分页结果
     */
    PageableResponse<List<OrderUniqueBarCodeDTO>> queryPage(OrderQRCodePageRequestDTO request);

    @Deprecated
    List<OrderUniqueBarCodeDTO> pageByBarcode(String orderNo, String startBarcode, Integer limit);
}
