package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderDetailAcceptancePicDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptancePicRequestDTO;
import com.reagent.order.base.order.model.OrderDetailAcceptancePicDO;
import com.ruijing.fundamental.common.collections.New;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class OrderDetailAcceptancePicTranslator {

    public static OrderDetailAcceptancePicDTO do2Dto(OrderDetailAcceptancePicDO orderDetailAcceptancePicDO) {
        if (Objects.isNull(orderDetailAcceptancePicDO)) {
            return null;
        }
        OrderDetailAcceptancePicDTO dto = new OrderDetailAcceptancePicDTO();
        dto.setId(orderDetailAcceptancePicDO.getId());
        dto.setOrderId(orderDetailAcceptancePicDO.getOrderId());
        dto.setDetailId(orderDetailAcceptancePicDO.getDetailId());
        dto.setUrl(orderDetailAcceptancePicDO.getUrl());
        dto.setFileName(orderDetailAcceptancePicDO.getFileName());
        dto.setCreateTime(orderDetailAcceptancePicDO.getCreateTime());
        dto.setUpdateTime(orderDetailAcceptancePicDO.getUpdateTime());
        return dto;
    }

    public static List<OrderDetailAcceptancePicDTO> listDo2Dto(List<OrderDetailAcceptancePicDO> orderDetailAcceptancePicDOList) {
        if (CollectionUtils.isEmpty(orderDetailAcceptancePicDOList)) {
            return New.emptyList();
        }
        return orderDetailAcceptancePicDOList.stream()
                .map(OrderDetailAcceptancePicTranslator::do2Dto)
                .collect(Collectors.toList());
    }



    public static OrderDetailAcceptancePicDO reqDto2Do(OrderDetailAcceptancePicRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)) {
            return null;
        }
        OrderDetailAcceptancePicDO doObj = new OrderDetailAcceptancePicDO();
        doObj.setOrderId(requestDTO.getOrderId());
        doObj.setDetailId(requestDTO.getDetailId());
        doObj.setUrl(requestDTO.getUrl());
        doObj.setFileName(requestDTO.getFileName());
        return doObj;
    }


    public static List<OrderDetailAcceptancePicDO> listReqDto2Do(List<OrderDetailAcceptancePicRequestDTO> requestDTOList) {
        if (CollectionUtils.isEmpty(requestDTOList)) {
            return New.emptyList();
        }
        return requestDTOList.stream()
                .map(OrderDetailAcceptancePicTranslator::reqDto2Do)
                .collect(Collectors.toList());
    }

}
