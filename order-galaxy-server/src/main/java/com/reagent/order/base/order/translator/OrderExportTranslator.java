package com.reagent.order.base.order.translator;
import java.util.Date;

import com.reagent.order.base.order.dto.OrderExportDTO;
import com.reagent.order.base.order.model.OrderExportDO;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 14:20
 * @Version 1.0
 * @Desc:描述
 */
public class OrderExportTranslator {


    /**
     * dtoToDo
     * @param orderExportDTO
     * @return
     */
    public static OrderExportDO dtoToDo(OrderExportDTO orderExportDTO) {
        if (orderExportDTO == null) {
            return null;
        }
        OrderExportDO orderExportDO = new OrderExportDO();
        orderExportDO.setId(orderExportDTO.getId());
        orderExportDO.setFileName(orderExportDTO.getFileName());
        orderExportDO.setExportDate(orderExportDTO.getExportDate());
        orderExportDO.setOrgId(orderExportDTO.getOrgId());
        orderExportDO.setOrgCode(orderExportDTO.getOrgCode());
        orderExportDO.setOrgName(orderExportDTO.getOrgName());
        orderExportDO.setUserId(orderExportDTO.getUserId());
        orderExportDO.setUserName(orderExportDTO.getUserName());
        orderExportDO.setStatus(orderExportDTO.getStatus());
        orderExportDO.setFileType(orderExportDTO.getFileType());
        orderExportDO.setFileUrl(orderExportDTO.getFileUrl());
        orderExportDO.setFailReason(orderExportDTO.getFailReason());
        orderExportDO.setCreatedTime(orderExportDTO.getCreatedTime());
        orderExportDO.setUpdatedTime(orderExportDTO.getUpdatedTime());
        return orderExportDO;
    }

    /**
     * doToDto
     * @param orderExportDO
     * @return
     */
    public static OrderExportDTO doToDto(OrderExportDO orderExportDO) {
        if (orderExportDO == null) {
            return null;
        }
        OrderExportDTO orderExportDTO = new OrderExportDTO();
        orderExportDTO.setId(orderExportDO.getId());
        orderExportDTO.setFileName(orderExportDO.getFileName());
        orderExportDTO.setExportDate(orderExportDO.getExportDate());
        orderExportDTO.setUserId(orderExportDO.getUserId());
        orderExportDTO.setUserName(orderExportDO.getUserName());
        orderExportDTO.setStatus(orderExportDO.getStatus());
        orderExportDTO.setFileType(orderExportDO.getFileType());
        orderExportDTO.setFileUrl(orderExportDO.getFileUrl());
        orderExportDTO.setFailReason(orderExportDO.getFailReason());
        orderExportDTO.setCreatedTime(orderExportDO.getCreatedTime());
        orderExportDTO.setUpdatedTime(orderExportDO.getUpdatedTime());
        return orderExportDTO;
    }
}
