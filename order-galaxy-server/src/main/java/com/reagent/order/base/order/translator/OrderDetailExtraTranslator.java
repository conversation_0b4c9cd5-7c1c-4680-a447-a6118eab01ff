package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderDetailExtraDTO;
import com.reagent.order.base.order.model.OrderDetailExtraDO;

/**
 * <AUTHOR>
 * @description OrderDetailExtra转换器
 * @date 2023/7/18 14:04
 */
public class OrderDetailExtraTranslator {

    public static OrderDetailExtraDO fromDTO(OrderDetailExtraDTO dto){
        OrderDetailExtraDO entity = new OrderDetailExtraDO();
        entity.setId(dto.getId());
        entity.setOrgId(dto.getOrgId());
        entity.setOrderId(dto.getOrderId());
        entity.setOrderDetailId(dto.getOrderDetailId());
        entity.setExtraKey(dto.getExtraKey());
        entity.setExtraValue(dto.getExtraValue());
        entity.setExtraKeyType(dto.getExtraKeyType());
        return entity;
    }

    public static OrderDetailExtraDTO toDTO(OrderDetailExtraDO entity){
        OrderDetailExtraDTO dto = new OrderDetailExtraDTO();
        dto.setId(entity.getId());
        dto.setOrderId(entity.getOrderId());
        dto.setOrderDetailId(entity.getOrderDetailId());
        dto.setOrgId(entity.getOrgId());
        dto.setExtraKey(entity.getExtraKey());
        dto.setExtraValue(entity.getExtraValue());
        dto.setExtraKeyType(entity.getExtraKeyType());
        return dto;
    }
}
