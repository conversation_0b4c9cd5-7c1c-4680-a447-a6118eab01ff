<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderDetailAcceptanceFileMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderDetailAcceptanceFileDO">
    <!--@mbg.generated-->
    <!--@Table order_detail_acceptance_file-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="detail_id" jdbcType="INTEGER" property="detailId" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, detail_id, url, file_name, create_time, update_time
  </sql>
  <select id="selectByOrderId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_detail_acceptance_file
    where order_id = #{orderId,jdbcType=INTEGER}
  </select>
  
  <select id="selectByOrderIds" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_detail_acceptance_file
    where order_id in
    <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
      #{orderId,jdbcType=INTEGER}
    </foreach>
  </select>
  
  <delete id="deleteByOrderId" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from order_detail_acceptance_file
    where order_id = #{orderId,jdbcType=INTEGER}

  </delete>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderDetailAcceptanceFileDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_detail_acceptance_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="detailId != null">
        detail_id,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="detailId != null">
        #{detailId,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderDetailAcceptanceFileDO">
    <!--@mbg.generated-->
    update order_detail_acceptance_file
    <set>
      <if test="detailId != null">
        detail_id = #{detailId,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_detail_acceptance_file
    (order_id, detail_id, url, file_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=INTEGER}, #{item.detailId,jdbcType=INTEGER}, #{item.url,jdbcType=VARCHAR},
      #{item.fileName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>