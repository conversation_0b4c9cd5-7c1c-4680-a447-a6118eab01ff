package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.OrderDetailBatchesDTO;

import java.util.Collection;
import java.util.List;

public interface OrderDetailBaseService {

    /**
     * 更新订单明细批次
     * @param list
     * @return 成功数量
     */
    int saveOrderDetailBatches(List<OrderDetailBatchesDTO> list);

    /**
     * 查询商品订单明细批次
     * @param detailIdCollection
     * @return 批次记录
     */
    List<OrderDetailBatchesDTO> findByDetailIdIn(Collection<Integer> detailIdCollection);
}
