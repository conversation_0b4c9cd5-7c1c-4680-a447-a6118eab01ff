package com.reagent.order.base.log.translator;

import com.reagent.order.base.log.dto.OrderDockingLogDTO;
import com.reagent.order.base.log.dto.OrderOperationLogDTO;
import com.reagent.order.base.log.model.OrderDockingLog;
import com.reagent.order.base.log.model.OrderOperationLog;

/**
 * @description: 订单日志转换对象
 * @author: zhuk
 * @create: 2019-08-01 09:53
 **/
public class OrderLogTranslator {

    private OrderLogTranslator() {
    }

    /**
     * OrderDockingLogDTO 转 OrderDockingLog
     * @param orderDockingLogDTO OrderDockingLogDTO
     * @return OrderDockingLog
     */
    public static OrderDockingLog dto2OrderDockingLog(OrderDockingLogDTO orderDockingLogDTO) {
        OrderDockingLog orderDockingLog = new OrderDockingLog();
        orderDockingLog.setId(orderDockingLogDTO.getId());
        orderDockingLog.setDockingNumber(orderDockingLogDTO.getDockingNumber());
        orderDockingLog.setParamInfo(orderDockingLogDTO.getParamInfo());
        orderDockingLog.setExtraInfo(orderDockingLogDTO.getExtraInfo());
        orderDockingLog.setOperation(orderDockingLogDTO.getOperation());
        orderDockingLog.setOrgCode(orderDockingLogDTO.getOrgCode());
        orderDockingLog.setResult(orderDockingLogDTO.getResult());
        orderDockingLog.setRemark(orderDockingLogDTO.getRemark());
        orderDockingLog.setCreateTime(orderDockingLogDTO.getCreateTime());
        return orderDockingLog;
    }

    /**
     * DTO  转 orderOperationLog对象
     * @param orderOperationLogDTO
     * @return
     */
    public static OrderOperationLog dtoToOrderOperationLog(OrderOperationLogDTO orderOperationLogDTO){
        OrderOperationLog orderOperationLog= new OrderOperationLog();
        orderOperationLog.setId(orderOperationLogDTO.getId());
        orderOperationLog.setOrderId(orderOperationLogDTO.getOrderId());
        orderOperationLog.setOrderNumber(orderOperationLogDTO.getOrderNumber());
        orderOperationLog.setBusinessType(orderOperationLogDTO.getBusinessType());
        orderOperationLog.setOperation(orderOperationLogDTO.getOperation());
        orderOperationLog.setOperationDesc(orderOperationLogDTO.getOperationDesc());
        orderOperationLog.setOperationNote(orderOperationLogDTO.getOperationNote());
        orderOperationLog.setOperatorId(orderOperationLogDTO.getOperatorId());
        orderOperationLog.setOperatorType(orderOperationLogDTO.getOperatorType());
        orderOperationLog.setOperatorName(orderOperationLogDTO.getOperatorName());
        orderOperationLog.setOperatorDepartId(orderOperationLogDTO.getOperatorDepartId());
        orderOperationLog.setOperatorDepartName(orderOperationLogDTO.getOperatorDepartName());
        orderOperationLog.setOperatorOrgId(orderOperationLogDTO.getOperatorOrgId());
        orderOperationLog.setOperatorOrgCode(orderOperationLogDTO.getOperatorOrgCode());
        orderOperationLog.setOperatorOrgName(orderOperationLogDTO.getOperatorOrgName());
        orderOperationLog.setCreateTime(orderOperationLogDTO.getCreateTime());
        orderOperationLog.setUpdateTime(orderOperationLogDTO.getUpdateTime());
        return orderOperationLog;
    }

    /**
     * orderOperationLog 转 DTO对象
     * @param orderOperationLog 入参
     * @return OrderOperationLogDTO
     */
    public static OrderOperationLogDTO orderOperationLogToDTO(OrderOperationLog orderOperationLog){

        OrderOperationLogDTO orderOperationLogDTO = new OrderOperationLogDTO();
        orderOperationLogDTO.setId(orderOperationLog.getId());
        orderOperationLogDTO.setOrderId(orderOperationLog.getOrderId());
        orderOperationLogDTO.setOrderNumber(orderOperationLog.getOrderNumber());
        orderOperationLogDTO.setBusinessType(orderOperationLog.getBusinessType());
        orderOperationLogDTO.setOperation(orderOperationLog.getOperation());
        orderOperationLogDTO.setOperationDesc(orderOperationLog.getOperationDesc());
        orderOperationLogDTO.setOperationNote(orderOperationLog.getOperationNote());
        orderOperationLogDTO.setOperatorId(orderOperationLog.getOperatorId());
        orderOperationLogDTO.setOperatorType(orderOperationLog.getOperatorType());
        orderOperationLogDTO.setOperatorName(orderOperationLog.getOperatorName());
        orderOperationLogDTO.setOperatorDepartId(orderOperationLog.getOperatorDepartId());
        orderOperationLogDTO.setOperatorDepartName(orderOperationLog.getOperatorDepartName());
        orderOperationLogDTO.setOperatorOrgId(orderOperationLog.getOperatorOrgId());
        orderOperationLogDTO.setOperatorOrgCode(orderOperationLog.getOperatorOrgCode());
        orderOperationLogDTO.setOperatorOrgName(orderOperationLog.getOperatorOrgName());
        orderOperationLogDTO.setCreateTime(orderOperationLog.getCreateTime());
        orderOperationLogDTO.setUpdateTime(orderOperationLog.getUpdateTime());
        return orderOperationLogDTO;

    }

}
