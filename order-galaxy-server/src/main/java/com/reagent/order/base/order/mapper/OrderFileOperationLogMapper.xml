<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderFileOperationLogMapper">
    <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderFileOperationLogDO">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="order_id" jdbcType="INTEGER" property="orderId" />
        <result column="log_id" jdbcType="INTEGER" property="logId" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="file_name" jdbcType="VARCHAR" property="fileName" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, order_id, log_id, url, file_name, create_time, update_time
    </sql>
    
    <select id="selectByOrderIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from order_file_operation_log
        where order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>
    
    <select id="selectByLogIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from order_file_operation_log
        where log_id in
        <foreach collection="logIds" item="logId" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </select>
    
    <insert id="batchInsert" parameterType="java.util.List">
        insert into order_file_operation_log
        (order_id, log_id, url, file_name)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderId}, #{item.logId}, #{item.url}, #{item.fileName})
        </foreach>
    </insert>
</mapper>
