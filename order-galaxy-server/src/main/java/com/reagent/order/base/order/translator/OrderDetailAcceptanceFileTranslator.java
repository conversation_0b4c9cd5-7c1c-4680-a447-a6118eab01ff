package com.reagent.order.base.order.translator;


import com.reagent.order.base.order.dto.OrderDetailAcceptanceFileDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptanceFileRequestDTO;
import com.reagent.order.base.order.model.OrderDetailAcceptanceFileDO;
import com.ruijing.fundamental.common.collections.New;
import org.apache.commons.collections4.CollectionUtils;


import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


public class OrderDetailAcceptanceFileTranslator {


    public static OrderDetailAcceptanceFileDTO do2Dto(OrderDetailAcceptanceFileDO orderDetailAcceptanceFileDO) {
        if (Objects.isNull(orderDetailAcceptanceFileDO)) {
            return null;
        }
        OrderDetailAcceptanceFileDTO dto = new OrderDetailAcceptanceFileDTO();
        dto.setId(orderDetailAcceptanceFileDO.getId());
        dto.setOrderId(orderDetailAcceptanceFileDO.getOrderId());
        dto.setDetailId(orderDetailAcceptanceFileDO.getDetailId());
        dto.setUrl(orderDetailAcceptanceFileDO.getUrl());
        dto.setFileName(orderDetailAcceptanceFileDO.getFileName());
        dto.setCreateTime(orderDetailAcceptanceFileDO.getCreateTime());
        dto.setUpdateTime(orderDetailAcceptanceFileDO.getUpdateTime());
        return dto;
    }


    public static List<OrderDetailAcceptanceFileDTO> listDo2Dto(List<OrderDetailAcceptanceFileDO> orderDetailAcceptanceFileDOList) {
        if (CollectionUtils.isEmpty(orderDetailAcceptanceFileDOList)) {
            return New.emptyList();
        }
        return orderDetailAcceptanceFileDOList.stream()
                .map(OrderDetailAcceptanceFileTranslator::do2Dto)
                .collect(Collectors.toList());
    }


    public static OrderDetailAcceptanceFileDO reqDto2Do(OrderDetailAcceptanceFileRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)) {
            return null;
        }
        OrderDetailAcceptanceFileDO doObj = new OrderDetailAcceptanceFileDO();
        doObj.setOrderId(requestDTO.getOrderId());
        doObj.setDetailId(requestDTO.getDetailId());
        doObj.setUrl(requestDTO.getUrl());
        doObj.setFileName(requestDTO.getFileName());
        return doObj;
    }


    public static List<OrderDetailAcceptanceFileDO> listReqDto2Do(List<OrderDetailAcceptanceFileRequestDTO> requestDTOList) {
        if (CollectionUtils.isEmpty(requestDTOList)) {
            return New.emptyList();
        }
        return requestDTOList.stream()
                .map(OrderDetailAcceptanceFileTranslator::reqDto2Do)
                .collect(Collectors.toList());
    }
}