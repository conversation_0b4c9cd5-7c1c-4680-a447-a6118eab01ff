package com.reagent.order.base.order.mapper;
import java.util.Collection;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.reagent.order.base.order.model.OrderAcceptCommentDO;

public interface OrderAcceptCommentMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(OrderAcceptCommentDO record);

    int insertSelective(OrderAcceptCommentDO record);

    OrderAcceptCommentDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderAcceptCommentDO record);

    int updateByPrimaryKey(OrderAcceptCommentDO record);

    /**
     * @description: 通过订单id列表和单位id查询评价信息
     * @date: 2021/2/26 16:18
     * @author: zengyanru
     * @param orderIdCollection
     * @param orgId
     * @return java.util.List<com.reagent.order.base.order.model.OrderAcceptCommentDO>
     */
    List<OrderAcceptCommentDO> findByOrderIdInAndOrgId(@Param("orderIdCollection")Collection<Integer> orderIdCollection,@Param("orgId")Integer orgId);

    /**
     * @description: 按照订单id删除评价
     * @date: 2021/2/26 16:18
     * @author: zengyanru
     * @param orderId
     * @return int
     */
    int deleteByOrderId(@Param("orderId")Integer orderId);
}
