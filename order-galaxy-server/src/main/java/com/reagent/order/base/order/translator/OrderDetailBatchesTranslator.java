package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderDetailBatchesDTO;
import com.reagent.order.base.order.model.OrderDetailBatchesDO;

public class OrderDetailBatchesTranslator {

    /**
     * DO to DTO
     * @param item
     * @return
     */
    public static OrderDetailBatchesDTO doToDto(OrderDetailBatchesDO item) {
        OrderDetailBatchesDTO result = new OrderDetailBatchesDTO();
        result.setId(item.getId());
        result.setDetailId(item.getDetailId());
        result.setBatches(item.getBatches());
        result.setExpiration(item.getExpiration());
        result.setExterior(item.getExterior());

        return result;
    }

    /**
     * DTO to DO
     * @param item
     * @return
     */
    public static OrderDetailBatchesDO dtoToDo(OrderDetailBatchesDTO item) {
        OrderDetailBatchesDO result = new OrderDetailBatchesDO();
        result.setId(item.getId());
        result.setDetailId(item.getDetailId());
        result.setBatches(item.getBatches());
        result.setExpiration(item.getExpiration());
        result.setExterior(item.getExterior());

        return result;
    }
}
