<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderMaterialCodeMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderMaterialCode">
    <!--@mbg.generated-->
    <!--@Table order_material_code-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="good_code" jdbcType="VARCHAR" property="goodCode" />
    <result column="spec" jdbcType="VARCHAR" property="spec" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, brand, good_code, spec, material_code, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_material_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from order_material_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderMaterialCode" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_material_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brand != null">
        brand,
      </if>
      <if test="goodCode != null">
        good_code,
      </if>
      <if test="spec != null">
        spec,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="goodCode != null">
        #{goodCode,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderMaterialCode">
    <!--@mbg.generated-->
    update order_material_code
    <set>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="goodCode != null">
        good_code = #{goodCode,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        spec = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryByParam" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from order_material_code
    <where>
      <if test="brand != null and brand != ''">
        and brand = #{brand,jdbcType=VARCHAR}
      </if>
      <if test="goodCode != null and goodCode != ''">
        and good_code = #{goodCode,jdbcType=VARCHAR}
      </if>
      <if test="spec != null and spec != ''">
        and spec = #{spec,jdbcType=VARCHAR}
      </if>
    </where>
    order by update_time desc limit 1
  </select>

  <insert id="insertSelectiveBatch">
    insert into order_material_code
      (brand, good_code, spec, material_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.brand,jdbcType=VARCHAR},
       #{item.goodCode,jdbcType=VARCHAR},
       #{item.spec,jdbcType=VARCHAR},
       #{item.materialCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>