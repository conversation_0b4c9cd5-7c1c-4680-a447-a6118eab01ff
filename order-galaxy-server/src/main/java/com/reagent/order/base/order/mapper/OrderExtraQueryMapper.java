package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.OrderExtraQuery;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @description: 出行记录返回结果
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2019-07-29 20:50
 **/
public interface OrderExtraQueryMapper {

    /**
     * 根据主键删除
     *
     * @param id 主键
     * @return int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增 OrderExtraQuery
     *
     * @param record 入参
     * @return int
     */
    int insert(OrderExtraQuery record);

    /**
     * 选择性新增
     *
     * @param record 入参
     * @return int
     */
    int insertSelective(OrderExtraQuery record);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return OrderBase
     */
    OrderExtraQuery selectByPrimaryKey(Long id);

    /**
     * 根据主键选择性更新
     *
     * @param record 入参
     * @return 结果
     */
    int updateByPrimaryKeySelective(OrderExtraQuery record);

    /**
     * 根据主键更新
     *
     * @param record 入参
     * @return 结果
     */
    int updateByPrimaryKey(OrderExtraQuery record);

    /**
     * 批量插入查询条件
     * @param list 入参集合
     * @return
     */
    int batchInsert(@Param("list") List<OrderExtraQuery> list);
}