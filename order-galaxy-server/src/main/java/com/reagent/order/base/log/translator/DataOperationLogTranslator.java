package com.reagent.order.base.log.translator;

import com.reagent.order.base.log.dto.DataOperationLogDTO;
import com.reagent.order.base.log.model.DataOperationLog;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/7 14:59
 * @description
 */
public class DataOperationLogTranslator {

    /**
     * dto转do
     * @param dataOperationLogDTO
     * @return
     */
    public static DataOperationLog dto2Do(DataOperationLogDTO dataOperationLogDTO){
        DataOperationLog dataOperationLog = new DataOperationLog();
        dataOperationLog.setOrderNo(dataOperationLogDTO.getOrderNo());
        dataOperationLog.setFixReason(dataOperationLogDTO.getFixReason());
        dataOperationLog.setOperation(dataOperationLogDTO.getOperation());
        dataOperationLog.setOperatorGuid(dataOperationLogDTO.getOperatorGuid());
        dataOperationLog.setOperatorName(dataOperationLogDTO.getOperatorName());
        dataOperationLog.setOrgId(dataOperationLogDTO.getOrgId());
        dataOperationLog.setOrgName(dataOperationLogDTO.getOrgName());
        dataOperationLog.setOperationType(dataOperationLogDTO.getOperationType());
        dataOperationLog.setApproveNumber(dataOperationLogDTO.getApproveNumber());
        dataOperationLog.setOperateTime(dataOperationLogDTO.getOperateTime());
        dataOperationLog.setPrevStatus(dataOperationLogDTO.getPrevStatus());
        dataOperationLog.setPreDesc(dataOperationLogDTO.getPreDesc());
        return dataOperationLog;
    }

    /**
     * do转dto
     * @param dataOperationLog
     * @return
     */
    public static DataOperationLogDTO do2Dto(DataOperationLog dataOperationLog){
        DataOperationLogDTO dataOperationLogDTO = new DataOperationLogDTO();
        dataOperationLogDTO.setOrderNo(dataOperationLog.getOrderNo());
        dataOperationLogDTO.setFixReason(dataOperationLog.getFixReason());
        dataOperationLogDTO.setOperation(dataOperationLog.getOperation());
        dataOperationLogDTO.setOperatorGuid(dataOperationLog.getOperatorGuid());
        dataOperationLogDTO.setOperatorName(dataOperationLog.getOperatorName());
        dataOperationLogDTO.setOrgId(dataOperationLog.getOrgId());
        dataOperationLogDTO.setOrgName(dataOperationLog.getOrgName());
        dataOperationLogDTO.setOperationType(dataOperationLog.getOperationType());
        dataOperationLogDTO.setApproveNumber(dataOperationLog.getApproveNumber());
        dataOperationLogDTO.setOperateTime(dataOperationLog.getOperateTime());
        dataOperationLogDTO.setPrevStatus(dataOperationLog.getPrevStatus());
        dataOperationLogDTO.setPreDesc(dataOperationLog.getPreDesc());
        return dataOperationLogDTO;
    }
}
