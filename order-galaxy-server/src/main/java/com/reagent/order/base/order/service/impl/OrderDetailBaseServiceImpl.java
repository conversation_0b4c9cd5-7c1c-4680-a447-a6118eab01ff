package com.reagent.order.base.order.service.impl;

import com.reagent.order.base.order.dto.OrderDetailBatchesDTO;
import com.reagent.order.base.order.mapper.OrderDetailBatchesDOMapper;
import com.reagent.order.base.order.model.OrderDetailBatchesDO;
import com.reagent.order.base.order.service.OrderDetailBaseService;
import com.reagent.order.base.order.translator.OrderDetailBatchesTranslator;
import com.ruijing.fundamental.lang.Preconditions;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class OrderDetailBaseServiceImpl implements OrderDetailBaseService {

    @Resource
    private OrderDetailBatchesDOMapper orderDetailBatchesDOMapper;

    @Override
    public int saveOrderDetailBatches(List<OrderDetailBatchesDTO> list) {
        List<Integer> detailIdList = list.stream().map(OrderDetailBatchesDTO::getDetailId).collect(Collectors.toList());
        Preconditions.notEmpty(detailIdList, "detailIdList can not be empty");
        List<OrderDetailBatchesDO> existDetailList = orderDetailBatchesDOMapper.findByDetailIdIn(detailIdList);
        // 如果不存在批次信息，新增
        if (CollectionUtils.isEmpty(existDetailList)) {
            List<OrderDetailBatchesDO> collect = list.stream().map(OrderDetailBatchesTranslator::dtoToDo).collect(Collectors.toList());
            return orderDetailBatchesDOMapper.insertList(collect);
        } else {
            // 过滤更新的批次信息
            Set<Integer> existDetailIdSet = existDetailList.stream().map(OrderDetailBatchesDO::getDetailId).collect(Collectors.toSet());
            List<OrderDetailBatchesDO> updatedList = list.stream().filter(it -> existDetailIdSet.contains(it.getDetailId())).map(OrderDetailBatchesTranslator::dtoToDo).collect(Collectors.toList());
            int affect = 0;
            if (CollectionUtils.isNotEmpty(updatedList)) {
                for (OrderDetailBatchesDO orderDetailBatchesDO : updatedList) {
                    affect += orderDetailBatchesDOMapper.updateByDetailId(orderDetailBatchesDO);
                }
            }
            // 过滤出新增的批次信息
            List<OrderDetailBatchesDO> insertDetailList = list.stream().filter(d -> !existDetailIdSet.contains(d.getDetailId())).map(OrderDetailBatchesTranslator::dtoToDo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(insertDetailList)) {
                affect += orderDetailBatchesDOMapper.insertList(insertDetailList);
            }
            return affect;
        }

    }

    @Override
    public List<OrderDetailBatchesDTO> findByDetailIdIn(Collection<Integer> detailIdCollection) {
        Preconditions.notEmpty(detailIdCollection, "detailIdList can not be empty");
        List<OrderDetailBatchesDO> detailList = orderDetailBatchesDOMapper.findByDetailIdIn(detailIdCollection);
        return detailList.stream().map(OrderDetailBatchesTranslator::doToDto).collect(Collectors.toList());
    }
}
