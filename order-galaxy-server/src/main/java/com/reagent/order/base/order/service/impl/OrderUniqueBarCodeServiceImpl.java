package com.reagent.order.base.order.service.impl;

import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.request.OrderQRCodePageRequestDTO;
import com.reagent.order.base.order.mapper.OrderUniqueBarCodeDOMapper;
import com.reagent.order.base.order.model.OrderUniqueBarCodeDO;
import com.reagent.order.base.order.service.OrderUniqueBarCodeService;
import com.reagent.order.base.order.translator.OrderUniqueBarCodeTranslator;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.utils.PageResponseUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OrderUniqueBarCodeServiceImpl implements OrderUniqueBarCodeService {

    @Resource
    private OrderUniqueBarCodeDOMapper orderUniqueBarCodeDOMapper;

    @Override
    public List<OrderUniqueBarCodeDTO> findByDetailId(Integer detailId, List<Integer> typeList) {
        if (detailId == null) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> queryResult = orderUniqueBarCodeDOMapper.findByOrderDetailId(detailId, typeList);
        return queryResult.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }

    @Override
    public OrderUniqueBarCodeDTO findFirstByDetailId(Integer detailId, List<Integer> typeList) {
        List<OrderUniqueBarCodeDTO> first = findByDetailId(detailId, typeList);
        if (CollectionUtils.isEmpty(first)) {
            return null;
        }
        return first.get(0);
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByDetailId(List<Integer> detailIdList, List<Integer> typeList) {
        return this.findByDetailId(detailIdList, typeList, null);
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByDetailId(List<Integer> detailIdList, List<Integer> typeList, Integer status) {
        if (CollectionUtils.isEmpty(detailIdList)) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> queryResult = orderUniqueBarCodeDOMapper.findByOrderDetailIdInAndStatus(detailIdList, status, typeList);
        return queryResult.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }


    @Override
    public OrderUniqueBarCodeDTO findByBarCode(String barCode) {
        OrderUniqueBarCodeDO orderUniqueBarCodeDO = orderUniqueBarCodeDOMapper.selectByPrimaryKey(barCode);
        if (orderUniqueBarCodeDO != null) {
            return OrderUniqueBarCodeTranslator.doToDTO(orderUniqueBarCodeDO);
        }
        return null;
    }


    @Override
    public int batchUpdateOrderUniqueBarCode(List<OrderUniqueBarCodeDTO> updatedList) {
        if (CollectionUtils.isEmpty(updatedList)) {
            return 0;
        }
        List<OrderUniqueBarCodeDO> collect = updatedList.stream().map(OrderUniqueBarCodeTranslator::dtoToDo).collect(Collectors.toList());
        return orderUniqueBarCodeDOMapper.batchUpdateByBarCode(collect);
    }

    @Override
    public int batchInsert(List<OrderUniqueBarCodeDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        List<OrderUniqueBarCodeDO> collect = list.stream().map(OrderUniqueBarCodeTranslator::dtoToDo).collect(Collectors.toList());
        return orderUniqueBarCodeDOMapper.batchInsert(collect);
    }

    @Override
    public int findExistedByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return 0;
        }
        return orderUniqueBarCodeDOMapper.countByOrderNo(orderNo);
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByOrderNo(String orderNo, List<Integer> typeList) {
        if (StringUtils.isBlank(orderNo)) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> query = orderUniqueBarCodeDOMapper.findByOrderNo(orderNo, typeList);
        return query.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByOrderNoList(List<String> orderNoList, List<Integer> typeList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> query = orderUniqueBarCodeDOMapper.findByOrderNoIn(orderNoList, typeList);
        return query.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByBarCodeList(List<String> barCodeList) {
        if (CollectionUtils.isEmpty(barCodeList)) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> query = orderUniqueBarCodeDOMapper.findByBarCodeIn(barCodeList);
        return query.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByEntryNo(String entryNo, List<Integer> typeList) {
        if (entryNo == null) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> query = orderUniqueBarCodeDOMapper.findByEntryNo(entryNo, typeList);
        return query.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByApplyNo(String applyNo, List<Integer> typeList) {
        if (applyNo == null) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> query = orderUniqueBarCodeDOMapper.findByApplyNo(applyNo, typeList);
        return query.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByExitNo(String exitNo, List<Integer> typeList) {
        if (exitNo == null) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> query = orderUniqueBarCodeDOMapper.findByExitNo(exitNo, typeList);
        return query.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderUniqueBarCodeDTO> findByReturnNo(String returnNo, List<Integer> typeList) {
        if (returnNo == null) {
            return Collections.emptyList();
        }
        List<OrderUniqueBarCodeDO> query = orderUniqueBarCodeDOMapper.findByReturnNo(returnNo, typeList);
        return query.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }

    @Override
    public int compareAndSetByEntryNoAndStatus(String entryNo, String exitNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList) {
        OrderUniqueBarCodeDO updatedDO = new OrderUniqueBarCodeDO();
        updatedDO.setStatus(updatedStatus);
        updatedDO.setExitNo(exitNo);
        updatedDO.setValid(valid);
        return orderUniqueBarCodeDOMapper.updateByEntryNoAndStatus(updatedDO, entryNo, expectStatus, typeList);
    }

    @Override
    public int compareAndSetByApplyNoAndStatus(String applyNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList) {
        OrderUniqueBarCodeDO updatedDO = new OrderUniqueBarCodeDO();
        updatedDO.setStatus(updatedStatus);
        updatedDO.setValid(valid);
        return orderUniqueBarCodeDOMapper.updateByApplyNoAndStatus(updatedDO, applyNo, expectStatus, typeList);
    }

    @Override
    public int compareAndSetByExitNoAndStatus(String exitNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList) {
        OrderUniqueBarCodeDO updatedDO = new OrderUniqueBarCodeDO();
        updatedDO.setStatus(updatedStatus);
        updatedDO.setValid(valid);
        return orderUniqueBarCodeDOMapper.updateByExitNoAndStatus(updatedDO, exitNo, expectStatus, typeList);
    }

    @Override
    public int compareAndSetByReturnNoAndStatus(String returnNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList) {
        OrderUniqueBarCodeDO updatedDO = new OrderUniqueBarCodeDO();
        updatedDO.setStatus(updatedStatus);
        updatedDO.setValid(valid);
        return orderUniqueBarCodeDOMapper.updateByReturnNoAndStatus(updatedDO, returnNo, expectStatus, typeList);
    }

    @Override
    public int compareAndSetByOrderNoAndStatus(String orderNo, Integer updatedStatus, Integer expectStatus, Integer valid, List<Integer> typeList) {
        OrderUniqueBarCodeDO updatedDO = new OrderUniqueBarCodeDO();
        updatedDO.setStatus(updatedStatus);
        updatedDO.setValid(valid);
        return orderUniqueBarCodeDOMapper.updateByOrderNoAndStatus(updatedDO, orderNo, expectStatus, typeList);
    }

    @Override
    public int updateStatusByBusinessNo(OrderUniqueBarCodeDO updateParam, List<Integer> typeList) {
        return orderUniqueBarCodeDOMapper.updateStatusByBusinessNo(updateParam, typeList);
    }

    @Override
    public int deleteByOrderNo(String orderNo, List<Integer> typeList) {
        if (StringUtils.isBlank(orderNo)) {
            return 0;
        }
        return orderUniqueBarCodeDOMapper.deleteByOrderNo(orderNo, typeList);
    }

    @Override
    public PageableResponse<List<OrderUniqueBarCodeDTO>> queryPage(OrderQRCodePageRequestDTO request) {
        Preconditions.notNull(request.getPageNo(), "pageNo must not be null");
        Preconditions.notNull(request.getPageSize(), "pageSize must not be null");
        Preconditions.isTrue(request.getPageSize() <= 100, "number of pageSize must not be less than 100");

        return PageResponseUtils.pageInvoke(
                () -> orderUniqueBarCodeDOMapper.queryPageForInventory(request),
                OrderUniqueBarCodeTranslator::doToDTO,
                request.getPageNo(),
                request.getPageSize()
        );

    }

    @Override
    public List<OrderUniqueBarCodeDTO> pageByBarcode(String orderNo, String startBarcode, Integer limit) {
        List<OrderUniqueBarCodeDO> orderUniqueBarCodeDOList = orderUniqueBarCodeDOMapper.rangeQuery(orderNo, startBarcode, limit);
        if(CollectionUtils.isEmpty(orderUniqueBarCodeDOList)){
            return New.emptyList();
        }
        return orderUniqueBarCodeDOList.stream().map(OrderUniqueBarCodeTranslator::doToDTO).collect(Collectors.toList());
    }
}
