<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderExtraQueryMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderExtraQuery">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="key" jdbcType="VARCHAR" property="key" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_type, order_id, `key`, `value`, `type`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_extra_query
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from order_extra_query
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderExtraQuery" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_extra_query (business_type, order_id, `key`, 
      `value`, `type`, create_time, 
      update_time)
    values (#{businessType,jdbcType=INTEGER}, #{orderId,jdbcType=BIGINT}, #{key,jdbcType=VARCHAR}, 
      #{value,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.reagent.order.base.order.model.OrderExtraQuery" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_extra_query
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessType != null">
        business_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="key != null">
        `key`,
      </if>
      <if test="value != null">
        `value`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="key != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.reagent.order.base.order.model.OrderExtraQuery">
    <!--@mbg.generated-->
    update order_extra_query
    <set>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="key != null">
        `key` = #{key,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.reagent.order.base.order.model.OrderExtraQuery">
    <!--@mbg.generated-->
    update order_extra_query
    set business_type = #{businessType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=BIGINT},
      `key` = #{key,jdbcType=VARCHAR},
      `value` = #{value,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into order_extra_query
    (business_type, order_id, `key`, `value`, `type`)
    values
    <foreach collection="list" item="item" separator=",">
      (

      #{item.businessType,jdbcType=INTEGER}, #{item.orderId,jdbcType=BIGINT}, #{item.key,jdbcType=VARCHAR},
        #{item.value,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
</mapper>