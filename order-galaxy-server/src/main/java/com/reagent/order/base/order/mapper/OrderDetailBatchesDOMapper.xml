<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderDetailBatchesDOMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderDetailBatchesDO">
    <!--@mbg.generated-->
    <!--@Table order_detail_batches-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="detail_id" jdbcType="INTEGER" property="detailId" />
    <result column="batches" jdbcType="VARCHAR" property="batches" />
    <result column="expiration" jdbcType="VARCHAR" property="expiration" />
    <result column="exterior" jdbcType="INTEGER" property="exterior" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `detail_id`, `batches`, `expiration`, `exterior`, `create_time`, `update_time`
  </sql>

<!--auto generated by MybatisCodeHelper on 2021-06-07-->
  <insert id="insertList">
    INSERT INTO order_detail_batches(
    id,
    `detail_id`,
    `batches`,
    `expiration`,
    `exterior`
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.id,jdbcType=INTEGER},
      #{element.detailId,jdbcType=INTEGER},
      <choose>
        <when test="element.batches != null ">
          #{element.batches,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.expiration != null ">
          #{element.expiration,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.exterior != null ">
          #{element.exterior,jdbcType=INTEGER}
        </when>
        <otherwise>
--           当没有录入外观信息时，默认-1
          -1
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2021-06-07-->
  <update id="updateByDetailId">
    update order_detail_batches
      <set>
      <if test="orderDetailBatchesDO.batches != null">
        `batches` = #{orderDetailBatchesDO.batches,jdbcType=VARCHAR},
      </if>
      <if test="orderDetailBatchesDO.expiration != null">
        `expiration` = #{orderDetailBatchesDO.expiration,jdbcType=VARCHAR},
      </if>
      <if test="orderDetailBatchesDO.exterior != null">
        `exterior` = #{orderDetailBatchesDO.exterior,jdbcType=INTEGER},
      </if>
      </set>
      where detail_id = #{orderDetailBatchesDO.detailId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-06-07-->
  <select id="findByDetailIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_detail_batches
    where detail_id in
    <foreach item="item" index="index" collection="detailIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

</mapper>