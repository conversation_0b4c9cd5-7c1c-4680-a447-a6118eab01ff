package com.reagent.order.base.order.model;

import com.reagent.order.base.order.dto.RefOrderFundCardDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单关联经费卡表
 * @TableName ref_order_fund_card
 */
public class RefOrderFundCard implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 经费卡id
     */
    private String fundCardId;

    /**
     * 经费卡号
     */
    private String fundCardNo;

    /**
     * 冻结金额
     */
    private BigDecimal freezeAmount;

    /**
     * 使用金额
     */
    private BigDecimal useAmount;

    /**
     * 经费支出申请单号
     */
    private String expenseApplyNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public static RefOrderFundCard fromDTO(RefOrderFundCardDTO source){
        RefOrderFundCard target = new RefOrderFundCard();
        target.setId(source.getId());
        target.setOrderId(source.getOrderId());
        target.setFreezeAmount(source.getFreezeAmount());
        target.setExpenseApplyNo(source.getExpenseApplyNo());
        target.setFundCardId(source.getFundCardId());
        target.setFundCardNo(source.getFundCardNo());
        target.setUseAmount(source.getUseAmount());
        return target;
    }

    public static RefOrderFundCardDTO toDTO(RefOrderFundCard source){
        RefOrderFundCardDTO target = new RefOrderFundCardDTO();
        target.setId(source.getId());
        target.setOrderId(source.getOrderId());
        target.setFreezeAmount(source.getFreezeAmount());
        target.setExpenseApplyNo(source.getExpenseApplyNo());
        target.setFundCardId(source.getFundCardId());
        target.setFundCardNo(source.getFundCardNo());
        target.setUseAmount(source.getUseAmount());
        return target;
    }

    /**
     * id
     */
    public Integer getId() {
        return id;
    }

    /**
     * id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 订单id
     */
    public Integer getOrderId() {
        return orderId;
    }

    /**
     * 订单id
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * 经费卡id
     */
    public String getFundCardId() {
        return fundCardId;
    }

    /**
     * 经费卡id
     */
    public void setFundCardId(String fundCardId) {
        this.fundCardId = fundCardId;
    }

    /**
     * 经费卡号
     */
    public String getFundCardNo() {
        return fundCardNo;
    }

    /**
     * 经费卡号
     */
    public void setFundCardNo(String fundCardNo) {
        this.fundCardNo = fundCardNo;
    }

    /**
     * 冻结金额
     */
    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    /**
     * 冻结金额
     */
    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    /**
     * 使用金额
     */
    public BigDecimal getUseAmount() {
        return useAmount;
    }

    /**
     * 使用金额
     */
    public void setUseAmount(BigDecimal useAmount) {
        this.useAmount = useAmount;
    }

    /**
     * 经费支出申请单号
     */
    public String getExpenseApplyNo() {
        return expenseApplyNo;
    }

    /**
     * 经费支出申请单号
     */
    public void setExpenseApplyNo(String expenseApplyNo) {
        this.expenseApplyNo = expenseApplyNo;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        RefOrderFundCard other = (RefOrderFundCard) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getFundCardId() == null ? other.getFundCardId() == null : this.getFundCardId().equals(other.getFundCardId()))
            && (this.getFundCardNo() == null ? other.getFundCardNo() == null : this.getFundCardNo().equals(other.getFundCardNo()))
            && (this.getFreezeAmount() == null ? other.getFreezeAmount() == null : this.getFreezeAmount().equals(other.getFreezeAmount()))
            && (this.getUseAmount() == null ? other.getUseAmount() == null : this.getUseAmount().equals(other.getUseAmount()))
            && (this.getExpenseApplyNo() == null ? other.getExpenseApplyNo() == null : this.getExpenseApplyNo().equals(other.getExpenseApplyNo()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getFundCardId() == null) ? 0 : getFundCardId().hashCode());
        result = prime * result + ((getFundCardNo() == null) ? 0 : getFundCardNo().hashCode());
        result = prime * result + ((getFreezeAmount() == null) ? 0 : getFreezeAmount().hashCode());
        result = prime * result + ((getUseAmount() == null) ? 0 : getUseAmount().hashCode());
        result = prime * result + ((getExpenseApplyNo() == null) ? 0 : getExpenseApplyNo().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", fundCardId=").append(fundCardId);
        sb.append(", fundCardNo=").append(fundCardNo);
        sb.append(", freezeAmount=").append(freezeAmount);
        sb.append(", useAmount=").append(useAmount);
        sb.append(", expenseApplyNo=").append(expenseApplyNo);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}