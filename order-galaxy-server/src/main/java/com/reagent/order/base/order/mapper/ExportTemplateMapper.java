package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.ExportTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;import java.util.List;

@Mapper
public interface ExportTemplateMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ExportTemplateDO record);

    int insertSelective(ExportTemplateDO record);

    ExportTemplateDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ExportTemplateDO record);

    int updateByPrimaryKey(ExportTemplateDO record);

    /**
     * 通过用户类型（store还是oms）和用户id找模板记录
     *
     * @param userType
     * @param userId
     * @return
     */
    List<ExportTemplateDO> selectByUserTypeAndUserId(@Param("userType") Integer userType, @Param("userId") Long userId);

    /**
     * 通过用户类型（store还是oms）和用户id找模板记录条数
     *
     * @param userType
     * @param userId
     * @return
     */
    Integer countByUserTypeAndUserId(@Param("userType") Integer userType, @Param("userId") Long userId);

    /**
     * 通过orgId和共享状态，查询某个共享状态在全单位的条目数
     * @param orgId
     * @param shareStatus
     * @return
     */
    Integer countByOrgIdAndShareStatus(@Param("orgId")Integer orgId,@Param("shareStatus")Integer shareStatus);

    /**
     * 通过orgId和共享状态，查询某个共享状态在全单位的条目列表
     * @param orgId
     * @param shareStatus
     * @return
     */
    List<ExportTemplateDO> selectByOrgIdAndShareStatus(@Param("orgId")Integer orgId,@Param("shareStatus")Integer shareStatus);
}