package com.reagent.order.base.order.mapper;
import java.util.Collection;

import com.reagent.order.base.order.dto.request.OrderQRCodePageRequestDTO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.reagent.order.base.order.model.OrderUniqueBarCodeDO;

public interface OrderUniqueBarCodeDOMapper {

    /**
     * 根据barCode查询二维码信息
     * @param barCode
     * @return
     */
    OrderUniqueBarCodeDO selectByPrimaryKey(String barCode);

    /**
     * 根据detailId查询二维码信息
     * @param orderDetailId
     * @return
     */
    List<OrderUniqueBarCodeDO> findByOrderDetailId(@Param("orderDetailId")Integer orderDetailId, @Param("typeList") List<Integer> typeList);

    /**
     * 根据detailId查询二维码信息, 返回单个
     * @param orderDetailId
     * @return
     */
    List<OrderUniqueBarCodeDO> findByOrderDetailIdFirst(@Param("orderDetailId")Integer orderDetailId);

    /**
     * 根据detailId和status查询二维码批次
     * @param orderDetailIdCollection
     * @param status
     * @return
     */
    List<OrderUniqueBarCodeDO> findByOrderDetailIdInAndStatus(@Param("orderDetailIdCollection")Collection<Integer> orderDetailIdCollection,
                                                              @Param("status")Integer status,
                                                              @Param("typeList") List<Integer> typeList);

    /**
     * 批量更新批次信息
     * @param updatedList
     * @return
     */
	int batchUpdateByBarCode(@Param("updatedList")List<OrderUniqueBarCodeDO> updatedList);

    /**
     * 批量插入二维码，单号和商品明细信息必填
     * @param list
     * @return
     */
	int batchInsert(@Param("list")List<OrderUniqueBarCodeDO> list);

    /**
     * 查询某个订单是否存在记录,只查单位生成的
     * @param orderNo
     * @return
     */
	Integer countByOrderNo(@Param("orderNo")String orderNo);

    /**
     * 根据订单号查询二维码信息
     * @param orderNo
     * @return
     */
	List<OrderUniqueBarCodeDO> findByOrderNo(@Param("orderNo")String orderNo, @Param("typeList") List<Integer> typeList);

    /**
     * 根据订单号查询二维码信息
     * @param orderNoCollection
     * @return
     */
	List<OrderUniqueBarCodeDO> findByOrderNoIn(@Param("orderNoCollection")Collection<String> orderNoCollection, @Param("typeList") List<Integer> typeList);

    /**
     * 根据条形码查询码的信息
     * @param barCodeCollection
     * @return
     */
	List<OrderUniqueBarCodeDO> findByBarCodeIn(@Param("barCodeCollection")Collection<String> barCodeCollection);

    /**
     * 使用入库单号查询
     * @param entryNo
     * @return
     */
	List<OrderUniqueBarCodeDO> findByEntryNo(@Param("entryNo")String entryNo, @Param("typeList") List<Integer> typeList);

    /**
     * 使用出库单号查询
     * @param exitNo
     * @return
     */
	List<OrderUniqueBarCodeDO> findByExitNo(@Param("exitNo")String exitNo, @Param("typeList") List<Integer> typeList);

    /**
     * 使用申领单号查询
     * @param applyNo
     * @return
     */
	List<OrderUniqueBarCodeDO> findByApplyNo(@Param("applyNo")String applyNo, @Param("typeList") List<Integer> typeList);

    /**
     * 使用退货单号查询
     * @param returnNo
     * @return
     */
	List<OrderUniqueBarCodeDO> findByReturnNo(@Param("returnNo")String returnNo, @Param("typeList") List<Integer> typeList);

    /**
     * 根据入库单更新码信息
     * @param updated 更新信息
     * @param entryNo 入库单号
     * @param expectStatus  期望被更新的状态
     * @return 更新数量
     */
    int updateByEntryNoAndStatus(@Param("updated") OrderUniqueBarCodeDO updated,
                                 @Param("entryNo") String entryNo,
                                 @Param("expectStatus") Integer expectStatus,
                                 @Param("typeList") List<Integer> typeList);

    /**
     * 根据入库单更新码信息
     * @param updated 更新信息
     * @param exitNo  入库单号
     * @param expectStatus  期望被更新的状态
     * @return 更新数量
     */
    int updateByExitNoAndStatus(@Param("updated") OrderUniqueBarCodeDO updated,
                                @Param("exitNo") String exitNo,
                                @Param("expectStatus") Integer expectStatus,
                                @Param("typeList") List<Integer> typeList);

    /**
     * 根据入库单更新码信息
     * @param updated 更新信息
     * @param applyNo 入库单号
     * @param expectStatus  期望被更新的状态
     * @return 更新数量
     */
    int updateByApplyNoAndStatus(@Param("updated") OrderUniqueBarCodeDO updated,
                                 @Param("applyNo") String applyNo,
                                 @Param("expectStatus") Integer expectStatus,
                                 @Param("typeList") List<Integer> typeList);

    /**
     * 根据入库单更新码信息
     * @param updated  更新信息
     * @param returnNo 入库单号
     * @param expectStatus   期望被更新的状态
     * @return 更新数量
     */
    int updateByReturnNoAndStatus(@Param("updated") OrderUniqueBarCodeDO updated,
                                  @Param("returnNo") String returnNo,
                                  @Param("expectStatus") Integer expectStatus,
                                  @Param("typeList") List<Integer> typeList);

    /**
     * 根据订单号更新码信息
     * @param updated   更新信息
     * @param orderNo   入库单号
     * @param expectStatus  期望被更新的状态
     * @return  更新数量
     */
    int updateByOrderNoAndStatus(@Param("updated") OrderUniqueBarCodeDO updated,
                                 @Param("orderNo") String orderNo,
                                 @Param("expectStatus") Integer expectStatus,
                                 @Param("typeList") List<Integer> typeList);

    /**
     * 根据业务号更新状态字段
     * @param updated 更新信息
     * @param typeList 类型
     * @return 更新数量
     */
    int updateStatusByBusinessNo(@Param("updated") OrderUniqueBarCodeDO updated,
                                 @Param("typeList") List<Integer> typeList);

    /**
     * 删除订单关联的二维码信息
     * @param orderNo   订单号
     * @return          删除数量
     */
    int deleteByOrderNo(@Param("orderNo")String orderNo, @Param("typeList") List<Integer> typeList);

    /**
     * 分页查询一物一码--库房盘点功能，只查单位二维码数据
     * @param params                分页入参
     * @return                      分页结果
     */
    List<OrderUniqueBarCodeDO> queryPageForInventory(@Param("params") OrderQRCodePageRequestDTO params);

    List<OrderUniqueBarCodeDO> rangeQuery(@Param("orderNo") String orderNo, @Param("minBarcode") String minBarcode, @Param("limit") Integer limit);
}