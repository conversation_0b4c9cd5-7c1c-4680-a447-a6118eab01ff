package com.reagent.order.base.log.model;

import java.util.Date;

/**
 * <AUTHOR>
 * @create: 2019/11/15
 */
public class OrderDockingLog {
    /**
    * 主键id
    */
    private Long id;

    /**
    * 对接的单号  主要是订单号
    */
    private String dockingNumber;

    /**
    * 调用接口的信息
    */
    private String paramInfo;

    /**
    * 接口返回信息，回调信息
    */
    private String extraInfo;

    /**
    * 操作
    */
    private String operation;

    /**
    * 组织编码
    */
    private String orgCode;

    /**
    * 结果
    */
    private String result;

    /**
    * 备注
    */
    private String remark;

    /**
    * 创建时间
    */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDockingNumber() {
        return dockingNumber;
    }

    public void setDockingNumber(String dockingNumber) {
        this.dockingNumber = dockingNumber;
    }

    public String getParamInfo() {
        return paramInfo;
    }

    public void setParamInfo(String paramInfo) {
        this.paramInfo = paramInfo;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dockingNumber=").append(dockingNumber);
        sb.append(", paramInfo=").append(paramInfo);
        sb.append(", extraInfo=").append(extraInfo);
        sb.append(", operation=").append(operation);
        sb.append(", orgCode=").append(orgCode);
        sb.append(", result=").append(result);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}