package com.reagent.order.base.order.mapper;

import com.reagent.order.base.order.model.OrderEventStatusDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 11:59
 * @description
 */
public interface OrderEventStatusMapper {
    /**
     * 根据主键删除
     * @param id 主键
     * @return
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入数据
     * @param record OrderEventStatusDO
     * @return
     */
    int insert(OrderEventStatusDO record);

    /**
     * 插入数据集
     * @param record OrderEventStatusDO
     * @return
     */
    int insertList(@Param("list") List<OrderEventStatusDO> record);

    /**
     * 选择性插入非null数据
     * @param record OrderEventStatusDO
     * @return
     */
    int insertSelective(OrderEventStatusDO record);

    /**
     * 通过主键查询
     * @param id 主键
     * @return
     */
    OrderEventStatusDO selectByPrimaryKey(Integer id);

    /**
     * 根据主键选择性更新非null数据
     * @param record OrderEventStatusDO
     * @return
     */
    int updateByPrimaryKeySelective(OrderEventStatusDO record);

    /**
     * 根据主键更新数据
     * @param record OrderEventStatusDO
     * @return
     */
    int updateByPrimaryKey(OrderEventStatusDO record);

    /**
     * 根据orderNo和EventType查找，EventType可空
     * @param orderEventStatusDO 订单事件状态
     * @return
     */
    List<OrderEventStatusDO> selectByOrderNoAndEventType(OrderEventStatusDO orderEventStatusDO);

    /**
     * 根据orderNo列表和EventType查找，EventType可空
     * @param orderNoList 订单号清单
     * @param eventType 事件类型
     * @return List<OrderEventStatusDO> 订单事件状态清单
     */
    List<OrderEventStatusDO> selectByOrderNoListAndEventType(@Param("orderNoList") List<String> orderNoList,@Param("eventType") Integer eventType);

    /**
     * 根据orderNo和EventType删除，EventType可空
     * @param orderEventStatusDO 订单事件状态
     * @return
     */
    int deleteByOrderNoAndEventType(OrderEventStatusDO orderEventStatusDO);

    /**
     * 根据orderNo和EventType更新
     * @param orderEventStatusDO 订单事件状态
     * @return
     */
    int updateByOrderNoAndEventType(OrderEventStatusDO orderEventStatusDO);
    
}