<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderBankAccountSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderBankAccountSnapshotDO">
    <!--@mbg.generated-->
    <!--@Table order_bank_account_snapshot-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="bank_id" jdbcType="INTEGER" property="bankId"/>
    <result column="bank_account_name" jdbcType="VARCHAR" property="bankAccountName" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_branch" jdbcType="VARCHAR" property="bankBranch" />
    <result column="bank_code" jdbcType="VARCHAR" property="bankCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="bank_card_number" jdbcType="VARCHAR" property="bankCardNumber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, order_no, org_id, bank_id, bank_account_name, bank_name, bank_branch, bank_code,
    province_code, city_code, bank_card_number, create_time, update_time, account_type
  </sql>

  <select id="selectByOrderIdList" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from order_bank_account_snapshot
    where order_id in
    <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
      #{orderId,jdbcType=INTEGER}
    </foreach>
    and deleted = 0
  </select>

  <insert id="insertList" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    <foreach collection="list" item="item" separator=";">
      <!--@mbg.generated-->
      insert into order_bank_account_snapshot
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="item.orderId != null">
          order_id,
        </if>
        <if test="item.orderNo != null">
          order_no,
        </if>
        <if test="item.orgId != null">
          org_id,
        </if>
        <if test="item.bankId != null">
          bank_id,
        </if>
        <if test="item.bankAccountName != null">
          bank_account_name,
        </if>
        <if test="item.bankName != null">
          bank_name,
        </if>
        <if test="item.bankBranch != null">
          bank_branch,
        </if>
        <if test="item.bankCode != null">
          bank_code,
        </if>
        <if test="item.provinceCode != null">
          province_code,
        </if>
        <if test="item.cityCode != null">
          city_code,
        </if>
        <if test="item.bankCardNumber != null">
          bank_card_number,
        </if>
        <if test="item.accountType != null">
          account_type,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="item.orderId != null">
          #{item.orderId,jdbcType=INTEGER},
        </if>
        <if test="item.orderNo != null">
          #{item.orderNo,jdbcType=VARCHAR},
        </if>
        <if test="item.orgId != null">
          #{item.orgId,jdbcType=INTEGER},
        </if>
        <if test="item.bankId != null">
          #{item.bankId,jdbcType=INTEGER},
        </if>
        <if test="item.bankAccountName != null">
          #{item.bankAccountName,jdbcType=VARCHAR},
        </if>
        <if test="item.bankName != null">
          #{item.bankName,jdbcType=VARCHAR},
        </if>
        <if test="item.bankBranch != null">
          #{item.bankBranch,jdbcType=VARCHAR},
        </if>
        <if test="item.bankCode != null">
          #{item.bankCode,jdbcType=VARCHAR},
        </if>
        <if test="item.provinceCode != null">
          #{item.provinceCode,jdbcType=VARCHAR},
        </if>
        <if test="item.cityCode != null">
          #{item.cityCode,jdbcType=VARCHAR},
        </if>
        <if test="item.bankCardNumber != null">
          #{item.bankCardNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.accountType != null">
          #{item.accountType,jdbcType=INTEGER},
        </if>
      </trim>
    </foreach>
  </insert>

  <update id="updateList">
    <foreach collection="list" separator=";" item="item">
      update order_bank_account_snapshot
      <!--@mbg.generated-->
      <set>
        <if test="item.bankId != null">
          bank_id = #{item.bankId,jdbcType=INTEGER},
        </if>
        <if test="item.bankAccountName != null">
          bank_account_name = #{item.bankAccountName,jdbcType=VARCHAR},
        </if>
        <if test="item.bankName != null">
          bank_name = #{item.bankName,jdbcType=VARCHAR},
        </if>
        <if test="item.bankBranch != null">
          bank_branch = #{item.bankBranch,jdbcType=VARCHAR},
        </if>
        <if test="item.bankCode != null">
          bank_code = #{item.bankCode,jdbcType=VARCHAR},
        </if>
        <if test="item.provinceCode != null">
          province_code = #{item.provinceCode,jdbcType=VARCHAR},
        </if>
        <if test="item.cityCode != null">
          city_code = #{item.cityCode,jdbcType=VARCHAR},
        </if>
        <if test="item.bankCardNumber != null">
          bank_card_number = #{item.bankCardNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.accountType != null">
          account_type = #{item.accountType,jdbcType=INTEGER},
        </if>
      </set>
      where order_id = #{item.orderId,jdbcType=INTEGER}
      and deleted = 0
    </foreach>
  </update>

  <delete id="deleteByOrderIdList">
    update order_bank_account_snapshot set deleted = 1
    where order_id in
    <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
      #{orderId,jdbcType=INTEGER}
    </foreach>
    and deleted = 0
  </delete>
</mapper>