package com.reagent.order.base.order.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.StringJoiner;

public class OrderUniqueBarCodeDO {
    /**
    * 二维码, 长整型
    */
    private String barCode;

    /**
     * 条形码类型 1：单位条形码 2：中爆条形码
     */
    private Integer barCodeType;

    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 订单明细id
    */
    private Integer orderDetailId;

    /**
    * 商品名
    */
    private String productName;

    /**
     * 商品货号
     */
    private String productCode;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
    * 规格
    */
    private String spec;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
    * 批号
    */
    private String batches;

    /**
    * 有效期
    */
    private String expiration;

    /**
     * 生厂厂家
     */
    private String manufacturer;

    /**
    * 耐久度(外观) 0正常1破损
    */
    private Integer exterior;

    /**
     * 绑定的气瓶id，-1即为没有绑定
     */
    private String gasBottleBarcode;

    /**
     * 0：待录入批次
     * 1：待发货
     * 2：待收货
     * 3：待入库审批
     * 4：已入库
     * --入库驳回
     * 5：待出库审批
     * 6：已出库
     * 7：退货待确认
     * 8：取消退货
     * 9：同意退货
     * 10：退还货物
     * 11：已退货
     * 12: 拒绝退货
     */
    private Integer status;

    /**
     * 批次状态
     */
    private Integer batchesStatus;

    /**
     * 库房状态
     */
    private Integer inventoryStatus;

    /**
     * 交易状态
     */
    private Integer transactionStatus;

    /**
     * 库房id
     */
    private Integer roomId;

    /**
    * 入库单号
    */
    private String entryNo;

    /**
     * 申领单号
     */
    private String applyNo;

    /**
    * 出库单号
    */
    private String exitNo;

    /**
    * 退货单号
    */
    private String returnNo;

    /**
    * 是否已打印
    */
    private Integer printed;

    /**
    * created_time
    */
    private Date createdTime;

    /**
    * updated_time
    */
    private Date updatedTime;

    /**
     * 商品图片
     */
    private String productPicture;

    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 退货说明
     */
    private String returnDescription;

    /**
     * 是否有效, 默认1有效, 0无效
     */
    private Integer valid;

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Integer getBarCodeType() {
        return barCodeType;
    }

    public void setBarCodeType(Integer barCodeType) {
        this.barCodeType = barCodeType;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getBatches() {
        return batches;
    }

    public void setBatches(String batches) {
        this.batches = batches;
    }

    public String getExpiration() {
        return expiration;
    }

    public void setExpiration(String expiration) {
        this.expiration = expiration;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getExterior() {
        return exterior;
    }

    public void setExterior(Integer exterior) {
        this.exterior = exterior;
    }

    public String getGasBottleBarcode() {
        return gasBottleBarcode;
    }

    public OrderUniqueBarCodeDO setGasBottleBarcode(String gasBottleBarcode) {
        this.gasBottleBarcode = gasBottleBarcode;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getBatchesStatus() {
        return batchesStatus;
    }

    public OrderUniqueBarCodeDO setBatchesStatus(Integer batchesStatus) {
        this.batchesStatus = batchesStatus;
        return this;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public OrderUniqueBarCodeDO setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
        return this;
    }

    public Integer getTransactionStatus() {
        return transactionStatus;
    }

    public OrderUniqueBarCodeDO setTransactionStatus(Integer transactionStatus) {
        this.transactionStatus = transactionStatus;
        return this;
    }

    public String getEntryNo() {
        return entryNo;
    }

    public void setEntryNo(String entryNo) {
        this.entryNo = entryNo;
    }

    public String getExitNo() {
        return exitNo;
    }

    public void setExitNo(String exitNo) {
        this.exitNo = exitNo;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Integer getPrinted() {
        return printed;
    }

    public void setPrinted(Integer printed) {
        this.printed = printed;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getBrand() {
        return brand;
    }

    public OrderUniqueBarCodeDO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getProductCode() {
        return productCode;
    }

    public OrderUniqueBarCodeDO setProductCode(String productCode) {
        this.productCode = productCode;
        return this;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public OrderUniqueBarCodeDO setSupplierName(String supplierName) {
        this.supplierName = supplierName;
        return this;
    }

    public String getProductPicture() {
        return productPicture;
    }

    public OrderUniqueBarCodeDO setProductPicture(String productPicture) {
        this.productPicture = productPicture;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public OrderUniqueBarCodeDO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getReturnDescription() {
        return returnDescription;
    }

    public OrderUniqueBarCodeDO setReturnDescription(String returnDescription) {
        this.returnDescription = returnDescription;
        return this;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public OrderUniqueBarCodeDO setApplyNo(String applyNo) {
        this.applyNo = applyNo;
        return this;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public OrderUniqueBarCodeDO setRoomId(Integer roomId) {
        this.roomId = roomId;
        return this;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public OrderUniqueBarCodeDO setPrice(BigDecimal price) {
        this.price = price;
        return this;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public OrderUniqueBarCodeDO setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderUniqueBarCodeDO.class.getSimpleName() + "[", "]")
                .add("barCode='" + barCode + "'")
                .add("barCodeType=" + barCodeType)
                .add("orderNo='" + orderNo + "'")
                .add("orderDetailId=" + orderDetailId)
                .add("productName='" + productName + "'")
                .add("productCode='" + productCode + "'")
                .add("productionDate=" + productionDate)
                .add("spec='" + spec + "'")
                .add("brand='" + brand + "'")
                .add("supplierId=" + supplierId)
                .add("supplierName='" + supplierName + "'")
                .add("price=" + price)
                .add("batches='" + batches + "'")
                .add("expiration='" + expiration + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("exterior=" + exterior)
                .add("gasBottleBarcode='" + gasBottleBarcode + "'")
                .add("status=" + status)
                .add("batchesStatus=" + batchesStatus)
                .add("inventoryStatus=" + inventoryStatus)
                .add("transactionStatus=" + transactionStatus)
                .add("roomId=" + roomId)
                .add("entryNo='" + entryNo + "'")
                .add("applyNo='" + applyNo + "'")
                .add("exitNo='" + exitNo + "'")
                .add("returnNo='" + returnNo + "'")
                .add("printed=" + printed)
                .add("createdTime=" + createdTime)
                .add("updatedTime=" + updatedTime)
                .add("productPicture='" + productPicture + "'")
                .add("returnReason='" + returnReason + "'")
                .add("returnDescription='" + returnDescription + "'")
                .add("valid=" + valid)
                .toString();
    }
}