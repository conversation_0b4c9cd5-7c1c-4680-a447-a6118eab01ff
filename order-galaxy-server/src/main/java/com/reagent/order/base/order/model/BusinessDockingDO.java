package com.reagent.order.base.order.model;

import java.util.Date;

public class BusinessDockingDO {
    private Integer id;

    /**
    * 锐竞平台的单号
    */
    private String businessOrderNo;

    /**
    * 第三方平台的单号
    */
    private String dockingNo;

    /**
    * 锐竞平台的订单状态
    */
    private Integer reagentStatus;

    /**
    * 对接平台用的扩展字段
    */
    private String extraJson;

    /**
    * 第三方平台的订单状态
    */
    private Integer extraStatus;

    /**
    * 机构id
    */
    private Integer orgId;

    /**
    * 机构名称
    */
    private String orgCode;

    /**
    * 备注
    */
    private String remark;

    private Date createTime;

    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBusinessOrderNo() {
        return businessOrderNo;
    }

    public void setBusinessOrderNo(String businessOrderNo) {
        this.businessOrderNo = businessOrderNo;
    }

    public String getDockingNo() {
        return dockingNo;
    }

    public void setDockingNo(String dockingNo) {
        this.dockingNo = dockingNo;
    }

    public Integer getReagentStatus() {
        return reagentStatus;
    }

    public void setReagentStatus(Integer reagentStatus) {
        this.reagentStatus = reagentStatus;
    }

    public String getExtraJson() {
        return extraJson;
    }

    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson;
    }

    public Integer getExtraStatus() {
        return extraStatus;
    }

    public void setExtraStatus(Integer extraStatus) {
        this.extraStatus = extraStatus;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessOrderNo=").append(businessOrderNo);
        sb.append(", dockingNo=").append(dockingNo);
        sb.append(", reagentStatus=").append(reagentStatus);
        sb.append(", extraJson=").append(extraJson);
        sb.append(", extraStatus=").append(extraStatus);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgCode=").append(orgCode);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}