<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reagent.order.base.order.mapper.OrderAddressDOMapper">
  <resultMap id="BaseResultMap" type="com.reagent.order.base.order.model.OrderAddressDO">
    <!--@mbg.generated-->
    <!--@Table order_address-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="receiver_name" jdbcType="VARCHAR" property="receiverName" />
    <result column="receiver_phone" jdbcType="VARCHAR" property="receiverPhone" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="geo" jdbcType="VARCHAR" property="geo" />
    <result column="delivery_type" jdbcType="INTEGER" property="deliveryType" />
    <result column="receiver_name_proxy" jdbcType="VARCHAR" property="receiverNameProxy" />
    <result column="receiver_phone_proxy" jdbcType="VARCHAR" property="receiverPhoneProxy" />
    <result column="province_proxy" jdbcType="VARCHAR" property="provinceProxy" />
    <result column="city_proxy" jdbcType="VARCHAR" property="cityProxy" />
    <result column="region_proxy" jdbcType="VARCHAR" property="regionProxy" />
    <result column="address_proxy" jdbcType="VARCHAR" property="addressProxy" />
    <result column="geo_proxy" jdbcType="VARCHAR" property="geoProxy" />
    <result column="sorted_user" jdbcType="VARCHAR" property="sortedUser" />
    <result column="delivery_user" jdbcType="VARCHAR" property="deliveryUser" />
    <result column="delivery_status" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="proxy_source_type" jdbcType="INTEGER" property="proxySourceType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_no, receiver_name, receiver_phone, province, city, region, address,label, geo, delivery_type, receiver_name_proxy,
    receiver_phone_proxy, province_proxy, city_proxy, region_proxy, address_proxy, geo_proxy, delivery_user, sorted_user, delivery_status, proxy_source_type, create_time,
    update_time
  </sql>

<!--auto generated by MybatisCodeHelper on 2021-08-13-->
  <insert id="insertList">
    INSERT INTO order_address(
    id,
    order_no,
    receiver_name,
    receiver_phone,
    province,
    city,
    region,
    address,
    label,
    geo,
    delivery_type,
    proxy_source_type,
    receiver_name_proxy,
    receiver_phone_proxy,
    province_proxy,
    city_proxy,
    region_proxy,
    address_proxy,
    geo_proxy
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.id,jdbcType=INTEGER},
      #{element.orderNo,jdbcType=VARCHAR},
      <choose>
        <when test="element.receiverName != null ">
          #{element.receiverName,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.receiverPhone != null ">
          #{element.receiverPhone,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.province != null ">
          #{element.province,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.city != null ">
          #{element.city,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.region != null ">
          #{element.region,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.address != null ">
          #{element.address,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.label != null ">
          #{element.label,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.geo != null ">
          #{element.geo,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.deliveryType != null ">
          #{element.deliveryType,jdbcType=INTEGER},
        </when>
        <otherwise>
          0,
        </otherwise>
      </choose>
      
      <choose>
        <when test="element.proxySourceType != null ">
          #{element.proxySourceType,jdbcType=INTEGER},
        </when>
        <otherwise>
          0,
        </otherwise>
      </choose>

      <choose>
        <when test="element.receiverNameProxy != null ">
          #{element.receiverNameProxy,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.receiverPhoneProxy != null ">
          #{element.receiverPhoneProxy,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.provinceProxy != null ">
          #{element.provinceProxy,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.cityProxy != null ">
          #{element.cityProxy,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.regionProxy != null ">
          #{element.regionProxy,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.addressProxy != null ">
          #{element.addressProxy,jdbcType=VARCHAR},
        </when>
        <otherwise>
          '',
        </otherwise>
      </choose>

      <choose>
        <when test="element.geoProxy != null ">
          #{element.geoProxy,jdbcType=VARCHAR}
        </when>
        <otherwise>
          ''
        </otherwise>
      </choose>      
      )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2021-08-13-->
  <select id="findByIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_address
    where id in
    <foreach item="item" index="index" collection="idCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-08-13-->
  <update id="updateById">
    update order_address
    <set>
      <if test="updated.id != null">
        id = #{updated.id,jdbcType=INTEGER},
      </if>
      <if test="updated.orderNo != null">
        order_no = #{updated.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="updated.receiverName != null">
        receiver_name = #{updated.receiverName,jdbcType=VARCHAR},
      </if>
      <if test="updated.receiverPhone != null">
        receiver_phone = #{updated.receiverPhone,jdbcType=VARCHAR},
      </if>
      <if test="updated.province != null">
        province = #{updated.province,jdbcType=VARCHAR},
      </if>
      <if test="updated.city != null">
        city = #{updated.city,jdbcType=VARCHAR},
      </if>
      <if test="updated.region != null">
        region = #{updated.region,jdbcType=VARCHAR},
      </if>
      <if test="updated.address != null">
        address = #{updated.address,jdbcType=VARCHAR},
      </if>
      <if test="updated.label != null">
          label = #{updated.label,jdbcType=VARCHAR},
      </if>
      <if test="updated.geo != null">
        geo = #{updated.geo,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliveryType != null">
        delivery_type = #{updated.deliveryType,jdbcType=INTEGER},
      </if>
      <if test="updated.receiverNameProxy != null">
        receiver_name_proxy = #{updated.receiverNameProxy,jdbcType=VARCHAR},
      </if>
      <if test="updated.receiverPhoneProxy != null">
        receiver_phone_proxy = #{updated.receiverPhoneProxy,jdbcType=VARCHAR},
      </if>
      <if test="updated.provinceProxy != null">
        province_proxy = #{updated.provinceProxy,jdbcType=VARCHAR},
      </if>
      <if test="updated.cityProxy != null">
        city_proxy = #{updated.cityProxy,jdbcType=VARCHAR},
      </if>
      <if test="updated.regionProxy != null">
        region_proxy = #{updated.regionProxy,jdbcType=VARCHAR},
      </if>
      <if test="updated.addressProxy != null">
        address_proxy = #{updated.addressProxy,jdbcType=VARCHAR},
      </if>
      <if test="updated.geoProxy != null">
          geo_proxy = #{updated.geoProxy,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliveryStatus != null">
          delivery_status = #{updated.deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="updated.sortedUser != null">
          sorted_user = #{updated.sortedUser,jdbcType=VARCHAR},
      </if>
      <if test="updated.deliveryUser != null">
          delivery_user = #{updated.deliveryUser,jdbcType=VARCHAR},
      </if>
      <if test="updated.proxySourceType != null">
          proxy_source_type = #{updated.proxySourceType,jdbcType=INTEGER},
      </if>
    </set>
    where id=#{updated.id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-08-19-->
  <select id="countById" resultType="java.lang.Integer">
    select count(1)
    from order_address
    where id=#{id,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2021-11-14-->
  <select id="countByIdIn" resultType="java.lang.Integer">
    select count(1)
    from order_address
    where id in
    <foreach item="item" index="index" collection="idCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2021-12-01-->
  <select id="findByOrderNoIn" resultType="java.lang.String">
        select
        order_no
        from order_address
        where order_no in
        <foreach item="item" index="index" collection="orderNoCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2021-12-14-->
  <update id="batchUpdateByOrderNo">
      <foreach collection="list" item="updated" index="index"
               separator=";">
          update order_address
          <set>
              <if test="updated.id != null">
                  id = #{updated.id,jdbcType=INTEGER},
              </if>
              <if test="updated.receiverName != null">
                  receiver_name = #{updated.receiverName,jdbcType=VARCHAR},
              </if>
              <if test="updated.receiverPhone != null">
                  receiver_phone = #{updated.receiverPhone,jdbcType=VARCHAR},
              </if>
              <if test="updated.province != null">
                  province = #{updated.province,jdbcType=VARCHAR},
              </if>
              <if test="updated.city != null">
                  city = #{updated.city,jdbcType=VARCHAR},
              </if>
              <if test="updated.region != null">
                  region = #{updated.region,jdbcType=VARCHAR},
              </if>
              <if test="updated.address != null">
                  address = #{updated.address,jdbcType=VARCHAR},
              </if>
              <if test="updated.label != null">
                  label = #{updated.label,jdbcType=VARCHAR},
              </if>
              <if test="updated.geo != null">
                  geo = #{updated.geo,jdbcType=VARCHAR},
              </if>
              <if test="updated.deliveryType != null">
                  delivery_type = #{updated.deliveryType,jdbcType=INTEGER},
              </if>
              <if test="updated.receiverNameProxy != null">
                  receiver_name_proxy = #{updated.receiverNameProxy,jdbcType=VARCHAR},
              </if>
              <if test="updated.receiverPhoneProxy != null">
                  receiver_phone_proxy = #{updated.receiverPhoneProxy,jdbcType=VARCHAR},
              </if>
              <if test="updated.provinceProxy != null">
                  province_proxy = #{updated.provinceProxy,jdbcType=VARCHAR},
              </if>
              <if test="updated.cityProxy != null">
                  city_proxy = #{updated.cityProxy,jdbcType=VARCHAR},
              </if>
              <if test="updated.regionProxy != null">
                  region_proxy = #{updated.regionProxy,jdbcType=VARCHAR},
              </if>
              <if test="updated.addressProxy != null">
                  address_proxy = #{updated.addressProxy,jdbcType=VARCHAR},
              </if>
              <if test="updated.geoProxy != null">
                  geo_proxy = #{updated.geoProxy,jdbcType=VARCHAR},
              </if>
              <if test="updated.deliveryStatus != null">
                  delivery_status = #{updated.deliveryStatus,jdbcType=INTEGER},
              </if>
              <if test="updated.sortedUser != null">
                  sorted_user = #{updated.sortedUser,jdbcType=VARCHAR},
              </if>
              <if test="updated.deliveryUser != null">
                  delivery_user = #{updated.deliveryUser,jdbcType=VARCHAR},
              </if>
              <if test="updated.proxySourceType != null">
                  proxy_source_type = #{updated.proxySourceType,jdbcType=INTEGER},
              </if>
              <if test="updated.createTime != null">
                  create_time = #{updated.createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updated.updateTime != null">
                  update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
              </if>
          </set>
          where order_no=#{updated.orderNo,jdbcType=VARCHAR}
      </foreach>

    </update>

<!--auto generated by MybatisCodeHelper on 2021-12-03-->
  <select id="findByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from order_address
    where order_no=#{orderNo,jdbcType=VARCHAR} limit 1
  </select>

    <select id="findOrderIdInOperatorIdAndDeliveryStatus" resultMap="BaseResultMap">
        select distinct oa.* from delivery_operation_log dol
        left join order_address oa on oa.id = dol .order_id
        where dol.operator_guid = #{operatorGuid,jdbcType=VARCHAR}
        <if test="deliveryStatus != null ">
            and oa.delivery_status = #{deliveryStatus,jdbcType=INTEGER}
        </if>
        order by dol.order_id
    </select>
</mapper>
