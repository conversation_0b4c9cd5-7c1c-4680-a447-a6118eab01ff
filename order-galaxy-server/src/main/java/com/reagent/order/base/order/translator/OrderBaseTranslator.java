package com.reagent.order.base.order.translator;

import com.reagent.order.base.order.dto.OrderBaseDTO;
import com.reagent.order.base.order.dto.OrderExtraQueryDTO;
import com.reagent.order.base.order.model.OrderBase;
import com.reagent.order.base.order.model.OrderExtraQuery;

/**
 * @description: OrderBase对象转换对象
 * @author: zhuk
 * @create: 2019-07-30 13:47
 **/
public class OrderBaseTranslator {

    /**
     * dto 转 OrderExtraQuery
     * @param orderExtraQueryDTO 入参
     * @return OrderExtraQuery
     */
    public static OrderExtraQuery dtoToOrderExtraQuery(OrderExtraQueryDTO orderExtraQueryDTO){

        OrderExtraQuery orderExtraQuery = new OrderExtraQuery();
        orderExtraQuery.setBusinessType(orderExtraQueryDTO.getBusinessType());
        orderExtraQuery.setOrderId(orderExtraQueryDTO.getOrderId());
        orderExtraQuery.setKey(orderExtraQueryDTO.getKey());
        orderExtraQuery.setValue(orderExtraQueryDTO.getValue());
        Integer type = orderExtraQueryDTO.getType();
        orderExtraQuery.setType(type == null ? -1: type);
        return orderExtraQuery;
    }

    /**
     * orderBaseDTO 转 OrderBase
     * @param orderBaseDTO 入参
     * @return OrderBase
     */
    public static OrderBase dtoToOrderBase(OrderBaseDTO orderBaseDTO){

        OrderBase orderBase = new OrderBase();
        orderBase.setId(orderBaseDTO.getId());
        orderBase.setOrderNumber(orderBaseDTO.getOrderNumber());
        orderBase.setParentId(orderBaseDTO.getParentId());
        orderBase.setParentNumber(orderBaseDTO.getParentNumber());
        orderBase.setSourceType(orderBaseDTO.getSourceType());
        orderBase.setSourceId(orderBaseDTO.getSourceId());
        orderBase.setSourceNumber(orderBaseDTO.getSourceNumber());
        orderBase.setBuyerId(orderBaseDTO.getBuyerId());
        orderBase.setBuyerName(orderBaseDTO.getBuyerName());
        orderBase.setBuyerPhone(orderBaseDTO.getBuyerPhone());
        orderBase.setBuyerEmail(orderBaseDTO.getBuyerEmail());
        orderBase.setOrgId(orderBaseDTO.getOrgId());
        orderBase.setOrgCode(orderBaseDTO.getOrgCode());
        orderBase.setOrgName(orderBaseDTO.getOrgName());
        orderBase.setDepartmentId(orderBaseDTO.getDepartmentId());
        orderBase.setDepartmentName(orderBaseDTO.getDepartmentName());
        orderBase.setCollegeId(orderBaseDTO.getCollegeId());
        orderBase.setCollegeName(orderBaseDTO.getCollegeName());
        orderBase.setBusinessType(orderBaseDTO.getBusinessType());
        orderBase.setOrderStatus(orderBaseDTO.getOrderStatus());
        orderBase.setActualAmount(orderBaseDTO.getActualAmount());
        orderBase.setOriginalPrice(orderBaseDTO.getOriginalPrice());
        orderBase.setDiscountsAmount(orderBaseDTO.getDiscountsAmount());
        orderBase.setProcessType(orderBaseDTO.getProcessType());
        orderBase.setDescriptionSupplier(orderBaseDTO.getDescriptionSupplier());
        orderBase.setDescriptionBuyer(orderBaseDTO.getDescriptionBuyer());
        orderBase.setSupplierId(orderBaseDTO.getSupplierId());
        orderBase.setSupplierName(orderBaseDTO.getSupplierName());
        orderBase.setSupplierCode(orderBaseDTO.getSupplierCode());
        orderBase.setSupplierEmail(orderBaseDTO.getSupplierEmail());
        orderBase.setSupplierPhone(orderBaseDTO.getSupplierPhone());
        orderBase.setCarryFee(orderBaseDTO.getCarryFee());
        orderBase.setReceiverName(orderBaseDTO.getReceiverName());
        orderBase.setReceiverPhone(orderBaseDTO.getReceiverPhone());
        orderBase.setReceiverAddress(orderBaseDTO.getReceiverAddress());
        orderBase.setCreateTime(orderBaseDTO.getCreateTime());
        orderBase.setUpdateTime(orderBaseDTO.getUpdateTime());
        return orderBase;

    }

    /**
     * orderBase 转为DTO 对象
     * @return OrderBaseDTO
     */
    public static OrderBaseDTO orderBaseToDTO(OrderBase orderBase){

        OrderBaseDTO orderBaseDTO = new OrderBaseDTO();
        orderBaseDTO.setId(orderBase.getId());
        orderBaseDTO.setOrderNumber(orderBase.getOrderNumber());
        orderBaseDTO.setParentId(orderBase.getParentId());
        orderBaseDTO.setParentNumber(orderBase.getParentNumber());
        orderBaseDTO.setSourceType(orderBase.getSourceType());
        orderBaseDTO.setSourceId(orderBase.getSourceId());
        orderBaseDTO.setSourceNumber(orderBase.getSourceNumber());
        orderBaseDTO.setBuyerId(orderBase.getBuyerId());
        orderBaseDTO.setBuyerName(orderBase.getBuyerName());
        orderBaseDTO.setBuyerPhone(orderBase.getBuyerPhone());
        orderBaseDTO.setBuyerEmail(orderBase.getBuyerEmail());
        orderBaseDTO.setOrgId(orderBase.getOrgId());
        orderBaseDTO.setOrgCode(orderBase.getOrgCode());
        orderBaseDTO.setOrgName(orderBase.getOrgName());
        orderBaseDTO.setCollegeId(orderBase.getCollegeId());
        orderBaseDTO.setCollegeName(orderBase.getCollegeName());
        orderBaseDTO.setDepartmentId(orderBase.getDepartmentId());
        orderBaseDTO.setDepartmentName(orderBase.getDepartmentName());
        orderBaseDTO.setBusinessType(orderBase.getBusinessType());
        orderBaseDTO.setOrderStatus(orderBase.getOrderStatus());
        orderBaseDTO.setActualAmount(orderBase.getActualAmount());
        orderBaseDTO.setOriginalPrice(orderBase.getOriginalPrice());
        orderBaseDTO.setDiscountsAmount(orderBase.getDiscountsAmount());
        orderBaseDTO.setProcessType(orderBase.getProcessType());
        orderBaseDTO.setDescriptionSupplier(orderBase.getDescriptionSupplier());
        orderBaseDTO.setDescriptionBuyer(orderBase.getDescriptionBuyer());
        orderBaseDTO.setSupplierId(orderBase.getSupplierId());
        orderBaseDTO.setSupplierName(orderBase.getSupplierName());
        orderBaseDTO.setSupplierCode(orderBase.getSupplierCode());
        orderBaseDTO.setSupplierEmail(orderBase.getSupplierEmail());
        orderBaseDTO.setSupplierPhone(orderBase.getSupplierPhone());
        orderBaseDTO.setCarryFee(orderBase.getCarryFee());
        orderBaseDTO.setReceiverName(orderBase.getReceiverName());
        orderBaseDTO.setReceiverPhone(orderBase.getReceiverPhone());
        orderBaseDTO.setReceiverAddress(orderBase.getReceiverAddress());
        orderBaseDTO.setCreateTime(orderBase.getCreateTime());
        orderBaseDTO.setUpdateTime(orderBase.getUpdateTime());
        return orderBaseDTO;
    }
}
