package com.reagent.order.base.order.service;

import com.reagent.order.base.order.dto.*;
import com.reagent.order.base.order.model.OrderBase;
import com.reagent.order.base.rentcar.dto.BuyerRentcarOrderBaseParamDTO;
import com.reagent.order.base.rentcar.dto.SuppRentcarOrderBaseParamDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;

import java.util.List;

/**
 * @description: orderBase的基础接口
 * @author: zhuk
 * @create: 2019-07-30 14:43
 **/

public interface OrderBaseService {

    /**
     * 获取 超时 日期订单
     * @param orderBaseParamDTO  入参状态集合
     * @return 订单列表
     */
    OrderBasePageResultDTO getTimeoutNoticeOrder(OrderBaseParamDTO orderBaseParamDTO);

    /**
     * 根据订单号查询订单
     * @param orderNumber 订单号
     * @return 订单基础集合
     */
    OrderBase findOrderBaseByOrderNumber(String orderNumber);

    /**
     * 新增OrderExtraQuery
     * @param orderExtraQueryDTO 入参
     */
    void createOrderBaseAndExtraQuery(OrderBaseAndExtrasDTO orderExtraQueryDTO);

    /**
     * 根据主键 用非空值 更新订单
     *
     * @param orderBaseDTO 入参
     * @return 更新行数
     */
    Integer updateOrderBaseById(OrderBaseDTO orderBaseDTO);

    /**
     * 根据用户id 和 订单状态查询订单条数
     *
     * @param orderBaseParamDTO 入参
     * @return count
     */
    RemoteResponse<Integer> countByBuyerIdAndStatus(OrderBaseParamDTO orderBaseParamDTO);

    /**
     * 更新订单状态
     *
     * @param orderBaseParamDTO 订单参数
     * @return 更新结果
     */
    RemoteResponse updateOrderBaseStatus(OrderBaseParamDTO orderBaseParamDTO);

    /**
     * 根据 BusinessType 生成订单号
     *
     * @return 订单号
     */
    RemoteResponse<Long> getOrderId();

    /**
     * 新增orderBase
     * @param orderBaseDTO 入参
     * @return boolean
     */
    RemoteResponse createOrderBase(OrderBaseDTO orderBaseDTO);

    /**
     * 供应商查询订单列表
     * @param suppRentcarParamDTO 入参
     * @return orderBase 列表
     */
    RemoteResponse<OrderBasePageResultDTO> rentcarOrderBaseListForSupp(SuppRentcarOrderBaseParamDTO suppRentcarParamDTO);

    /**
     * 获取行程记录列表
     * @param buyerParamDTO 入参
     * @return OrderBasePageResultDTO
     */
    OrderBasePageResultDTO getTravelRecordList(BuyerRentcarOrderBaseParamDTO buyerParamDTO);

    /**
     * 采购人查询订单列表
     * @param buyerParamDTO 入参
     * @return orderBase 列表
     */
    RemoteResponse<OrderBasePageResultDTO> rentcarOrderBaseListForBuyer(BuyerRentcarOrderBaseParamDTO buyerParamDTO);

    /**
     * 根据主键 查询 orderBase
     * @param orderBaseParamDTO 入参
     * @return orderBase
     */
    RemoteResponse<OrderBaseDTO> findOrderBaseById(OrderBaseParamDTO orderBaseParamDTO);

    /**
     * 根据主键集合 查询 orderBase
     * @param orderBaseParamDTO 入参
     * @return
     */
    RemoteResponse<OrderBaseResultDTO> findOrderBaseByIdList(OrderBaseParamDTO orderBaseParamDTO);


    /**
     * 根据采购人 和 订单状态集合查询订单
     *
     * @param buyerId 入参
     * @param  orderStatusList 订单状态
     * @param businessType 业务类型
     * @param size 大小限制
     * @return orderBase 订单集合
     */
    List<OrderBase> findOrdersByStatusAndBuyerId(Long buyerId, List<Integer> orderStatusList,Integer businessType,Integer size);

    /**
     * 查询公示栏 订单
     * @param publicityOrderParamDTO 入参
     * @return
     */
    OrderBasePageResultDTO findPublicityOrder(PublicityOrderParamDTO publicityOrderParamDTO);
}
