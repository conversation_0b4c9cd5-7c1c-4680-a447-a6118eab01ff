package com.reagent.order.backdoor.service.impl;

import com.google.common.util.concurrent.RateLimiter;
import com.reagent.order.backdoor.service.BackDoorService;
import com.reagent.order.base.log.mapper.DataOperationLogMapper;
import com.reagent.order.base.log.model.DataOperationLog;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: chenzhanliang
 * @createTime: 2024-11-29 17:57
 * @description:
 **/
@Service
public class BackDoorServiceImpl implements BackDoorService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private DataOperationLogMapper dataOperationLogMapper;

    private RateLimiter rateLimiter = RateLimiter.create(0.5);


    @Override
    public Integer initializePreDesc() {
        int lastId = dataOperationLogMapper.selectMaxId();
        int startId = dataOperationLogMapper.selectMinId();
        int endId = 0;
        int size = 100;
        int pageNo = startId / size + 1;

        for (; endId < lastId; pageNo++) {
            startId = (pageNo - 1) * size;
            endId = pageNo * size;
            rateLimiter.acquire();
            this.updateDataOperationLogBetweenId(startId, endId);
            logger.info("current endId:{}, size:{}", endId, size);
        }
        return null;
    }

    private void updateDataOperationLogBetweenId(Integer startId, Integer endId){
        List<DataOperationLog> dataOperationLogs = dataOperationLogMapper.listDataOperationLogBetweenId(startId, endId);
        dataOperationLogs.stream().
                filter(dataOperationLog -> {
                    boolean validStatus = OrderApprovalEnum.FIX_ORDER_STATUS.getValue().equals(dataOperationLog.getOperationType()) || OrderApprovalEnum.FIX_FUND_STATUS.getValue().equals(dataOperationLog.getOperationType());
                    return validStatus && StringUtils.isBlank(dataOperationLog.getPreDesc()) && dataOperationLog.getPrevStatus() != -99;
                }).
                forEach(dataOperationLog -> {
                    if (OrderApprovalEnum.FIX_ORDER_STATUS.getValue().equals(dataOperationLog.getOperationType())) {
                        OrderStatusEnum orderStatusEnum = OrderStatusEnum.get(dataOperationLog.getPrevStatus());
                        dataOperationLog.setPreDesc(orderStatusEnum.getName());
                    } else if (OrderApprovalEnum.FIX_FUND_STATUS.getValue().equals(dataOperationLog.getOperationType())) {
                        OrderFundStatusEnum orderFundStatusEnum = OrderFundStatusEnum.get(dataOperationLog.getPrevStatus());
                        dataOperationLog.setPreDesc(orderFundStatusEnum.getName());
                    }
                });
        dataOperationLogMapper.batchUpdatePreDesc(dataOperationLogs);
    }
}
