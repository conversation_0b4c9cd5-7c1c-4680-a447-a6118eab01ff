package com.reagent.order.backdoor.controller;

import com.reagent.order.backdoor.service.BackDoorService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.order.utils.BusinessErrUtil;

import javax.annotation.Resource;

/**
 * @author: chenzhanliang
 * @createTime: 2024-11-29 17:55
 * @description:
 **/
@MSharpService(isGateway = "true")
@RpcMapping("/private")
public class backDoorController {


    @Resource
    private BackDoorService backDoorService;

    @RpcMapping("/initDataOperation")
    public RemoteResponse<Boolean> initDataOperation(String slang) {
        BusinessErrUtil.isTrue("<EMAIL>-".equals(slang), "你没权限！");
        backDoorService.initializePreDesc();
        return RemoteResponse.success();
    }
}
