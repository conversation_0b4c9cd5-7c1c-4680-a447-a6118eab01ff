package com.reagent.order.scheduled.impl;

import com.google.common.collect.Lists;
import com.ruijing.order.annotation.ServiceLog;
import com.reagent.order.base.order.mapper.OrderExportMapper;
import com.reagent.order.base.order.model.OrderExportDO;
import com.reagent.order.base.scheduled.OrderScheduledRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.upload.client.FileUploadClient;
import com.ruijing.fundamental.upload.client.FileUploadResp;
import com.ruijing.pearl.annotation.PearlValue;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/11/11 0011 15:22
 * @Version 1.0
 * @Desc:描述
 */
@MSharpService
public class OrderScheduledRpcServiceImpl implements OrderScheduledRpcService {

    @PearlValue(key = "order.export.over.day", defaultValue = "60")
    private Integer overDay;

    @PearlValue(key="file.upload.keyId",defaultValue = "34")
    private Integer accessKeyId;

    @PearlValue(key="file.upload.secretAccessKey",defaultValue = "IeE3Wo*ypkd8#r$ds8Wq")
    private  String secretAccessKey;

    private  Integer accessKeyIdTemp;

    private  String secretAccessKeyTemp;

    /**
     * 引入文件上传
     */
    private FileUploadClient fileUploadClient;

    @Resource
    private OrderExportMapper orderExportMapper;

    @Override
    @ServiceLog(description = "定时清理过期订单导出数据")
    public RemoteResponse<Boolean> clearOverdueExportInfo() {
        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime overDate = localDateTime.minusDays(overDay);
        LocalDate overLocalDate = overDate.toLocalDate();

        List<OrderExportDO> orderExportDOList = orderExportMapper.selectByExportDateBefore(overLocalDate);
        if (CollectionUtils.isNotEmpty(orderExportDOList)) {
            List<Integer> overIdList = orderExportDOList.stream().map(OrderExportDO::getId).collect(Collectors.toList());
            List<String> fileUrlList = orderExportDOList.stream().filter(orderExportDO -> StringUtils.isNotBlank(orderExportDO.getFileUrl())).map(OrderExportDO::getFileUrl).collect(Collectors.toList());
            //删除对应的文件
            if (CollectionUtils.isNotEmpty(fileUrlList)) {
                FileUploadClient fileUploadClient = this.getFileUploadClient();
                List<List<String>> partitionList = Lists.partition(fileUrlList, 200);
                for (List<String> urlList : partitionList) {
                    FileUploadResp fileUploadResp = fileUploadClient.deleteFiles(New.list(urlList));
                    Preconditions.isTrue(fileUploadResp.isSuccess(), fileUploadResp.getMsg());
                }
            }
            orderExportMapper.deleteByIdIn(overIdList);
        }
        return RemoteResponse.success();
    }

    /**
     * 获取文件上传对象
     * @return
     */
    private  FileUploadClient getFileUploadClient() {
        if (!accessKeyId.equals(accessKeyIdTemp) || !secretAccessKey.equals(secretAccessKeyTemp)) {
            fileUploadClient = new FileUploadClient(accessKeyId,secretAccessKey);
        }
        accessKeyIdTemp = accessKeyId;
        secretAccessKeyTemp = secretAccessKey;
        return fileUploadClient;
    }
}
