package com.reagent.order.utils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.cat.common.collections.New;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/07/02 0028 16:14
 * @Version 1.0
 * @Desc:描述
 */
public class PageResponseUtils {

    /**
     * 分页工具
     * @param supplier     需要分页的查询
     * @param pageNumber   pagenumber
     * @param pageSize
     * @return
     */
    public static <T> BasePageResultDTO<T> pageInvoke(Supplier<List<T>> supplier, Integer pageNumber , Integer pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        ArrayList<T> resultList = (ArrayList) supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(resultList);
        //page对象转List
        List<T> pageList = New.list(pageInfo.getList());
        BasePageResultDTO<T> pageResultDTO = new BasePageResultDTO();
        pageResultDTO.setData(pageList);
        pageResultDTO.setPageNo(pageInfo.getPageNum());
        pageResultDTO.setPageSize(pageInfo.getPageSize());
        pageResultDTO.setTotal(pageInfo.getTotal());
        return pageResultDTO;
    }

    /**
     * 分页工具，转DTO
     * @param supplier     需要分页的查询
     * @param pageNumber   pagenumber
     * @param pageSize
     * @return
     */
    public static <T, R> PageableResponse<List<R>> pageInvoke(Supplier<List<T>> supplier,
                                                              Function<? super T, ? extends R> function,
                                                              Integer pageNumber,
                                                              Integer pageSize) {
        PageHelper.startPage(pageNumber, pageSize);
        ArrayList<T> resultList = (ArrayList) supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(resultList);
        List<R> pageList = resultList.stream().map(function).collect(Collectors.toList());
        return PageableResponse
                .<List<R>>custom()
                .setData(pageList)
                .setPageNo(pageInfo.getPageNum())
                .setPageSize(pageInfo.getPageSize())
                .setTotal(pageInfo.getTotal())
                .setCode(200)
                .build();
    }
}