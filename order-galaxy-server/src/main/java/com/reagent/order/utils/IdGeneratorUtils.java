package com.reagent.order.utils;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.sequence.api.service.IdGenerator;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: zhukai
 * @date : 2021/10/12 上午10:39
 * @description: Id生成器  基于卫平的id分发器，结合业务的id工具类
 */
@Component
public class IdGeneratorUtils {

    /**
     * 业务号
     */
    private final static String BIZ_NAME = "order_bar_code";

    /**
     * 自增序号不能超过1000，超过1000 重新取id
     */
    private final static  int MAX_COUNT = 1000;
    

    @MSharpReference(token = "dOCxeib3M")
    private static IdGenerator idGenerator;


    private static Long getOrderId() {
        //生成订单编号
        RemoteResponse<Long> response =  idGenerator.nextId(BIZ_NAME);
        if (response.isSuccess()){
            return response.getData();
        }else {
            Cat.logError("IdGeneratorHandler","getOrderId","获取分布式ID异常",new Throwable(JsonUtils.toJson(response)));
            throw new RuntimeException("获取分布式ID异常");
        }
    }

    /**
     * 批量获取唯一码
     * 通过卫平的ID分发器，每次获取一个递增 id
     * 再乘以上1000，再加上#count从0开始递增的值
     * 如果count > 1000 则再取一个新的ID，再从0开始递增，直至count排完
     *
     * @param count 需要获取唯一码的个数
     * @return barCodeList 唯一码集合
     */
    public static List<Long> getBarCodeList(int count) {
        List<Long> barCodeList = New.listWithCapacity(count);
        while (count > 0){
            Long orderId = getOrderId();
            int k = count > MAX_COUNT ? MAX_COUNT : count;
            for (int l = 0; l < k; l++) {
                barCodeList.add(orderId * MAX_COUNT + l);
            }
            count = count - MAX_COUNT;
        }
        return barCodeList;
    }
}
