package com.reagent.order.rpc.orderextra;

import com.reagent.order.base.order.dto.OrderFileOperationLogDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogBatchQueryRequestDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogRequestDTO;
import com.reagent.order.base.order.mapper.OrderFileOperationLogMapper;
import com.reagent.order.base.order.model.OrderFileOperationLogDO;
import com.reagent.order.base.order.service.OrderFileOperationLogRpcService;
import com.reagent.order.base.order.translator.OrderFileOperationLogTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单文件操作日志RPC服务接口实现
 */
@MSharpService
public class OrderFileOperationLogRpcServiceImpl implements OrderFileOperationLogRpcService {

    @Resource
    private OrderFileOperationLogMapper orderFileOperationLogMapper;

    /**
     * 批量查询订单文件操作日志
     *
     * @param requestDTO 批量查询请求参数，包含orderIds和logIds
     * @return 订单文件操作日志列表
     */
    @Override
    public RemoteResponse<List<OrderFileOperationLogDTO>> batchQuery(OrderFileOperationLogBatchQueryRequestDTO requestDTO) {
        Preconditions.notNull(requestDTO, "请求参数不能为空");

        // 如果orderIds和logIds都为空，返回错误
        if (CollectionUtils.isEmpty(requestDTO.getOrderIds()) && CollectionUtils.isEmpty(requestDTO.getLogIds())) {
            Preconditions.isTrue(false, "orderIds和logIds不能同时为空");
        }
        Preconditions.isTrue(CollectionUtils.size(requestDTO.getOrderIds()) <= 200, "orderIds单次最多支持200个");
        Preconditions.isTrue(CollectionUtils.size(requestDTO.getLogIds()) <= 200, "logIds单次最多支持200个");

        List<OrderFileOperationLogDO> logDOList = New.list();

        // 如果orderIds不为空，则按orderIds查询
        if (CollectionUtils.isNotEmpty(requestDTO.getOrderIds())) {
            logDOList = orderFileOperationLogMapper.selectByOrderIds(requestDTO.getOrderIds());
        } else if (CollectionUtils.isNotEmpty(requestDTO.getLogIds())) {
            // 如果logIds不为空，则按logIds查询
            logDOList = orderFileOperationLogMapper.selectByLogIds(requestDTO.getLogIds());
        }

        List<OrderFileOperationLogDTO> logDTOList = OrderFileOperationLogTranslator.listDo2Dto(logDOList);
        return RemoteResponse.success(logDTOList);
    }

    /**
     * 批量保存订单文件操作日志
     *
     * @param fileOperationLogs 批量保存请求参数
     * @return 是否保存成功
     */
    @Override
    public RemoteResponse<Boolean> batchSave(List<OrderFileOperationLogRequestDTO> fileOperationLogs) {

        if (CollectionUtils.isEmpty(fileOperationLogs)) {
            return RemoteResponse.success();
        }
        Preconditions.isTrue(CollectionUtils.size(fileOperationLogs) <= 200, "批量保存记录数单次最多支持200条");

        List<OrderFileOperationLogDO> logDOList = OrderFileOperationLogTranslator.listReqDto2Do(fileOperationLogs);
        orderFileOperationLogMapper.batchInsert(logDOList);

        return RemoteResponse.success();
    }
}
