package com.reagent.order.rpc.orderextra;

import com.reagent.order.base.order.dto.OrderDetailAcceptanceFileDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptanceFileRequestDTO;
import com.reagent.order.base.order.mapper.OrderDetailAcceptanceFileMapper;
import com.reagent.order.base.order.model.OrderDetailAcceptanceFileDO;
import com.reagent.order.base.order.service.OrderDetailAcceptanceFileRpcService;
import com.reagent.order.base.order.translator.OrderDetailAcceptanceFileTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.utils.BusinessErrUtil;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


@MSharpService
public class OrderDetailAcceptanceFileRpcServiceImpl implements OrderDetailAcceptanceFileRpcService {


    @Resource
    private OrderDetailAcceptanceFileMapper orderDetailAcceptanceFileMapper;


    /**
     * 根据订单ID查询 详情关联验收文件列表
     *
     * @param orderId 订单ID
     * @return 详情关联验收文件列表
     */
    @Override
    public RemoteResponse<List<OrderDetailAcceptanceFileDTO>> listByOrderId(Integer orderId) {
        BusinessErrUtil.notNull(orderId, "orderId不能为空");
        List<OrderDetailAcceptanceFileDO> fileDOS = orderDetailAcceptanceFileMapper.selectByOrderId(orderId);
        if (CollectionUtils.isEmpty(fileDOS)) {
            return RemoteResponse.success(New.emptyList());
        }
        List<OrderDetailAcceptanceFileDTO> fileDTOS = OrderDetailAcceptanceFileTranslator.listDo2Dto(fileDOS);
        return RemoteResponse.success(fileDTOS);
    }
    
    /**
     * 根据订单ID集合批量查询 详情关联验收文件列表
     * 注意：每次最多查询200个订单
     *
     * @param orderIds 订单ID集合（最大支持200个）
     * @return 详情关联验收文件列表
     */
    @Override
    public RemoteResponse<List<OrderDetailAcceptanceFileDTO>> listByOrderIds(List<Integer> orderIds) {
        Preconditions.notEmpty(orderIds, "orderIds不能为空");
        // 限制每次最多查询200个订单
        Preconditions.isTrue(CollectionUtils.size(orderIds) <= 200, "orderIds单次最多支持200个");
        List<OrderDetailAcceptanceFileDO> fileDOS = orderDetailAcceptanceFileMapper.selectByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(fileDOS)) {
            return RemoteResponse.success(New.emptyList());
        }
        List<OrderDetailAcceptanceFileDTO> fileDTOS = OrderDetailAcceptanceFileTranslator.listDo2Dto(fileDOS);
        return RemoteResponse.success(fileDTOS);
    }


    /**
     * 根据订单ID删除 详情关联验收文件列表
     *
     * @param orderId 订单ID
     * @return 详情关联验收文件列表
     */
    @Override
    public RemoteResponse<Boolean> deleteByOrderId(Integer orderId) {
        BusinessErrUtil.notNull(orderId, "orderId不能为空");
        orderDetailAcceptanceFileMapper.deleteByOrderId(orderId);
        return RemoteResponse.success();
    }


    /**
     * 批量保存 详情关联验收文件列表
     *
     * @param list 详情关联验收文件列表
     * @return boolean
     */
    @Override
    public RemoteResponse<Boolean> batchSave(List<OrderDetailAcceptanceFileRequestDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return RemoteResponse.success();
        }
        List<OrderDetailAcceptanceFileDO> fileDOS = OrderDetailAcceptanceFileTranslator.listReqDto2Do(list);
        orderDetailAcceptanceFileMapper.batchInsert(fileDOS);
        return RemoteResponse.success();
    }
}