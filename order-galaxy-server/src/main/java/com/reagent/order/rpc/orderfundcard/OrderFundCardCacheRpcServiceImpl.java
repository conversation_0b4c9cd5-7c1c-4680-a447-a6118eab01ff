package com.reagent.order.rpc.orderfundcard;

import com.ruijing.order.annotation.ServiceLog;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.base.order.mapper.OrderFundCardMapper;
import com.reagent.order.base.order.model.OrderFundCardDO;
import com.reagent.order.base.order.service.OrderFundCardCacheRpcService;
import com.reagent.order.base.order.translator.OrderFundCardTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单关联经费卡服务rpc实现
 * <AUTHOR>
 */
@CatAnnotation
@MSharpService
public class OrderFundCardCacheRpcServiceImpl implements OrderFundCardCacheRpcService {

    @Resource
    private OrderFundCardMapper orderFundCardMapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderFundCardCacheRpcServiceImpl.class);

    @Override
    @ServiceLog
    public RemoteResponse saveOrderFundCard(List<OrderFundCardDTO> orderFundCardList) {
        LOGGER.info("进入saveOrderFundCard方法 => 入参：{}", JsonUtils.toJson(orderFundCardList));
        Assert.notEmpty(orderFundCardList, "保存失败！经费卡对象为空！");

        // 1. dto to do
        List<OrderFundCardDO> orderFundCardDoList = orderFundCardList
                .stream()
                .map(f -> {
                    OrderFundCardDO orderFundCardDo = OrderFundCardTranslator.dtoToDo(f);
                    return orderFundCardDo;
                }).collect(Collectors.toList());

        // 2. 保存新订单经费卡关联记录
        orderFundCardMapper.insertList(orderFundCardDoList);

        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog
    public RemoteResponse removeOrderFundCard(List<Integer> orderIdList) {
        Assert.notEmpty(orderIdList, "删除失败，订单id为空！");
        orderFundCardMapper.deleteByOrderIdIn(orderIdList);
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog
    public RemoteResponse<List<OrderFundCardDTO>> findByOrderIdDesc(List<Integer> orderIdList) {
        Assert.notEmpty(orderIdList, "查询失败，订单id为空！");
        List<OrderFundCardDO> result = orderFundCardMapper.findByOrderIdInDesc(orderIdList);
        // 筛选每一组orderId最大sequence的经费卡对象

        HashMap<Integer, Optional<OrderFundCardDO>> maxSequenceMap = result.stream().collect(Collectors.groupingBy(
                OrderFundCardDO::getOrderId,
                HashMap::new,
                Collectors.maxBy(Comparator.comparingInt(OrderFundCardDO::getSequence))));

        List<OrderFundCardDTO> maxSequenceCard = new ArrayList<>(maxSequenceMap.values().size());
        for (Optional<OrderFundCardDO> value : maxSequenceMap.values()) {
            if (value.isPresent()) {
                maxSequenceCard.add(OrderFundCardTranslator.doToDto(value.get()));
            }
        }

        return RemoteResponse.<List<OrderFundCardDTO>>custom().setSuccess().setData(maxSequenceCard).build();
    }
}
