package com.reagent.order.rpc.materialCode.service.impl;

import com.reagent.order.base.order.dto.OrderMaterialCodeDTO;
import com.reagent.order.base.order.dto.request.OrderMaterialCodeRequest;
import com.reagent.order.base.order.mapper.OrderMaterialCodeMapper;
import com.reagent.order.base.order.model.OrderMaterialCode;
import com.reagent.order.base.order.service.OrderMaterialCodeRPCService;
import com.reagent.order.base.order.translator.OrderMaterialCodeTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Name: OrderMaterialCodeRPCServiceImpl
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/3/11
 */
@MSharpService
public class OrderMaterialCodeRPCServiceImpl implements OrderMaterialCodeRPCService {

    @Resource
    private OrderMaterialCodeMapper orderMaterialCodeMapper;


    /**
     * 新增物资编码
     *
     * @param orderMaterialCodeDTOList 新增入参
     * @return 新增结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RemoteResponse<Integer> insert(List<OrderMaterialCodeDTO> orderMaterialCodeDTOList) {
        preCheck(orderMaterialCodeDTOList);
        List<OrderMaterialCode> materialCodeList = orderMaterialCodeDTOList
                .stream()
                .map(OrderMaterialCodeTranslator::dto2DO)
                .collect(Collectors.toList());
        int insertCount = orderMaterialCodeMapper.insertSelectiveBatch(materialCodeList);
        return RemoteResponse.<Integer>custom().setData(insertCount).setSuccess();
    }


    /**
     * 通过 品牌+货号+规格 查询商品编码
     *
     * @param requestList 查询入参
     * @return 查询结果
     */
    @Override
    public RemoteResponse<List<OrderMaterialCodeDTO>> queryByParam(List<OrderMaterialCodeRequest> requestList) {
        List<OrderMaterialCode> orderMaterialCodeList = New.list();
        for (OrderMaterialCodeRequest request : requestList) {
            List<OrderMaterialCode> list = orderMaterialCodeMapper.queryByParam(request.getBrand(), request.getGoodCode(),
                                                                                                  request.getSpec());
            orderMaterialCodeList.addAll(list);
        }
        if (CollectionUtils.isEmpty(orderMaterialCodeList)){
            return RemoteResponse.<List<OrderMaterialCodeDTO>>custom().setData(New.list()).setSuccess();
        }
        List<OrderMaterialCodeDTO> orderMaterialCodeDTOList = orderMaterialCodeList
                .stream()
                .map(OrderMaterialCodeTranslator::do2DTO)
                .collect(Collectors.toList());
        return RemoteResponse.<List<OrderMaterialCodeDTO>>custom().setData(orderMaterialCodeDTOList).setSuccess();
    }

    /**
     * 更新订单物资编码信息
     *
     * @param orderMaterialCodeDTO 修改入参
     * @return 修改结果
     */
    @Override
    public RemoteResponse<Integer> update(OrderMaterialCodeDTO orderMaterialCodeDTO) {
        preCheck(New.list(orderMaterialCodeDTO));
        int i = orderMaterialCodeMapper.updateByPrimaryKeySelective(OrderMaterialCodeTranslator.dto2DO(orderMaterialCodeDTO));
        return RemoteResponse.<Integer>custom().setData(i).setSuccess();
    }

    /**
     * 参数校验
     *
     * @param materialCodeDTOList 入参
     */
    private void preCheck(List<OrderMaterialCodeDTO> materialCodeDTOList) {
        for (OrderMaterialCodeDTO materialCodeDTO : materialCodeDTOList) {
            Preconditions.notNull(materialCodeDTO.getMaterialCode(), "物资编码不能为空！");
            Preconditions.isTrue(materialCodeDTO.getMaterialCode().length() <= 10, "物资编码长度小于10个字母或数字！");
        }
 }

}
