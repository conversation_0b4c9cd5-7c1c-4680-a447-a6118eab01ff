package com.reagent.order.rpc.orderexport.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.reagent.order.base.order.dto.OrderExportDTO;
import com.reagent.order.base.order.dto.OrderExportQueryDTO;
import com.reagent.order.base.order.dto.OrderExportResultDTO;
import com.reagent.order.base.order.mapper.OrderExportMapper;
import com.reagent.order.base.order.model.OrderExportDO;
import com.reagent.order.base.order.service.OrderExportRpcService;
import com.reagent.order.base.order.translator.OrderExportTranslator;
import com.reagent.order.utils.PageResponseUtils;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/6 0006 10:53
 * @Version 1.0
 * @Desc:描述
 */
@MSharpService
public class OrderExportRpcServiceImpl implements OrderExportRpcService {

    @Resource
    private OrderExportMapper orderExportMapper;

    @Override
    @ServiceLog(description = "查找订单导出列表")
    public RemoteResponse<BasePageResultDTO<OrderExportDTO>> findOrderExportList(OrderExportQueryDTO orderExportQueryDTO) {
        Integer pageSize = orderExportQueryDTO.getPageSize();
        Integer pageNo = orderExportQueryDTO.getPageNo();
        Integer userId = orderExportQueryDTO.getUserId();
        Integer status = orderExportQueryDTO.getStatus();
        Integer orgId = orderExportQueryDTO.getOrgId();
        List<Integer> fileTypeList = orderExportQueryDTO.getFileTypeList();
        Preconditions.notNull(userId, "用户id不能为空");
        Preconditions.notNull(orgId, "组织id不能为空");
        Preconditions.notEmpty(fileTypeList, "文件来源类型不能为空");
        Date exportDateStart = orderExportQueryDTO.getExportDateStart();
        Date exportDateEnd = orderExportQueryDTO.getExportDateEnd();
        BasePageResultDTO<OrderExportDTO> pageResultDTO = PageResponseUtils.pageInvoke(() -> orderExportMapper.selectOrderExportList(userId, orgId, fileTypeList,status, exportDateStart, exportDateEnd)
                , pageNo, pageSize);
        return RemoteResponse.success(pageResultDTO);
    }

    @Override
    @ServiceLog(description = "删除订单导出信息")
    public RemoteResponse<Boolean> deleteOrderExportInfoById(Integer id) {
        Preconditions.notNull(id, "id不能为空");
        orderExportMapper.deleteByPrimaryKey(id);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "保存订单导出信息")
    public RemoteResponse<OrderExportResultDTO> saveOrderExport(OrderExportDTO orderExportDTO) {
        Preconditions.notNull(orderExportDTO, "入参为空");
        OrderExportDO orderExportDO = OrderExportTranslator.dtoToDo(orderExportDTO);
        orderExportMapper.insertSelective(orderExportDO);
        OrderExportResultDTO orderExportResultDTO = new OrderExportResultDTO();
        orderExportResultDTO.setId(orderExportDO.getId());
        return RemoteResponse.success(orderExportResultDTO);
    }

    @Override
    @ServiceLog(description = "更新订单导出信息")
    public RemoteResponse<Boolean> updateById(OrderExportDTO orderExportDTO) {
        Preconditions.notNull(orderExportDTO, "入参为空");
        OrderExportDO orderExportDO = OrderExportTranslator.dtoToDo(orderExportDTO);
        orderExportMapper.updateByPrimaryKeySelective(orderExportDO);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "根据id查询")
    public RemoteResponse<OrderExportDTO> findById(Integer id) {
        Preconditions.notNull(id, "id不能为空");
        OrderExportDO orderExportDO = orderExportMapper.selectByPrimaryKey(id);
        OrderExportDTO orderExportDTO = OrderExportTranslator.doToDto(orderExportDO);
        return RemoteResponse.success(orderExportDTO);
    }
}
