package com.reagent.order.rpc.ordereventstatus.service.impl;

import com.reagent.order.base.order.dto.OrderEventStatusDTO;
import com.reagent.order.base.order.mapper.OrderEventStatusMapper;
import com.reagent.order.base.order.model.OrderEventStatusDO;
import com.reagent.order.base.order.service.OrderEventStatusRpcService;
import com.reagent.order.base.order.translator.OrderEventStatusTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 10:50
 * @description
 */
@MSharpService
public class OrderEventStatusRpcServiceImpl implements OrderEventStatusRpcService {
    
    @Resource
    private OrderEventStatusMapper orderEventStatusMapper;
    
    @Override
    public RemoteResponse<Integer> insertList(List<OrderEventStatusDTO> orderEventStatusDTOS) {
        Preconditions.notEmpty(orderEventStatusDTOS, "批量插入订单事件状态表入参不可为空");
        List<OrderEventStatusDO> orderEventStatusDOList = OrderEventStatusTranslator.dtoListToDoList(orderEventStatusDTOS);
        int insertCount = orderEventStatusMapper.insertList(orderEventStatusDOList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(insertCount);
    }

    @Override
    public RemoteResponse<Integer> updateByOrderNoAndEventType(OrderEventStatusDTO orderEventStatusDTO) {
        Preconditions.notNull(orderEventStatusDTO,"更新订单事件状态表参数不可为空");
        Preconditions.notNull(orderEventStatusDTO.getOrderNo(),"更新订单事件状态表的订单号不可为空");
        Preconditions.notNull(orderEventStatusDTO.getEventType(),"更新订单事件状态表的事件类型不可为空");
        OrderEventStatusDO orderEventStatusDO = OrderEventStatusTranslator.dtoToDo(orderEventStatusDTO);
        int updateCount = orderEventStatusMapper.updateByOrderNoAndEventType(orderEventStatusDO);
        return RemoteResponse.<Integer>custom().setSuccess().setData(updateCount);
    }

    @Override
    public RemoteResponse<OrderEventStatusDTO> selectByOrderNoAndEventType(OrderEventStatusDTO orderEventStatusDTO) {
        Preconditions.notNull(orderEventStatusDTO,"查询订单事件状态表参数不可为空");
        Preconditions.notNull(orderEventStatusDTO.getOrderNo(),"查询订单事件状态表参数的订单号不可为空");
        Preconditions.notNull(orderEventStatusDTO.getEventType(),"查询订单事件状态表参数的事件类型不可为空");
        OrderEventStatusDO orderEventStatusDO = OrderEventStatusTranslator.dtoToDo(orderEventStatusDTO);
        List<OrderEventStatusDO> orderEventStatusDOList = orderEventStatusMapper.selectByOrderNoAndEventType(orderEventStatusDO);
        OrderEventStatusDTO returnData = null;
        if(CollectionUtils.isNotEmpty(orderEventStatusDOList)){
            returnData = OrderEventStatusTranslator.doToDto(orderEventStatusDOList.get(0));
        }
        return RemoteResponse.<OrderEventStatusDTO>custom().setSuccess().setData(returnData);
    }

    @Override
    public RemoteResponse<List<OrderEventStatusDTO>> selectByOrderNoListAndEventType(List<String> orderNoList, Integer eventType) {
        Preconditions.notEmpty(orderNoList,"查询订单事件状态表参数的订单列表不可为空");
        Preconditions.notNull(eventType,"查询订单事件状态表参数的事件类型不可为空");
        List<OrderEventStatusDO> orderEventStatusDOList = orderEventStatusMapper.selectByOrderNoListAndEventType(orderNoList,eventType);
        List<OrderEventStatusDTO> orderEventStatusDTOList = OrderEventStatusTranslator.doListToDtoList(orderEventStatusDOList);
        return RemoteResponse.<List<OrderEventStatusDTO>>custom().setSuccess().setData(orderEventStatusDTOList);
    }

    @Override
    public RemoteResponse<List<OrderEventStatusDTO>> selectByOrderNo(String orderNo) {
        Preconditions.notNull(orderNo,"查询订单事件状态表参数的订单号不可为空");
        OrderEventStatusDO orderEventStatusDO = new OrderEventStatusDO();
        orderEventStatusDO.setOrderNo(orderNo);
        List<OrderEventStatusDO> orderEventStatusDOList = orderEventStatusMapper.selectByOrderNoAndEventType(orderEventStatusDO);
        List<OrderEventStatusDTO> orderEventStatusDTOList = OrderEventStatusTranslator.doListToDtoList(orderEventStatusDOList);
        return RemoteResponse.<List<OrderEventStatusDTO>>custom().setSuccess().setData(orderEventStatusDTOList);
    }

    @Override
    public RemoteResponse<Integer> deleteByOrderNoAndEventType(OrderEventStatusDTO orderEventStatusDTO) {
        Preconditions.notNull(orderEventStatusDTO,"删除订单事件状态表参数不可为空");
        Preconditions.notNull(orderEventStatusDTO.getOrderNo(),"删除订单事件状态表参数的订单号不可为空");
        Preconditions.notNull(orderEventStatusDTO.getEventType(),"删除订单事件状态表参数的事件类型不可为空");
        OrderEventStatusDO orderEventStatusDO = OrderEventStatusTranslator.dtoToDo(orderEventStatusDTO);
        Integer deleteCount = orderEventStatusMapper.deleteByOrderNoAndEventType(orderEventStatusDO);
        return RemoteResponse.<Integer>custom().setSuccess().setData(deleteCount);
    }

    @Override
    public RemoteResponse<Integer> deleteByOrderNo(String orderNo) {
        Preconditions.notNull(orderNo,"删除订单事件状态表参数的订单号不可为空");
        OrderEventStatusDO orderEventStatusDO = new OrderEventStatusDO();
        orderEventStatusDO.setOrderNo(orderNo);
        Integer deleteCount = orderEventStatusMapper.deleteByOrderNoAndEventType(orderEventStatusDO);
        return RemoteResponse.<Integer>custom().setSuccess().setData(deleteCount);
    }
    
}
