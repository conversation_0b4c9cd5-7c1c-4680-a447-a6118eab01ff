package com.reagent.order.rpc.reforderfundcard.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.reagent.order.base.order.dto.RefOrderFundCardDTO;
import com.reagent.order.base.order.mapper.RefOrderFundCardMapper;
import com.reagent.order.base.order.model.RefOrderFundCard;
import com.reagent.order.base.order.service.RefFundCardOrderRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 订单关联经费卡表服务类
 * @date 2024/2/5 下午 03:03
 */
@MSharpService
public class RefFundCardOrderRpcServiceImpl implements RefFundCardOrderRpcService {

    @Resource
    private RefOrderFundCardMapper refOrderFundCardMapper;

    @Override
    public RemoteResponse<List<RefOrderFundCardDTO>> listInOrderId(List<Integer> orderIdList) {
        return RemoteResponse.success(refOrderFundCardMapper.listInOrderId(orderIdList).stream().map(RefOrderFundCard::toDTO).collect(Collectors.toList()));
    }

    @Override
    @ServiceLog(description = "插入关联经费信息表", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> batchInsertSelective(List<RefOrderFundCardDTO> refOrderFundCardDTOList) {
        if(CollectionUtils.isNotEmpty(refOrderFundCardDTOList)) {
            refOrderFundCardMapper.batchInsertSelective(refOrderFundCardDTOList.stream().map(RefOrderFundCard::fromDTO).collect(Collectors.toList()));
        }
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "更新经费信息表", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> batchUpdateSelective(List<RefOrderFundCardDTO> refOrderFundCardDTOList) {
        if(CollectionUtils.isNotEmpty(refOrderFundCardDTOList)){
            refOrderFundCardMapper.batchUpdateSelective(refOrderFundCardDTOList.stream().map(RefOrderFundCard::fromDTO).collect(Collectors.toList()));
        }
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "根据订单id更新经费卡信息", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> updateByOrderId(RefOrderFundCardDTO refOrderFundCardDTO) {
        if(refOrderFundCardDTO != null){
            refOrderFundCardMapper.updateByOrderIdSelective(RefOrderFundCard.fromDTO(refOrderFundCardDTO));
        }
        return RemoteResponse.success();
    }

    @Override
    public RemoteResponse<Boolean> deleteInOrderId(List<Integer> orderIdList) {
        if (CollectionUtils.isNotEmpty(orderIdList)) {
            refOrderFundCardMapper.deleteInId(orderIdList);
        }
        return RemoteResponse.success();
    }
}
