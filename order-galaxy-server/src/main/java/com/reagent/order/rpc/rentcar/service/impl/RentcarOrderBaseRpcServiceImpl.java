package com.reagent.order.rpc.rentcar.service.impl;

import com.reagent.order.base.order.dto.OrderBasePageResultDTO;
import com.reagent.order.base.order.service.OrderBaseService;
import com.reagent.order.base.rentcar.dto.BuyerRentcarOrderBaseParamDTO;
import com.reagent.order.base.rentcar.dto.SuppRentcarOrderBaseParamDTO;
import com.reagent.order.base.rentcar.service.RentcarOrderBaseRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;

import javax.annotation.Resource;

/**
 * @description: rentcar orderBase rpc服务
 * @author: zhukai
 * @create: 2019-07-30 14:55
 **/
@MSharpService
@CatAnnotation
public class RentcarOrderBaseRpcServiceImpl implements RentcarOrderBaseRpcService {

    @Resource
    private OrderBaseService orderBaseService;

    /**
     * 供应商 网约车 orderBase列表查询
     * @param suppRentcarOrderBaseParamDTO 入参
     * @return RentcarOrderBaseResultDTO
     */
    @Override
    public RemoteResponse<OrderBasePageResultDTO> rentcarOrderBaseListForSupp(SuppRentcarOrderBaseParamDTO suppRentcarOrderBaseParamDTO){
        return orderBaseService.rentcarOrderBaseListForSupp(suppRentcarOrderBaseParamDTO);
    }

    /**
     * 采购网约车 orderBase信息列表
     * @param buyerRentcarOrderBaseParamDTO 入参
     * @return RentcarOrderBaseResultDTO
     */
    @Override
    public RemoteResponse<OrderBasePageResultDTO> rentcarOrderBaseListForBuyer(BuyerRentcarOrderBaseParamDTO buyerRentcarOrderBaseParamDTO){
        return orderBaseService.rentcarOrderBaseListForBuyer(buyerRentcarOrderBaseParamDTO);
    }
}
