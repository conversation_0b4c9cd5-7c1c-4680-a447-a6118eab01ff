package com.reagent.order.rpc.exporttemplate.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.reagent.order.base.order.dto.ExportTemplateDTO;
import com.reagent.order.base.order.mapper.ExportTemplateMapper;
import com.reagent.order.base.order.model.ExportTemplateDO;
import com.reagent.order.base.order.service.ExportTemplateRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/9/1 15:12
 */
@MSharpService
public class ExportTemplateRpcServiceImpl implements ExportTemplateRpcService {

    @Resource
    private ExportTemplateMapper exportTemplateMapper;

    @Override
    public RemoteResponse<List<ExportTemplateDTO>> getExportTemplateByUserInfo(ExportTemplateDTO exportTemplateDTO) {
        Preconditions.isTrue(exportTemplateDTO.getUserType() != null && exportTemplateDTO.getUserId() != null, "查询模板必须同时传入用户类型和用户id");
        List<ExportTemplateDO> templateList = exportTemplateMapper.selectByUserTypeAndUserId(exportTemplateDTO.getUserType(), exportTemplateDTO.getUserId());
        List<ExportTemplateDTO> templateDTOList = this.exportTemplateDO2DTOList(templateList);
        return RemoteResponse.<List<ExportTemplateDTO>>custom().setData(templateDTOList).setSuccess();
    }

    @Override
    public RemoteResponse<Integer> countExportTemplateByUserInfo(ExportTemplateDTO exportTemplateDTO) {
        Preconditions.isTrue(exportTemplateDTO.getUserType() != null && exportTemplateDTO.getUserId() != null, "查询模板计数必须同时传入用户类型和用户id");
        Integer countTemplate = exportTemplateMapper.countByUserTypeAndUserId(exportTemplateDTO.getUserType(), exportTemplateDTO.getUserId());
        return RemoteResponse.<Integer>custom().setData(countTemplate).setSuccess();
    }

    @Override
    @ServiceLog(description = "保存导出模板", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> saveExportTemplate(ExportTemplateDTO exportTemplateDTO) {
        Preconditions.isTrue(exportTemplateDTO.getUserType() != null && exportTemplateDTO.getUserId() != null
                        && StringUtils.isNotBlank(exportTemplateDTO.getTemplateJson()),
                "保存必须同时传入用户类型和用户id和模板json内容");
        ExportTemplateDO template = new ExportTemplateDO();
        template.setUserId(exportTemplateDTO.getUserId());
        template.setUserType(exportTemplateDTO.getUserType());
        template.setTemplateJson(exportTemplateDTO.getTemplateJson());
        template.setTemplateName(exportTemplateDTO.getTemplateName());
        template.setOrgId(exportTemplateDTO.getOrgId());
        template.setShareStatus(exportTemplateDTO.getShareStatus());
        exportTemplateMapper.insertSelective(template);
        return RemoteResponse.success();
    }

    @Override
    public RemoteResponse<Boolean> deleteExportTemplate(ExportTemplateDTO exportTemplateDTO) {
        Preconditions.notNull(exportTemplateDTO.getId(), "删除操作时，传入的模板id不可为空");
        exportTemplateMapper.deleteByPrimaryKey(exportTemplateDTO.getId());
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "更新导出模板", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> updateExportTemplate(ExportTemplateDTO exportTemplateDTO) {
        Preconditions.notNull(exportTemplateDTO.getId(), "更新操作时，传入的模板id不可为空");
        ExportTemplateDO template = new ExportTemplateDO();
        template.setId(exportTemplateDTO.getId());
        template.setUserId(exportTemplateDTO.getUserId());
        template.setUserType(exportTemplateDTO.getUserType());
        template.setTemplateJson(exportTemplateDTO.getTemplateJson());
        template.setTemplateName(exportTemplateDTO.getTemplateName());
        template.setOrgId(exportTemplateDTO.getOrgId());
        template.setShareStatus(exportTemplateDTO.getShareStatus());
        int i = exportTemplateMapper.updateByPrimaryKeySelective(template);
        // 如果是唯一索引报错会直接在mapper就抛出错误
        Preconditions.isTrue(i == 1, "更新失败，不存在此templateId="+exportTemplateDTO.getId() + "的数据");
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "根据共享状态和单位，计算模板数量", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Integer> countShareTemplateInOrg(ExportTemplateDTO exportTemplateDTO) {
        Preconditions.isTrue(exportTemplateDTO.getOrgId() != null && exportTemplateDTO.getShareStatus() != null, "根据共享状态和单位，计算模板数量，请传入orgId与shareStatus");
        Integer countShareTemplate = exportTemplateMapper.countByOrgIdAndShareStatus(exportTemplateDTO.getOrgId(), exportTemplateDTO.getShareStatus());
        return RemoteResponse.success(countShareTemplate);
    }

    @Override
    @ServiceLog(description = "根据共享状态和单位，获取此共享状态的模板列表", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<List<ExportTemplateDTO>> getExportTemplateByShareStatusInOrg(ExportTemplateDTO exportTemplateDTO) {
        Preconditions.isTrue(exportTemplateDTO.getOrgId() != null && exportTemplateDTO.getShareStatus() != null, "根据共享状态和单位，获取此共享状态的模板列表，请传入orgId与shareStatus");
        List<ExportTemplateDO> exportTemplateList = exportTemplateMapper.selectByOrgIdAndShareStatus(exportTemplateDTO.getOrgId(), exportTemplateDTO.getShareStatus());
        return RemoteResponse.success(this.exportTemplateDO2DTOList(exportTemplateList));
    }

    /**
     * 模板do -> dto
     * @param templateDO
     * @return
     */
    private ExportTemplateDTO exportTemplateDO2DTO(ExportTemplateDO templateDO) {
        ExportTemplateDTO exportTemplateDTO = new ExportTemplateDTO();
        exportTemplateDTO.setId(templateDO.getId());
        exportTemplateDTO.setUserId(templateDO.getUserId());
        exportTemplateDTO.setUserType(templateDO.getUserType());
        exportTemplateDTO.setTemplateName(templateDO.getTemplateName());
        exportTemplateDTO.setTemplateJson(templateDO.getTemplateJson());
        exportTemplateDTO.setOrgId(templateDO.getOrgId());
        exportTemplateDTO.setShareStatus(templateDO.getShareStatus());
        return exportTemplateDTO;
    }

    /**
     * 模板do list -> dto list
     * @param templateDOList
     * @return
     */
    private List<ExportTemplateDTO> exportTemplateDO2DTOList(List<ExportTemplateDO> templateDOList) {
        List<ExportTemplateDTO> resList = New.listWithCapacity(templateDOList.size());
        for (ExportTemplateDO exportTemplateDO : templateDOList) {
            resList.add(this.exportTemplateDO2DTO(exportTemplateDO));
        }
        return resList;
    }
}
