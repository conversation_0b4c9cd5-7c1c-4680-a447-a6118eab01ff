package com.reagent.order.rpc.orderaddress.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.dto.request.OrderAddressRequestDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.reagent.order.base.order.mapper.OrderAddressDOMapper;
import com.reagent.order.base.order.model.OrderAddressDO;
import com.reagent.order.base.order.service.OrderAddressRPCService;
import com.reagent.order.base.order.translator.OrderAddressTranslator;
import com.reagent.order.rpc.client.OrderMasterRPCClient;
import com.reagent.order.utils.PageUtil;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.cache.RedisClient;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@MSharpService
public class OrderAddressRPCServiceImpl implements OrderAddressRPCService {

    @Resource
    private OrderAddressDOMapper orderAddressDOMapper;

    @Resource
    private OrderMasterRPCClient orderMasterRPCClient;

    @Resource
    private RedisClient redisClient;

    @Override
    @ServiceLog(description = "批量插入配送地址接口", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> insertList(List<OrderAddressDTO> request) {
        Preconditions.notEmpty(request, "request must not be empty");
        boolean idNotEmpty = request.stream().allMatch(it -> it.getId() != null);
        Preconditions.isTrue(idNotEmpty, "id must not be null");
        String uniqKey = "OrderAddressRPCService:insertList:" + request.stream().map(it -> it.getId().toString()).collect(Collectors.joining(","));

        redisClient.getLock(uniqKey, 3);
        List<String> orderNoList = request.stream().map(OrderAddressDTO::getOrderNo).collect(Collectors.toList());
        List<String> existedOrderNo = orderAddressDOMapper.findByOrderNoIn(orderNoList);
        List<OrderAddressDO> doList = request.stream().filter(it -> !existedOrderNo.contains(it.getOrderNo())).map(OrderAddressTranslator::dtoToDo).collect(Collectors.toList());
        List<OrderAddressDO> existDoList = request.stream().filter(it -> existedOrderNo.contains(it.getOrderNo())).map(OrderAddressTranslator::dtoToDo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(doList)) {
            orderAddressDOMapper.insertList(doList);
        }
        if (CollectionUtils.isNotEmpty(existDoList)) {
            orderAddressDOMapper.batchUpdateByOrderNo(existDoList);
        }
        redisClient.unlock(uniqKey);
        return RemoteResponse.<Integer>custom().setSuccess().setData(doList.size());
    }

    @Override
    @ServiceLog(description = "查询配送地址接口")
    public RemoteResponse<List<OrderAddressDTO>> listByOrderId(OrderBaseParamDTO request) {
        Preconditions.notEmpty(request.getOrderIdList(), "orderIdList must not be empty");
        List<Integer> orderIdList = request.getOrderIdList().stream().map(Long::intValue).collect(Collectors.toList());
        List<OrderAddressDO> queryResult = orderAddressDOMapper.findByIdIn(orderIdList);
        return RemoteResponse
                .<List<OrderAddressDTO>>custom()
                .setSuccess().setData(queryResult.stream().map(OrderAddressTranslator::doToDto).collect(Collectors.toList()));
    }

    @Override
    @ServiceLog(description = "更新配送地址接口", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> saveAddress(OrderAddressDTO request) {
        Preconditions.notNull(request.getId(), "id must not be null");
        Preconditions.notNull(request.getOrderNo(), "orderNo must not be null");
        OrderAddressDO orderAddressDO = OrderAddressTranslator.dtoToDo(request);
        Integer count = orderAddressDOMapper.countById(request.getId());
        int affect = 0;
        if (count > 0) {
            affect = orderAddressDOMapper.updateById(orderAddressDO);
        } else {
            affect = orderAddressDOMapper.insertList(Arrays.asList(orderAddressDO));
        }
        UpdateOrderParamDTO updateOrderParam = wrapUpdateParam(request);
        orderMasterRPCClient.updateById(updateOrderParam);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(affect > 0);
    }

    @Override
    public RemoteResponse<Boolean> updateAddress(OrderAddressDTO orderAddressDTO) {
        OrderAddressDO orderAddressDO = OrderAddressTranslator.dtoToDo(orderAddressDTO);
        orderAddressDOMapper.updateById(orderAddressDO);
        return RemoteResponse.success();
    }

    /**
     * 封装收货人入参信息
     * @param request 地址入参
     * @return  更新入参
     */
    private UpdateOrderParamDTO wrapUpdateParam(OrderAddressDTO request) {
        UpdateOrderParamDTO param = new UpdateOrderParamDTO();
        param.setOrderId(request.getId());
        //  加上收货人信息
        param.setBuyerContactMan(request.getReceiverName());
        param.setBuyerTelephone(request.getReceiverPhone());

        String deliveryAddress = StringUtils.defaultIfBlank(request.getProvince(), StringUtils.EMPTY)
                + StringUtils.defaultIfBlank(request.getCity(), StringUtils.EMPTY)
                + StringUtils.defaultIfBlank(request.getRegion(), StringUtils.EMPTY)
                + StringUtils.defaultIfBlank(request.getAddress(), StringUtils.EMPTY);
        if (StringUtils.isNotBlank(request.getLabel())) {
            deliveryAddress += "-" + request.getLabel();
            if (StringUtils.isNotBlank(request.getLabelRemarks())) {
                deliveryAddress += "（" + request.getLabelRemarks() + "）";
            }
        }


        if (DeliveryTypeEnum.PROXY.getCode().equals(request.getDeliveryType())) {
            String deliveryAddressProxy = request.getProvinceProxy()
                    + request.getCityProxy()
                    + request.getRegionProxy()
                    + request.getAddressProxy();
            deliveryAddress = deliveryAddressProxy
                    + " 转送 "
                    + deliveryAddress
                    + "，"
                    + request.getReceiverName()
                    + "，"
                    + request.getReceiverPhone();
        }

        param.setDeliveryPlace(deliveryAddress);

        return param;
    }

    @Override
    public RemoteResponse<OrderAddressDTO> findByOrderNo(OrderBaseParamDTO request) {
        String orderNo = request.getOrderNumber();
        Preconditions.notNull(orderNo, "orderNumber must be not null");
        OrderAddressDO byOrderNo = orderAddressDOMapper.findByOrderNo(orderNo);
        return RemoteResponse.<OrderAddressDTO>custom().setSuccess().setData(OrderAddressTranslator.doToDto(byOrderNo));
    }

    @Override
    public RemoteResponse<Boolean> batchUpdateByOrderNo(List<OrderAddressDTO> orderAddressDTOList) {
        if(CollectionUtils.isNotEmpty(orderAddressDTOList)) {
            orderAddressDOMapper.batchUpdateByOrderNo(orderAddressDTOList.stream().map(OrderAddressTranslator::dtoToDo).collect(Collectors.toList()));
        }
        return RemoteResponse.success();
    }

    @Override
    public PageableResponse<List<OrderAddressDTO>> findOrderIdInOperatorIdAndDeliveryStatus(OrderAddressRequestDTO requestDTO) {
        return PageUtil.pageInvoke(()-> orderAddressDOMapper.findOrderIdInOperatorIdAndDeliveryStatus(requestDTO), OrderAddressTranslator::doToDto, requestDTO.getPageNo(), requestDTO.getPageSize());
    }
}
