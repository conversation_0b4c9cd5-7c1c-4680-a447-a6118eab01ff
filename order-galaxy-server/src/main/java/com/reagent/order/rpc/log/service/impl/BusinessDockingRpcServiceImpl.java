package com.reagent.order.rpc.log.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.reagent.order.base.order.dto.BusinessDockingDTO;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.mapper.BusinessDockingMapper;
import com.reagent.order.base.order.model.BusinessDockingDO;
import com.reagent.order.base.order.service.BusinessDockingRpcService;
import com.reagent.order.base.order.translator.BusinessDockingTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/8/18 6:19 下午
 */
@MSharpService
public class BusinessDockingRpcServiceImpl implements BusinessDockingRpcService {

    @Resource
    private BusinessDockingMapper businessDockingMapper;

    @Override
    @ServiceLog(description = "通过业务单号查询业务订单")
    public RemoteResponse<List<BusinessDockingDTO>> findByBusinessNo(OrderBaseParamDTO request) {
        List<String> orderNumberList = request.getOrderNumberList();
        Preconditions.notEmpty(orderNumberList, "查询失败！订单号不能为空！");
        List<BusinessDockingDO> businessDockingOrder = businessDockingMapper.findByBusinessOrderNoIn(orderNumberList);

        List<BusinessDockingDTO> result = businessDockingOrder.stream().map(BusinessDockingTranslator::doToDto).collect(Collectors.toList());
        return RemoteResponse.<List<BusinessDockingDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "更新第三方业务单记录")
    public RemoteResponse<Integer> updateByBusinessNo(BusinessDockingDTO request) {
        String orderNo = request.getBusinessOrderNo();
        Preconditions.notNull(orderNo, "更新失败！业务单号不能为空！");

        BusinessDockingDO dockingDO = BusinessDockingTranslator.dtoToDo(request);
        int affect = businessDockingMapper.updateByBusinessOrderNo(dockingDO);
        return RemoteResponse.<Integer>custom().setData(affect).setSuccess();
    }

    @Override
    @ServiceLog(description = "保存第三方业务单记录")
    public RemoteResponse<Integer> saveBusinessOrders(List<BusinessDockingDTO> request) {
        Preconditions.notEmpty(request, "保存失败，对接单数量不能为空！");
        List<BusinessDockingDO> dockingDOList = request.stream().map(BusinessDockingTranslator::dtoToDo).collect(Collectors.toList());
        int affect = businessDockingMapper.insertList(dockingDOList);
        return RemoteResponse.<Integer>custom().setData(affect).setSuccess();
    }

    @Override
    @ServiceLog(description = "查询第三方业务单记录")
    public RemoteResponse<List<BusinessDockingDTO>> findByOrgCodeAndStatus(OrderBaseParamDTO request) {
        String orgCode = request.getOrgCode();
        Preconditions.notNull(orgCode, "查询失败，机构编号不能为空！");
        List<Integer> orderStatusList = request.getOrderStatusList();
        List<BusinessDockingDO> businessDockingList = businessDockingMapper.findByOrgCodeAndReagentStatusIn(orgCode, orderStatusList);

        List<BusinessDockingDTO> result = businessDockingList.stream().map(BusinessDockingTranslator::doToDto).collect(Collectors.toList());
        return RemoteResponse.<List<BusinessDockingDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "批量更新第三方业务单记录")
    public RemoteResponse<Integer> updateBatchByDockingNoList(List<BusinessDockingDTO> request) {
        Preconditions.notEmpty(request, "更新失败，入参不能为空！");
        List<BusinessDockingDO> list = request.stream().map(BusinessDockingTranslator::dtoToDo).collect(Collectors.toList());
        int affect = businessDockingMapper.updateBatchByBusinessOrderNoIn(list);
        return RemoteResponse.<Integer>custom().setData(affect).setSuccess();
    }
}
