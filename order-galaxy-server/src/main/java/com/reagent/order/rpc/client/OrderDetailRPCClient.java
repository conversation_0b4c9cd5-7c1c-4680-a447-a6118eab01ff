package com.reagent.order.rpc.client;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;

import java.util.List;

@ServiceClient
public class OrderDetailRPCClient {

    @MSharpReference(remoteAppkey = "store-order-service")
    private OrderDetailService orderDetailService;

    @ServiceLog(description = "根据订单id查询", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderDetailDTO> findByMasterId(Integer orderId) {
        Preconditions.notNull(orderId, "orderId must not be null");
        OrderDetailReq request = new OrderDetailReq();
        request.setOrderMasterId(orderId);
        RemoteResponse<List<OrderDetailDTO>> response = orderDetailService.findOrderDetailsByMasterId(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "根据商品id查询", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderDetailDTO> findByIdList(List<Integer> detailIdList){
        OrderDetailReq request = new OrderDetailReq();
        request.setOrderDetailIdList(detailIdList);
        RemoteResponse<List<OrderDetailDTO>> response = orderDetailService.findOrderDetailByIdList(request);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
