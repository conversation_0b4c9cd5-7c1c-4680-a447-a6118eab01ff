package com.reagent.order.rpc.client;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseQrCodeDangerousOccupyDTO;
import com.ruijing.store.wms.api.dto.requset.BizWarehouseEntryReq;
import com.ruijing.store.wms.api.service.BizEntryService;
import com.ruijing.store.wms.api.service.BizWarehouseDangerousOccupyStockRpcService;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ServiceClient
public class BizWarehouseRoomServiceClient {

    @MSharpReference(remoteAppkey = "store-wms-service")
    private BizEntryService bizEntryService;

    @MSharpReference
    private BizWarehouseDangerousOccupyStockRpcService bizWarehouseDangerousOccupyStockRpcService;


    @ServiceLog(description = "同步二维码信息到库房", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void syncQrCodeDetailList(BizWarehouseQrCodeDangerousOccupyDTO occupyDTO) {
        RemoteResponse<Boolean> response = bizWarehouseDangerousOccupyStockRpcService.syncQrCodeDetailList(occupyDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    /**
     * 查询订单对应提交入库人
     *
     * @param orderNoList
     * @return 有异常时返回空列表
     */
    @ServiceLog(description = "查询订单对应提交入库人", serviceType = ServiceType.RPC_CLIENT)
    public List<BizWarehouseEntryDTO> getEntryContactByOrderNo(List<String> orderNoList) {
        BizWarehouseEntryReq request = new BizWarehouseEntryReq();
        request.setOrderNos(orderNoList);
        RemoteResponse<List<BizWarehouseEntryDTO>> response = bizEntryService.queryApplyUser(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    public Map<String, String> getOrderNoEntryContactGuidMap(List<String> orderNoList) {
        List<BizWarehouseEntryDTO> list = getEntryContactByOrderNo(orderNoList);
        return list.stream().collect(Collectors.toMap(BizWarehouseEntryDTO::getOrderNo, BizWarehouseEntryDTO::getApplyUserGuid));
    }
}