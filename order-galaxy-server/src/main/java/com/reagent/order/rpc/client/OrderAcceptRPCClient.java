package com.reagent.order.rpc.client;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderAcceptRpcService;

@ServiceClient
public class OrderAcceptRPCClient {

    @MSharpReference(remoteAppkey = "store-order-service")
    private OrderAcceptRpcService orderAcceptRpcService;

    @ServiceLog(description = "订单验收", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public ReceiptOrderResponseDO orderAccept(OrderReceiptParamDTO request) {
        RemoteResponse<ReceiptOrderResponseDO> response = orderAcceptRpcService.autoAcceptOrder(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }
}
