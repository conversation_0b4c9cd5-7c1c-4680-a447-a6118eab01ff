package com.reagent.order.rpc.client;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.service.UserInfoService;

import java.util.List;
import java.util.stream.Collectors;

import static com.reagent.order.base.enums.ExecptionMessageEnum.USER_DISABLED_CONTACT_ADMIN;
import static com.reagent.order.base.enums.ExecptionMessageEnum.USER_NO_PURCHASING_CENTER;

@ServiceClient
public class UserRPCClient {

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserInfoService userInfoService;

    @ServiceLog(description = "通过guid获取用户基础信息", serviceType = ServiceType.RPC_CLIENT)
    public UserBaseInfoDTO getUserBaseInfoByGuid(String userGuid) {
        RemoteResponse<UserBaseInfoDTO> response = userInfoService.getUserBaseInfoByGuidAndOrgid(userGuid,null);

        UserBaseInfoDTO userBaseInfoDTO = response.getData();
        BusinessErrUtil.notNull(userBaseInfoDTO, USER_NO_PURCHASING_CENTER);
        BusinessErrUtil.isTrue(userBaseInfoDTO.getActivate(), USER_DISABLED_CONTACT_ADMIN);
        return userBaseInfoDTO;
    }

    @ServiceLog(description = "通过guid获取用户基础信息", serviceType = ServiceType.RPC_CLIENT)
    public List<UserBaseInfoDTO> getUserBaseInfoByGuidList(List<String> userGuidList, Integer orgId) {
        RemoteResponse<List<UserBaseInfoDTO>> response = userInfoService.getByGuids(userGuidList, orgId);
        List<UserBaseInfoDTO> userBaseInfoDTO = response.getData();
        BusinessErrUtil.notNull(userBaseInfoDTO, USER_NO_PURCHASING_CENTER);
        return response.getData().stream().filter(UserBaseInfoDTO::getActivate).collect(Collectors.toList());
    }
}
