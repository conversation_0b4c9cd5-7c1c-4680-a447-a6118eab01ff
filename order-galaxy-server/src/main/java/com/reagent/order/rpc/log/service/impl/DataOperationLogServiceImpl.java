package com.reagent.order.rpc.log.service.impl;

import com.reagent.order.base.log.dto.DataOperationLogDTO;
import com.reagent.order.base.log.mapper.DataOperationLogMapper;
import com.reagent.order.base.log.model.DataOperationLog;
import com.reagent.order.base.log.request.DataOperationLogListRequest;
import com.reagent.order.base.log.request.DataOperationLogQueryRequest;
import com.reagent.order.base.log.service.DataOperationLogService;
import com.reagent.order.base.log.translator.DataOperationLogTranslator;
import com.reagent.order.utils.PageResponseUtils;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/7 14:57
 * @description
 */
@MSharpService
public class DataOperationLogServiceImpl implements DataOperationLogService {

    @Resource
    private DataOperationLogMapper dataOperationLogMapper;

    @Override
    @ServiceLog(description = "插入OMS异常数据修正日志", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> insertLog(DataOperationLogDTO dataOperationLogDTO) {
        Preconditions.notNull(dataOperationLogDTO, "请求参数不可空");
        int count = dataOperationLogMapper.insertSelective(DataOperationLogTranslator.dto2Do(dataOperationLogDTO));
        return RemoteResponse.success(count > 0);
    }

    @Override
    @ServiceLog(description = "批量插入OMS异常数据修正日志", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> batchInsertLog(List<DataOperationLogDTO> dataOperationLogDTOList) {
        Preconditions.notEmpty(dataOperationLogDTOList, "请求参数不可空");
        int count = dataOperationLogMapper.batchInsertSelective(dataOperationLogDTOList.stream().map(DataOperationLogTranslator::dto2Do).collect(Collectors.toList()));
        return RemoteResponse.success(true);
    }

    @Override
    @ServiceLog(description = "查询OMS异常数据修正日志")
    public PageableResponse<List<DataOperationLogDTO>> listByParams(DataOperationLogListRequest dataOperationLogListRequest) {
        Preconditions.notNull(dataOperationLogListRequest, "请求参数不可空");
        Integer pageNo = dataOperationLogListRequest.getPageNo();
        Integer pageSize = dataOperationLogListRequest.getPageSize();
        Preconditions.notNull(pageNo, "页号不能为空");
        Preconditions.notNull(pageSize, "分页大小不可空");
        return PageResponseUtils.pageInvoke(
                () -> dataOperationLogMapper.listDataOperationLog(dataOperationLogListRequest),
                DataOperationLogTranslator::do2Dto,
                pageNo,
                pageSize);
    }
    
    @Override
    @ServiceLog(description = "根据批量订单号查询修正日志")
    public RemoteResponse<List<DataOperationLogDTO>> listByOrderNosAndTypes(DataOperationLogQueryRequest request) {
        Preconditions.notNull(request, "请求参数不可空");
        if (CollectionUtils.isEmpty(request.getOrderNos())) {
            return RemoteResponse.success(New.emptyList());
        }
        Preconditions.isTrue(CollectionUtils.size(request.getOrderNos()) <= 200, "单次最多支持200个订单号");

        List<DataOperationLog> logList = dataOperationLogMapper.listDataOperationLogByOrderNosAndTypes(request);
        if (CollectionUtils.isEmpty(logList)) {
            return RemoteResponse.success(New.emptyList());
        }
        List<DataOperationLogDTO> resultList = logList.stream().map(DataOperationLogTranslator::do2Dto).collect(Collectors.toList());
        return RemoteResponse.success(resultList);
    }
}
