package com.reagent.order.rpc.order.detail.batches.impl;

import com.google.common.collect.Lists;
import com.reagent.order.base.order.dto.OrderBaseParamDTO;
import com.reagent.order.base.order.dto.OrderDetailBatchesRequestDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeStatisticsDTO;
import com.reagent.order.base.order.dto.request.OrderQRCodePageRequestDTO;
import com.reagent.order.base.order.enums.BarCodePrintedEnum;
import com.reagent.order.base.order.enums.BarCodeStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductBatchesStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.reagent.order.base.order.mapper.OrderExtraMapper;
import com.reagent.order.base.order.model.OrderExtraDO;
import com.reagent.order.base.order.model.OrderUniqueBarCodeDO;
import com.reagent.order.base.order.service.OrderUniqueBarCodeRPCService;
import com.reagent.order.base.order.service.OrderUniqueBarCodeService;
import com.reagent.order.base.order.translator.OrderUniqueBarCodeTranslator;
import com.reagent.order.rpc.client.*;
import com.reagent.order.utils.IdGeneratorUtils;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.cache.RedisClient;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseQrCodeDangerousOccupyDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseQrCodeDangerousOccupyDetailDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.reagent.order.base.enums.ExecptionMessageEnum.INPUT_QUANTITY_EXCEEDS_REMAINING;

@MSharpService
public class OrderUniqueBarCodeRPCServiceImpl implements OrderUniqueBarCodeRPCService {

    private final Logger LOGGER = LoggerFactory.getLogger(OrderUniqueBarCodeRPCServiceImpl.class);

    private static final String CAT_TYPE = "OrderUniqueBarCodeRPCServiceImpl";

    /**
     * 默认类型--单位条形码
     */
    private final List<Integer> DEFAULT_TYPE = New.list(UniqueBarCodeTypeEnum.ORG.getCode());

    /**
     * 库房状态更新时，可以触发自动验收的库房状态
     */
    private final List<Integer> TRIGGER_ACCEPT_UPDATE_INVENTORY_STATUS = New.list(OrderProductInventoryStatusEnum.NO_NEED.getCode(),
            OrderProductInventoryStatusEnum.COMPLETE_INBOUND.getCode(),
            OrderProductInventoryStatusEnum.COMPLETE_OUTBOUND.getCode());

    /**
     * 交易状态更新时，可以触发自动验收的交易状态
     */
    private final List<Integer> TRIGGER_ACCEPT_UPDATE_TRANSACTION_STATUS = New.list(OrderProductTransactionStatusEnum.RECEIVED.getCode(),
            OrderProductTransactionStatusEnum.RETURNED.getCode(),
            OrderProductTransactionStatusEnum.CANCELED.getCode());

    /**
     * 表示空的detail_id
     */
    private final int NULL_DETAIL_ID = -1;

    /**
     * 问题订单，有14w个商品的订单
     */
    private final List<Integer> OVER_COUNT_DETAIL_ID_LIST = New.list(6227627,
            6227628,
            6227629,
            6227630,
            6227631,
            6227632,
            6227633,
            6227634,
            6227635);

    @Resource
    private OrderUniqueBarCodeService orderUniqueBarCodeService;

    @Resource
    private RedisClient redisClient;

    @Resource
    private OrderMasterRPCClient orderMasterRPCClient;

    @Resource
    private OrderDetailRPCClient orderDetailRPCClient;

    @Resource
    private OrderAcceptRPCClient orderAcceptRPCClient;

    @Resource
    private UserRPCClient userRPCClient;

    @Resource
    private BizWarehouseRoomServiceClient bizWarehouseRoomServiceClient;

    @Resource
    private OrderExtraMapper orderExtraMapper;

    @Override
    @ServiceLog(description = "查询商品订单明细批次(含二维码)")
    public RemoteResponse<List<OrderUniqueBarCodeDTO>> findByDetailId(OrderDetailBatchesRequestDTO request) {
        Integer detailId = request.getDetailId();
        List<Integer> detailIdList = request.getDetailIdList();
        Preconditions.isTrue(detailId != null || CollectionUtils.isNotEmpty(detailIdList), "detailId must be not null");
        List<Integer> typeList = CollectionUtils.isEmpty(request.getTypeList()) ? DEFAULT_TYPE : request.getTypeList();
        if (request.getLimit() == 1) {
            if(OVER_COUNT_DETAIL_ID_LIST.contains(detailId)){
                return RemoteResponse.success(New.emptyList());
            }
            OrderUniqueBarCodeDTO first = orderUniqueBarCodeService.findFirstByDetailId(detailId, typeList);
            return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(New.list(first));
        } else {
            List<OrderUniqueBarCodeDTO> result;
            List<Integer> needQryDetailIds = CollectionUtils.isNotEmpty(detailIdList) ? detailIdList : New.list();
            Preconditions.isTrue(detailIdList.size() <= 100, "number of detailIdList must be less than 100");
            if(detailId != null){
                needQryDetailIds.add(detailId);
            }
            // 过滤掉批次数量太多的订单
            needQryDetailIds = needQryDetailIds.stream().filter(d->!OVER_COUNT_DETAIL_ID_LIST.contains(d)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(needQryDetailIds)){
                return RemoteResponse.success(New.emptyList());
            }
            result = orderUniqueBarCodeService.findByDetailId(needQryDetailIds, typeList);
            return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(result);
        }
    }

    @Override
    @ServiceLog(description = "根据二维码查询详情")
    public RemoteResponse<OrderUniqueBarCodeDTO> findByBarCode(OrderDetailBatchesRequestDTO request) {
        String barCode = this.getBarCode(request);
        Preconditions.notNull(barCode, "barCode/uniBarCode must be not null");
        OrderUniqueBarCodeDTO code = orderUniqueBarCodeService.findByBarCode(barCode);
        return RemoteResponse.<OrderUniqueBarCodeDTO>custom().setSuccess().setData(code);
    }

    @Override
    @ServiceLog(description = "新增录入批次信息--单位一物一码用", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> addDetailBatches(List<OrderUniqueBarCodeDTO> request) {
        Preconditions.notEmpty(request, "request must be not empty");
        String orderNo = request.get(0).getOrderNo();
        // 校验剩余可补录批次的数量
        List<Integer> detailIdList = request.stream().distinct().map(OrderUniqueBarCodeDTO::getOrderDetailId).collect(Collectors.toList());
        Preconditions.isTrue(CollectionUtils.isNotEmpty(detailIdList), "请求入参中订单详情id未填写");
        redisClient.getLock(orderNo, 3);
        List<OrderUniqueBarCodeDTO> allBatchesList = orderUniqueBarCodeService.findByDetailId(detailIdList, DEFAULT_TYPE);
        // 未录入的批次信息
        List<OrderUniqueBarCodeDTO> unInputBatchesList = allBatchesList.stream()
                                                                       .filter(orderUniqueBarCodeDTO -> OrderProductBatchesStatusEnum.UN_INPUT.getCode() == orderUniqueBarCodeDTO.getBatchesStatus())
                                                                       .collect(Collectors.toList());
        // 校验可录数量
        Map<Integer, String> detailIdProductNameMap = request.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.collectingAndThen(Collectors.toList(), it -> it.get(0).getProductName())));
        Map<Integer, Long> inputCountMap = request.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.summingLong(OrderUniqueBarCodeDTO::getTotal)));
        Map<Integer, Long> remainCountMap = unInputBatchesList.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.counting()));
        remainCountMap.forEach((detailId, remainCount) -> {
            String productName = Optional.ofNullable(detailIdProductNameMap.get(detailId)).orElse(StringUtils.EMPTY);
            Long inputCount = Optional.ofNullable(inputCountMap.get(detailId)).orElse(0L);
            BusinessErrUtil.isTrue(remainCount >= inputCount, INPUT_QUANTITY_EXCEEDS_REMAINING, productName);
        });
        Map<Integer, Set<String>> detailIdMappingBarCodeSet = unInputBatchesList.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.mapping(OrderUniqueBarCodeDTO::getUniBarCode, Collectors.toSet())));
        List<OrderUniqueBarCodeDTO> updatedList = new ArrayList<>();
        request.forEach(it -> {
            for (int i = 0; i < it.getTotal(); i++) {
                Set<String> barCodeSet = detailIdMappingBarCodeSet.get(it.getOrderDetailId());
                if (CollectionUtils.isEmpty(barCodeSet)) {
                    return;
                }
                String barCode = barCodeSet.iterator().next();
                it.setUniBarCode(barCode);
                // 标识为已录入
                it.setBatchesStatus(OrderProductBatchesStatusEnum.INPUTTED.getCode());
                barCodeSet.remove(barCode);
                OrderUniqueBarCodeDTO newIt = it.clone();
                updatedList.add(newIt);
            }
        });

        // 设置批次信息
        orderUniqueBarCodeService.batchUpdateOrderUniqueBarCode(updatedList);
        redisClient.unlock(orderNo);

        return RemoteResponse.<Integer>custom().setSuccess().setData(unInputBatchesList.size());
    }

    @Override
    @ServiceLog(description = "更新二维码信息", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> setDetailBatches(List<OrderUniqueBarCodeDTO> request) {
        Preconditions.notEmpty(request, "error, request must not be null");
        boolean havingNull = request.stream().anyMatch(o -> o.getUniBarCode() == null);
        Preconditions.isTrue(!havingNull, "error, request must not having null of barcode/uniBarCode");
        int affect = orderUniqueBarCodeService.batchUpdateOrderUniqueBarCode(request);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    /**
     * 订单验收
     * @param request
     */
    private void orderAccept(List<OrderUniqueBarCodeDTO> request) {
        List<String> involveOrderNoList = request.stream().filter(barCode->UniqueBarCodeTypeEnum.ORG.getCode().equals(barCode.getType()) && barCode.getOrderNo() != null)
                .map(OrderUniqueBarCodeDTO::getOrderNo)
                .distinct()
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(request)){
            return;
        }
        // 查询当前的一物一码状态
        List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderUniqueBarCodeService.findByOrderNoList(involveOrderNoList, DEFAULT_TYPE);
        Map<String, List<OrderUniqueBarCodeDTO>> orderNoGroupMap = DictionaryUtils.groupBy(orderUniqueBarCodeDTOList, OrderUniqueBarCodeDTO::getOrderNo);

        List<OrderMasterDTO> orderMasterDTOList = orderMasterRPCClient.findByOrderNoList(involveOrderNoList);
        Map<String, String> orderNoEntryContactGuidMap = bizWarehouseRoomServiceClient.getOrderNoEntryContactGuidMap(involveOrderNoList);
        Map<String, OrderMasterDTO> orderNoIdentityMap = orderMasterDTOList.stream().collect(Collectors.toMap(OrderMasterDTO::getForderno, Function.identity()));
        List<UserBaseInfoDTO> userBaseInfoByGuidList = userRPCClient.getUserBaseInfoByGuidList(New.list(orderNoEntryContactGuidMap.values()), orderMasterDTOList.get(0).getFuserid());
        Map<String, UserBaseInfoDTO> guidIdentityMap = userBaseInfoByGuidList.stream().collect(Collectors.toMap(UserBaseInfoDTO::getGuid, Function.identity()));
        orderNoGroupMap.forEach((orderNo, orderBarCodeList) -> {
            OrderMasterDTO order = orderNoIdentityMap.get(orderNo);
            if (!(order != null && OrderStatusEnum.WaitingForReceive.getValue().equals(order.getStatus()))) {
                // 待验收的单据才需要出发验收
                return;
            }
            // 存在未入库 || 未退货完成的贴码商品, 不做收货（旧状态）
            boolean isOver = orderBarCodeList.stream().allMatch(it ->
                    BarCodeStatusEnum.ENTERED.code == it.getStatus()
                            || BarCodeStatusEnum.WAITING_FOR_EXIT.code == it.getStatus()
                            || BarCodeStatusEnum.EXITED.code == it.getStatus()
                            || BarCodeStatusEnum.FINISH_RETURN.code == it.getStatus()
            );
            boolean hasEntry = orderBarCodeList.stream().anyMatch(it -> (BarCodeStatusEnum.ENTERED.code == it.getStatus() || BarCodeStatusEnum.WAITING_FOR_EXIT.code == it.getStatus() || BarCodeStatusEnum.EXITED.code == it.getStatus()));

            // 拆分后字段，该状态下可以进行订单验收。入库完成且订单已验收/退货完成
            boolean canReceive = orderBarCodeList.stream().allMatch(it-> {
                boolean finishInbound = TRIGGER_ACCEPT_UPDATE_INVENTORY_STATUS.contains(it.getInventoryStatus());
                boolean finishOrSkipReceiveProcess = TRIGGER_ACCEPT_UPDATE_TRANSACTION_STATUS.contains(it.getTransactionStatus());
                return finishInbound || finishOrSkipReceiveProcess;
            });
            if (!(canReceive || (isOver && hasEntry))) {
                return;
            }
            // 这里取入库提交人的guid反找提交人的信息
            UserBaseInfoDTO userInfo = guidIdentityMap.get(orderNoEntryContactGuidMap.get(orderNo));
            if (userInfo == null) {
                LOGGER.error("入库完成自动收货失败, 查无用户信息, 订单号:{}, 入库提交人guid:{}", orderNo, orderNoEntryContactGuidMap.get(orderNo));
                return;
            }
            OrderReceiptParamDTO params = new OrderReceiptParamDTO();
            params.setOrderId(order.getId());
            params.setOrgId(order.getFuserid());
            params.setOrgCode(order.getFusercode());
            params.setUserId(userInfo.getId());
            params.setUserName(userInfo.getName());
            params.setDepartmentIdList(Collections.singletonList(order.getFbuydepartmentid()));
            params.setUserGuid(userInfo.getGuid());
            params.setInventoryStatus(InventoryStatusEnum.COMPLETE.getCode());

            Transaction transaction = Cat.newTransaction(CAT_TYPE, "orderAccept", true);
            try {
                orderAcceptRPCClient.orderAccept(params);
                transaction.setSuccessStatus();
            } catch (Exception e) {
                LOGGER.error("订单" + orderNo + "入库自动收货失败:" + e.getMessage());
                transaction.setStatus(e);
                transaction.addData(CatUtils.buildStackInfo("订单" + orderNo + "入库自动收货失败:", e));
            } finally {
                transaction.complete();
            }
        });

    }

    @Override
    @ServiceLog(description = "订单生成二维码--单位一物一码专用", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> generatedBarCodeByOrder(OrderBaseParamDTO request) {
        Long orderId = request.getOrderId();
        Preconditions.notNull(orderId, "error, orderId must not be null");

        OrderMasterDTO orderMaster = orderMasterRPCClient.findById(orderId.intValue());
        String orderNo = orderMaster.getForderno();
        List<OrderDetailDTO> allOrderDetailList = orderDetailRPCClient.findByMasterId(orderId.intValue());

        // 初始交易状态，待收货的订单对应待收货（线下单），否则都是待发货。注:基于订单生成一物一码的节点只有四个：线上单生成/确认订单时，订单拆单，线下单生成（此时为待收货）
        final Integer initTransactionStatus = OrderStatusEnum.WaitingForReceive.getValue().equals(orderMaster.getStatus()) ? OrderProductTransactionStatusEnum.WAITING_FOR_RECEIVE.getCode() : OrderProductTransactionStatusEnum.WAITING_FOR_DELIVERY.getCode();
        // 原子操作
        Runnable task = () -> {
            redisClient.getLock(orderNo, 5);
            int count = orderUniqueBarCodeService.findExistedByOrderNo(orderNo);
            if (count > 0) {
                // 如果存在，则跳过生成
                return;
            }
            allOrderDetailList.forEach(orderDetail -> {
                // 单种商品的购买数量
                int quantityTotal = Optional.ofNullable(orderDetail.getFquantity()).map(BigDecimal::intValue).orElse(0);
                if (quantityTotal == 0) {
                    return;
                }

                // 订单商品计件，生成二维码
                while (quantityTotal > 0) {
                    int quantity = Math.min(quantityTotal, 1000);
                    Set<Long> barCodeCollect = new HashSet<>(IdGeneratorUtils.getBarCodeList(quantity));
                    List<OrderUniqueBarCodeDTO> list = new ArrayList<>(barCodeCollect.size());

                    for (int i = 0; i < quantity; i++) {
                        OrderUniqueBarCodeDTO barCodeDO = OrderUniqueBarCodeTranslator.orderWrapperUniqueBarCode(orderMaster, orderDetail);
                        barCodeDO.setType(UniqueBarCodeTypeEnum.ORG.getCode());
                        barCodeDO.setStatus(BarCodeStatusEnum.UN_INPUT.code);
                        barCodeDO.setTransactionStatus(initTransactionStatus);
                        Long next = barCodeCollect.iterator().next();
                        barCodeDO.setUniBarCode(next.toString());
                        barCodeCollect.remove(next);
                        list.add(barCodeDO);
                    }

                    List<List<OrderUniqueBarCodeDTO>> partition = Lists.partition(list, 100);
                    for (List<OrderUniqueBarCodeDTO> itemList : partition) {
                        orderUniqueBarCodeService.batchInsert(itemList);
                    }
                    quantityTotal -= 1000;
                }
            });
            // 同步一物一码信息到库房
            syncQrCode2Warehouse(orderMaster);
            redisClient.unlock(orderNo);
        };
        AsyncExecutor.listenableRunAsync(task).addFailureCallback(ex -> LOGGER.error("订单:" + orderNo + "生成二维码失败 =>" + ex));

        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }


    /**
     * 同步一物一码信息到库房
     */
    private void syncQrCode2Warehouse(OrderMasterDTO orderMasterDTO) {
        String orderNo = orderMasterDTO.getForderno();
        Integer orderId = orderMasterDTO.getId();

        List<OrderExtraDO> orderExtraDOList = orderExtraMapper.selectByOrderIdAndExtraKey(orderId, OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        if (CollectionUtils.isEmpty(orderExtraDOList)) {
            return;
        }
        String trueStr = "1";
        String extraValue = orderExtraDOList.get(0).getExtraValue();
        // 非一物一码订单，跳过
        if (!trueStr.equals(extraValue)) {
            return;
        }

        List<OrderUniqueBarCodeDTO> uniqueBarCodeDTOS = orderUniqueBarCodeService.findByOrderNo(orderNo, New.list(UniqueBarCodeTypeEnum.ORG.getCode()));
        if (CollectionUtils.isEmpty(uniqueBarCodeDTOS)) {
            LOGGER.error("同步二维码到库房失败，{}没有找到对应的一物一码信息！", orderNo);
            return;
        }
        BizWarehouseQrCodeDangerousOccupyDTO occupyDTO = new BizWarehouseQrCodeDangerousOccupyDTO();
        BizWarehouseQrCodeDangerousOccupyDetailDTO detailDTO = new BizWarehouseQrCodeDangerousOccupyDetailDTO();
        List<String> qrCodeList = uniqueBarCodeDTOS.stream().map(OrderUniqueBarCodeDTO::getUniBarCode).collect(Collectors.toList());
        detailDTO.setOrderNo(orderNo);
        detailDTO.setQrCodeList(qrCodeList);
        occupyDTO.setDetailDTOList(New.list(detailDTO));
        bizWarehouseRoomServiceClient.syncQrCodeDetailList(occupyDTO);
    }

    @Override
    @ServiceLog(description = "写入单位条形码--已经生成好的，不需要供应商填写", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> insertOrgBarCode(List<OrderUniqueBarCodeDTO> request) {
        if(CollectionUtils.isEmpty(request)){
            return RemoteResponse.success();
        }
        request.forEach(item-> item.setType(UniqueBarCodeTypeEnum.ORG.getCode()));
        this.insertBarCode(request);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "写入中爆条形码--已经生成好的，不需要供应商填写", operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> insertCbsdBarCode(List<OrderUniqueBarCodeDTO> request) {
        if(CollectionUtils.isEmpty(request)){
            return RemoteResponse.success();
        }
        request.forEach(item-> item.setType(UniqueBarCodeTypeEnum.CHINA_BLAST.getCode()));
        this.insertBarCode(request);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> insertWarehouseBarcode(List<OrderUniqueBarCodeDTO> request) {
        if(CollectionUtils.isEmpty(request)){
            return RemoteResponse.success();
        }
        request.forEach(item->{
            // 库房导入商品的一物一码，与订单无关联，关联订单字段设置为空
            item.setOrderNo(StringUtils.EMPTY);
            item.setOrderDetailId(NULL_DETAIL_ID);
            item.setType(UniqueBarCodeTypeEnum.WAREHOUSE.getCode());
            item.setStatus(BarCodeStatusEnum.UN_INPUT.code);
        });
        orderUniqueBarCodeService.batchInsert(request);
        return RemoteResponse.success(true);
    }

    @Override
    @ServiceLog(description = "查询订单批次码录入统计信息")
    public RemoteResponse<List<OrderUniqueBarCodeStatisticsDTO>> findBarCodeStatisticsByOrderNo(OrderDetailBatchesRequestDTO request) {
        String orderNo = request.getOrderNo();
        BusinessErrUtil.notNull(orderNo, "orderNo must not be null");
        BusinessErrUtil.notEmpty(request.getTypeList(), "类型不可空");

        // 获取所有的购买数
        List<OrderUniqueBarCodeDTO> orderUniqueBarCodeList = orderUniqueBarCodeService.findByOrderNo(orderNo, request.getTypeList());
        List<OrderUniqueBarCodeStatisticsDTO> result = new ArrayList<>();
        // detailId => total
        Map<Integer, Long> detailIdTotalMap = orderUniqueBarCodeList.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.counting()));
        // detailId => total of more than 0
        Map<Integer, Long> detailIdHasInputMap = orderUniqueBarCodeList.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.summingLong(it -> it.getBatchesStatus() == OrderProductBatchesStatusEnum.INPUTTED.getCode() ? 1 : 0)));
        // detailId => bar code item
        Map<Integer, OrderUniqueBarCodeDTO> detailIdProductNameMap = orderUniqueBarCodeList.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.collectingAndThen(Collectors.toList(), it -> it.get(0))));
        // detailId => total of printed
        Map<Integer, Long> detailIdHasPrintedMap = orderUniqueBarCodeList.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.summingLong(it -> BarCodePrintedEnum.PRINTED.code == it.getPrinted() ? 1 : 0)));

        detailIdProductNameMap.forEach((detailId, codeDTO) -> {
            OrderUniqueBarCodeStatisticsDTO item = new OrderUniqueBarCodeStatisticsDTO();
            item.setDetailId(detailId);
            item.setProductName(codeDTO.getProductName());
            item.setBrand(codeDTO.getBrand());
            item.setSpec(codeDTO.getSpec());
            item.setHasInputTotal(Optional.ofNullable(detailIdHasInputMap.get(detailId)).map(Long::intValue).orElse(0));
            item.setTotal(Optional.ofNullable(detailIdTotalMap.get(detailId)).map(Long::intValue).orElse(0));
            item.setPrintedTotal(Optional.ofNullable(detailIdHasPrintedMap.get(detailId)).map(Long::intValue).orElse(0));

            result.add(item);
        });
        // 统计已录入批次的
        return RemoteResponse.<List<OrderUniqueBarCodeStatisticsDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "查询商品批次码录入统计信息")
    public RemoteResponse<OrderUniqueBarCodeStatisticsDTO> findBarCodeStatisticsByDetailId(OrderDetailBatchesRequestDTO request) {
        Integer detailId = request.getDetailId();
        Preconditions.notNull(detailId, "detailId must not be null");
        BusinessErrUtil.notEmpty(request.getTypeList(), "类型不可空");

        List<OrderUniqueBarCodeDTO> list = orderUniqueBarCodeService.findByDetailId(detailId, request.getTypeList());
        // detailId => total
        Map<Integer, Long> detailIdTotalMap = list.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.counting()));
        // detailId => total of more than 0
        Map<Integer, Long> detailIdHasInputMap = list.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.summingLong(it -> it.getStatus() > BarCodeStatusEnum.UN_INPUT.code ? 1 : 0)));
        // detailId => total of printed
        Map<Integer, Long> detailIdHasPrintedMap = list.stream().collect(Collectors.groupingBy(OrderUniqueBarCodeDTO::getOrderDetailId, Collectors.summingLong(it -> BarCodePrintedEnum.PRINTED.code == it.getPrinted() ? 1 : 0)));

        OrderUniqueBarCodeStatisticsDTO item = new OrderUniqueBarCodeStatisticsDTO();
        item.setDetailId(detailId);
        item.setProductName(Optional.ofNullable(list.get(0)).map(OrderUniqueBarCodeDTO::getProductName).orElse(StringUtils.EMPTY));
        item.setBrand(Optional.ofNullable(list.get(0)).map(OrderUniqueBarCodeDTO::getBrand).orElse(StringUtils.EMPTY));
        item.setSpec(Optional.ofNullable(list.get(0)).map(OrderUniqueBarCodeDTO::getSpec).orElse(StringUtils.EMPTY));
        item.setHasInputTotal(Optional.ofNullable(detailIdHasInputMap.get(detailId)).map(Long::intValue).orElse(0));
        item.setTotal(Optional.ofNullable(detailIdTotalMap.get(detailId)).map(Long::intValue).orElse(0));
        item.setPrintedTotal(Optional.ofNullable(detailIdHasPrintedMap.get(detailId)).map(Long::intValue).orElse(0));

        return RemoteResponse.<OrderUniqueBarCodeStatisticsDTO>custom().setSuccess().setData(item);
    }

    @Override
    @ServiceLog(description = "订单号查询订单明细批次(含二维码)")
    public RemoteResponse<List<OrderUniqueBarCodeDTO>> findByOrderNo(OrderDetailBatchesRequestDTO request) {
        // validate params
        String orderNo = request.getOrderNo();
        List<String> orderNoList = request.getOrderNoList();
        BusinessErrUtil.isTrue(orderNo != null || CollectionUtils.isNotEmpty(orderNoList), "必须传入orderNoList");
        if(orderNoList == null){
            orderNoList = New.list(orderNo);
        }
        if(orderNo != null){
            orderNoList.add(orderNo);
        }

        if(orderNoList.contains("DJ2024082801327901")){
            return RemoteResponse.success(New.emptyList());
        }

        List<Integer> typeList = CollectionUtils.isEmpty(request.getTypeList()) ? DEFAULT_TYPE : request.getTypeList();
        return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(orderUniqueBarCodeService.findByOrderNoList(orderNoList, typeList));
    }

    @Override
    @ServiceLog(description = "根据barCode批量查询批次信息, barCodeList必填")
    public RemoteResponse<List<OrderUniqueBarCodeDTO>> findByBarCodeList(OrderDetailBatchesRequestDTO request) {
        List<String> barCodeList = this.getBarCodeList(request);
        Preconditions.notEmpty(barCodeList, "barCodeList/uniBarCodeList must not be empty");
        Preconditions.isTrue(barCodeList.size() <= 200, "number of barCodeList must be less than 200");
        return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(orderUniqueBarCodeService.findByBarCodeList(barCodeList));
    }

    @Override
    @ServiceLog(description = "根据业务单号查询批次信息")
    public RemoteResponse<List<OrderUniqueBarCodeDTO>> findByBusinessNo(OrderDetailBatchesRequestDTO request) {
        String entryNo = request.getEntryNo();
        String applyNo = request.getApplyNo();
        String exitNo = request.getExitNo();
        String returnNo = request.getReturnNo();
        String orderNo = request.getOrderNo();

        List<Integer> typeList = CollectionUtils.isEmpty(request.getTypeList()) ? DEFAULT_TYPE : request.getTypeList();

        if (entryNo == null && applyNo == null && exitNo == null && returnNo == null && orderNo == null) {
            throw new IllegalStateException("orderNo, entryNo, applyNo, exitNo, returnNo其一必填!");
        }
        if (orderNo != null) {
            return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(orderUniqueBarCodeService.findByOrderNo(orderNo, typeList));
        }
        if (entryNo != null) {
            return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(orderUniqueBarCodeService.findByEntryNo(entryNo, typeList));
        }
        if (applyNo != null) {
            return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(orderUniqueBarCodeService.findByApplyNo(applyNo, typeList));
        }
        if (exitNo != null) {
            return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(orderUniqueBarCodeService.findByExitNo(exitNo, typeList));
        }
        if (returnNo != null) {
            return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(orderUniqueBarCodeService.findByReturnNo(returnNo, typeList));
        }
        return RemoteResponse.<List<OrderUniqueBarCodeDTO>>custom().setSuccess().setData(Collections.emptyList());
    }

    @Override
    @ServiceLog(description = "根据业务单号更新对应批次信息", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> compareAndSetByBusinessNoAndStatus(OrderDetailBatchesRequestDTO request) {
        String entryNo = request.getEntryNo();
        String applyNo = request.getApplyNo();
        String exitNo = request.getExitNo();
        String returnNo = request.getReturnNo();
        String orderNo = request.getOrderNo();
        Integer updatedStatus = request.getUpdatedStatus();
        Integer expectStatus = request.getExpectStatus();
        Integer valid = request.getValid();

        List<Integer> typeList = CollectionUtils.isEmpty(request.getTypeList()) ? DEFAULT_TYPE : request.getTypeList();
        if (entryNo == null && applyNo == null && exitNo == null && returnNo == null && orderNo == null) {
            throw new IllegalStateException("orderNo, entryNo, applyNo, exitNo, returnNo其一必填!");
        }
        if (updatedStatus == null || expectStatus == null) {
            throw new IllegalStateException("updatedStatus, expectStatus必填!");
        }

        if (orderNo != null) {
            return RemoteResponse.<Integer>custom().setSuccess().setData(orderUniqueBarCodeService.compareAndSetByOrderNoAndStatus(orderNo, updatedStatus, expectStatus, valid, typeList));
        }
        if (entryNo != null) {
            int affect = orderUniqueBarCodeService.compareAndSetByEntryNoAndStatus(entryNo, exitNo, updatedStatus, expectStatus, valid, typeList);
            if (affect == 0 || BarCodeStatusEnum.ENTERED.code != updatedStatus) {
                return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
            }
            // 单位一物一码执行完毕处理逻辑
            List<OrderUniqueBarCodeDTO> byEntryNo = orderUniqueBarCodeService.findByEntryNo(entryNo, DEFAULT_TYPE);
            if(CollectionUtils.isNotEmpty(byEntryNo)){
                List<String> orderNoList = byEntryNo.stream().map(OrderUniqueBarCodeDTO::getOrderNo).distinct().collect(Collectors.toList());
                List<OrderUniqueBarCodeDTO> byOrderNoList = orderUniqueBarCodeService.findByOrderNoList(orderNoList, DEFAULT_TYPE);
                Runnable acceptOrderTask = () -> orderAccept(byOrderNoList);
                AsyncExecutor.listenableRunAsync(acceptOrderTask);
            }
            return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
        }
        if (applyNo != null) {
            return RemoteResponse.<Integer>custom().setSuccess().setData(orderUniqueBarCodeService.compareAndSetByApplyNoAndStatus(applyNo, updatedStatus, expectStatus, valid, typeList));
        }
        if (exitNo != null) {
            return RemoteResponse.<Integer>custom().setSuccess().setData(orderUniqueBarCodeService.compareAndSetByExitNoAndStatus(exitNo, updatedStatus, expectStatus, valid, typeList));
        }
        if (returnNo != null) {
            int affect = orderUniqueBarCodeService.compareAndSetByReturnNoAndStatus(returnNo, updatedStatus, expectStatus, valid, typeList);
            if (affect == 0 || BarCodeStatusEnum.FINISH_RETURN.code != updatedStatus) {
                return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
            }
            // 单位一物一码执行完毕处理逻辑
            List<OrderUniqueBarCodeDTO> byReturnNo = orderUniqueBarCodeService.findByReturnNo(returnNo, DEFAULT_TYPE);
            if(CollectionUtils.isNotEmpty(byReturnNo)){
                List<String> orderNoList = byReturnNo.stream().map(OrderUniqueBarCodeDTO::getOrderNo).distinct().collect(Collectors.toList());
                List<OrderUniqueBarCodeDTO> byOrderNoList = orderUniqueBarCodeService.findByOrderNoList(orderNoList, DEFAULT_TYPE);
                Runnable acceptOrderTask = () -> orderAccept(byOrderNoList);
                AsyncExecutor.listenableRunAsync(acceptOrderTask);
            }
            return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(0);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "根据业务号更新状态")
    public RemoteResponse<Integer> updateStatusByBusinessNo(OrderDetailBatchesRequestDTO request) {
        String entryNo = request.getEntryNo();
        String returnNo = request.getReturnNo();
        String orderNo = request.getOrderNo();
        List<String> uniBarCodeList = request.getUniBarCodeList();
        Integer inventoryStatus = request.getInventoryStatus();
        Integer transactionStatus = request.getTransactionStatus();
        Integer valid = request.getValid();

        List<Integer> typeList = CollectionUtils.isEmpty(request.getTypeList()) ? DEFAULT_TYPE : request.getTypeList();
        if (inventoryStatus == null && transactionStatus == null) {
            throw new IllegalArgumentException("没有传入需要更新的有效状态!");
        }
        int affect;
        List<OrderUniqueBarCodeDTO> involveData = New.emptyList();
        if(CollectionUtils.isNotEmpty(uniBarCodeList)){
            List<OrderUniqueBarCodeDTO> updateParams = uniBarCodeList.stream().map(uniBarCode->{
                OrderUniqueBarCodeDTO updateParam = new OrderUniqueBarCodeDTO();
                updateParam.setUniBarCode(uniBarCode);
                updateParam.setInventoryStatus(inventoryStatus);
                updateParam.setTransactionStatus(transactionStatus);
                return updateParam;
            }).collect(Collectors.toList());
            affect = orderUniqueBarCodeService.batchUpdateOrderUniqueBarCode(updateParams);
            if(affect > 0){
                involveData = orderUniqueBarCodeService.findByBarCodeList(uniBarCodeList);
            }
        }else if (orderNo != null){
            OrderUniqueBarCodeDO updateParam = new OrderUniqueBarCodeDO();
            updateParam.setInventoryStatus(inventoryStatus);
            updateParam.setTransactionStatus(transactionStatus);
            updateParam.setOrderNo(orderNo);
            affect = orderUniqueBarCodeService.updateStatusByBusinessNo(updateParam, typeList);
            if(affect > 0){
                involveData = orderUniqueBarCodeService.findByOrderNo(orderNo, typeList);
            }
        } else if (entryNo != null){
            involveData = orderUniqueBarCodeService.findByEntryNo(entryNo, typeList);
            if(CollectionUtils.isEmpty(involveData)){
                return RemoteResponse.success(0);
            }
            List<OrderUniqueBarCodeDTO> updateParamList = involveData.stream().map(item->{
                OrderUniqueBarCodeDTO updateParam = new OrderUniqueBarCodeDTO();
                boolean completeWarehouse = inventoryStatus != null && OrderProductInventoryStatusEnum.COMPLETE_INBOUND.getCode() == inventoryStatus;
                if (completeWarehouse && transactionStatus == null && OrderProductTransactionStatusEnum.WAITING_FOR_RECEIVE.getCode() == item.getTransactionStatus()) {
                    // 入库完成，且没有指定更新交易状态的话，且当前交易状态是待验收时，把交易状态更新为已验收
                    updateParam.setTransactionStatus(OrderProductTransactionStatusEnum.RECEIVED.getCode());
                }else {
                    updateParam.setTransactionStatus(transactionStatus);
                }
                updateParam.setInventoryStatus(inventoryStatus);
                updateParam.setUniBarCode(item.getUniBarCode());
                return updateParam;
            }).collect(Collectors.toList());
            affect = orderUniqueBarCodeService.batchUpdateOrderUniqueBarCode(updateParamList);
        } else if (returnNo != null){
            OrderUniqueBarCodeDO updateParam = new OrderUniqueBarCodeDO();
            updateParam.setInventoryStatus(inventoryStatus);
            updateParam.setTransactionStatus(transactionStatus);
            updateParam.setReturnNo(returnNo);
            affect = orderUniqueBarCodeService.updateStatusByBusinessNo(updateParam, typeList);
            if(affect > 0){
                involveData = orderUniqueBarCodeService.findByReturnNo(returnNo, typeList);
            }
        }else {
            throw new IllegalArgumentException("没有传入进行更新的有效业务号！");
        }

        boolean inventoryStatusTriggerAccept = inventoryStatus != null && TRIGGER_ACCEPT_UPDATE_INVENTORY_STATUS.contains(inventoryStatus);
        boolean transactionStatusTriggerAccept = transactionStatus != null && TRIGGER_ACCEPT_UPDATE_TRANSACTION_STATUS.contains(transactionStatus);
        if(affect > 0 && (inventoryStatusTriggerAccept || transactionStatusTriggerAccept)){
            List<OrderUniqueBarCodeDTO> finalInvolveData = involveData;
            Runnable acceptOrderTask = () -> orderAccept(finalInvolveData);
            AsyncExecutor.listenableRunAsync(acceptOrderTask);
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    @ServiceLog(description = "按订单号删除一物一码")
    public RemoteResponse<Integer> deleteByOrderNo(OrderDetailBatchesRequestDTO request) {
        String orderNo = request.getOrderNo();
        Preconditions.notNull(orderNo, "orderNo must not be null");
        List<Integer> typeList = CollectionUtils.isEmpty(request.getTypeList()) ? DEFAULT_TYPE : request.getTypeList();
        int affect = orderUniqueBarCodeService.deleteByOrderNo(orderNo, typeList);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    @ServiceLog(description = "分页查询")
    public PageableResponse<List<OrderUniqueBarCodeDTO>> queryPage(OrderQRCodePageRequestDTO request) {
        return orderUniqueBarCodeService.queryPage(request);
    }

    @Override
    public RemoteResponse<List<OrderUniqueBarCodeDTO>> pageByBarcode(String orderNo, String startBarcode, Integer limit) {
        return RemoteResponse.success(orderUniqueBarCodeService.pageByBarcode(orderNo, startBarcode, limit));
    }

    private String getBarCode(OrderDetailBatchesRequestDTO requestDTO){
        return requestDTO.getUniBarCode();
    }

    private List<String> getBarCodeList(OrderDetailBatchesRequestDTO requestDTO){
        return requestDTO.getUniBarCodeList();
    }

    /**
     * 插入外部已经生成好的条形码数据
     * @param request 数据
     */
    private void insertBarCode(List<OrderUniqueBarCodeDTO> request){
        boolean nullField = request.stream().anyMatch(item->item.getOrderDetailId() == null || item.getUniBarCode() == null);
        BusinessErrUtil.isTrue(!nullField, "订单详情id/条形码不可空");

        List<Integer> detailIdList = request.stream().map(OrderUniqueBarCodeDTO::getOrderDetailId).collect(Collectors.toList());
        List<OrderDetailDTO> detailDTOList = orderDetailRPCClient.findByIdList(detailIdList);
        Map<Integer, OrderDetailDTO> detailIdIdentityMap = DictionaryUtils.toMap(detailDTOList, OrderDetailDTO::getId, Function.identity());

        List<Integer> orderIdList = detailDTOList.stream().map(OrderDetailDTO::getFmasterid).collect(Collectors.toList());
        List<OrderMasterDTO> orderMasterList = orderMasterRPCClient.findByIdList(orderIdList);
        Map<Integer, OrderMasterDTO> orderIdMasterMap = DictionaryUtils.toMap(orderMasterList, OrderMasterDTO::getId, Function.identity());

        List<OrderUniqueBarCodeDTO> barCodeList = request.stream().map(orderUniqueBarCodeDTO -> {
            OrderDetailDTO orderDetail = detailIdIdentityMap.get(orderUniqueBarCodeDTO.getOrderDetailId());
            OrderMasterDTO orderMaster = orderIdMasterMap.get(orderDetail.getFmasterid());
            OrderUniqueBarCodeDTO barCode = OrderUniqueBarCodeTranslator.orderWrapperUniqueBarCode(orderMaster, orderDetail);
            barCode.setType(orderUniqueBarCodeDTO.getType());
            // 初始化为可修改打印状态
            barCode.setStatus(BarCodeStatusEnum.WAITING_FOR_DELIVERY.code);
            barCode.setBatchesStatus(OrderProductBatchesStatusEnum.INPUTTED.getCode());
            barCode.setUniBarCode(orderUniqueBarCodeDTO.getUniBarCode());
            return barCode;
        }).collect(Collectors.toList());

        List<List<OrderUniqueBarCodeDTO>> partition = Lists.partition(barCodeList, 100);
        for (List<OrderUniqueBarCodeDTO> itemList : partition) {
            orderUniqueBarCodeService.batchInsert(itemList);
        }
    }
}
