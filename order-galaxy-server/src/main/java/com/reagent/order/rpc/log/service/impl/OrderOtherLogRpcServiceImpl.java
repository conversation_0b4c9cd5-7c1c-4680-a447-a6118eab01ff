package com.reagent.order.rpc.log.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.reagent.order.base.log.dto.OrderDockingLogDTO;
import com.reagent.order.base.log.mapper.OrderDockingLogMapper;
import com.reagent.order.base.log.model.OrderDockingLog;
import com.reagent.order.base.log.service.OrderOtherLogRpcService;
import com.reagent.order.base.log.translator.OrderLogTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description: 订单相关其他日志接口
 * @author: zhuk
 * @create: 2019-11-15 17:07
 **/
@MSharpService
public class OrderOtherLogRpcServiceImpl implements OrderOtherLogRpcService {

    @Autowired
    private OrderDockingLogMapper orderDockingLogMapper;

    @Override
    @ServiceLog
    public RemoteResponse insertOrderDockingLog(OrderDockingLogDTO orderDockingLogDTO) {
            OrderDockingLog orderDockingLog = OrderLogTranslator.dto2OrderDockingLog(orderDockingLogDTO);
            int result =  orderDockingLogMapper.insertSelective(orderDockingLog);
            return RemoteResponse.custom().setSuccess().setData(result).build();
    }
}
