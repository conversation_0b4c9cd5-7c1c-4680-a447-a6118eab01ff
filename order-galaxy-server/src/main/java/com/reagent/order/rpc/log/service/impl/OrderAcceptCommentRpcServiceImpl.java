package com.reagent.order.rpc.log.service.impl;

import com.reagent.order.base.order.dto.OrderAcceptCommentDTO;
import com.reagent.order.base.order.dto.OrderAcceptQueryDTO;
import com.reagent.order.base.order.mapper.OrderAcceptCommentMapper;
import com.reagent.order.base.order.model.OrderAcceptCommentDO;
import com.reagent.order.base.order.service.OrderAcceptCommentRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/2/25 16:02
 * @Description
 **/
@MSharpService
@CatAnnotation
public class OrderAcceptCommentRpcServiceImpl implements OrderAcceptCommentRpcService {

    @Resource
    private OrderAcceptCommentMapper orderAcceptCommentMapper;

    private final static String CAT_TYPE = "OrderAcceptCommentRpcService";

    /**
     * @param orderAcceptCommentDTO
     * @return com.ruijing.fundamental.api.remote.RemoteResponse<java.lang.Boolean>
     * @description: 保存订单验收评价，传入orgid, orderid, 评价id列表
     * @date: 2021/2/25 15:59
     * @author: zengyanru
     */
    @Override
    public RemoteResponse<Boolean> saveOrderComment(OrderAcceptCommentDTO orderAcceptCommentDTO) {
        Preconditions.notNull(orderAcceptCommentDTO, "保存订单验收评价rpc方法saveOrderComment入参不可为空");
        Preconditions.notEmpty(orderAcceptCommentDTO.getAcceptCommentTagList(), "保存订单验收评价rpc方法saveOrderComment 评价列表不可为空");
        Preconditions.notNull(orderAcceptCommentDTO.getOrderId(), "保存订单验收评价rpc方法saveOrderComment 订单id不可为空");
        Preconditions.notNull(orderAcceptCommentDTO.getOrgId(), "保存订单验收评价rpc方法saveOrderComment 单位idid不可为空");
        OrderAcceptCommentDO orderAcceptCommentDO = new OrderAcceptCommentDO();
        orderAcceptCommentDO.setOrderId(orderAcceptCommentDTO.getOrderId());
        orderAcceptCommentDO.setOrgId(orderAcceptCommentDTO.getOrgId());
        List<String> commentTagList = orderAcceptCommentDTO.getAcceptCommentTagList().stream().map(String::valueOf).collect(Collectors.toList());
        String commentTag = String.join(";", commentTagList);
        orderAcceptCommentDO.setAcceptCommentTags(commentTag);
        try {
            // 保证一个订单只有一条评价记录
            orderAcceptCommentMapper.deleteByOrderId(orderAcceptCommentDTO.getOrderId());
            orderAcceptCommentMapper.insertSelective(orderAcceptCommentDO);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "saveOrderComment", e.getMessage(), e);
            return RemoteResponse.<Boolean>custom().setData(false).setFailure(e.getMessage());
        }
        return RemoteResponse.<Boolean>custom().setData(true).setSuccess();
    }

    /**
     * @param input
     * @return com.ruijing.fundamental.api.remote.RemoteResponse<com.reagent.order.base.order.dto.OrderAcceptCommentDTO>
     * @description: 获取订单验收评价，传入订单orderid，orgid
     * @date: 2021/2/25 15:59
     * @author: zengyanru
     */
    @Override
    public RemoteResponse<List<OrderAcceptCommentDTO>> getOrderComment(OrderAcceptQueryDTO input) {
        Preconditions.notNull(input, "获取订单验收评价rpc方法getOrderComment入参不可为空");
        List<Integer> orderIdList = input.getOrderIdList();
        Integer orgId = input.getOrgId();
        Preconditions.notEmpty(orderIdList, "获取订单验收评价rpc方法getOrderComment入参orderId不可为空");
        List<OrderAcceptCommentDO> orderAcceptCommentList = orderAcceptCommentMapper.findByOrderIdInAndOrgId(orderIdList, orgId);
        if (CollectionUtils.isEmpty(orderAcceptCommentList)) {
            return RemoteResponse.<List<OrderAcceptCommentDTO>>custom().setData(New.list()).setSuccess();
        }

        List<OrderAcceptCommentDTO> outputList = New.listWithCapacity(orderAcceptCommentList.size());
        for (OrderAcceptCommentDO orderAcceptComment : orderAcceptCommentList) {
            outputList.add(this.orderAcceptCommentObjectConverter(orderAcceptComment));
        }

        return RemoteResponse.<List<OrderAcceptCommentDTO>>custom().setData(outputList).setSuccess();
    }

    /**
     * @description: 订单验收评价对象转换器
     * @date: 2021/2/26 9:56
     * @author: zengyanru
     * @param input
     * @return com.reagent.order.base.order.dto.OrderAcceptCommentDTO
     */
    private OrderAcceptCommentDTO orderAcceptCommentObjectConverter(OrderAcceptCommentDO input) {
        OrderAcceptCommentDTO output = new OrderAcceptCommentDTO();
        if (input == null) {
            return output;
        }
        // 处理从表中得来的comment组装
        String acceptCommentTags = input.getAcceptCommentTags();
        String[] acceptCommentTagArrays = acceptCommentTags.split(";");
        List<Integer> commentTagList = New.list(acceptCommentTagArrays).stream().map(Integer::valueOf).collect(Collectors.toList());
        output.setAcceptCommentTagList(commentTagList).setOrderId(input.getOrderId()).setOrgId(input.getOrgId());
        return output;
    }
}
