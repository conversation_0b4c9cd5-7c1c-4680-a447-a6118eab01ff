package com.reagent.order.rpc.orderextra;

import com.reagent.order.base.order.dto.OrderBankAccountSnapshotDTO;
import com.reagent.order.base.order.mapper.OrderBankAccountSnapshotMapper;
import com.reagent.order.base.order.model.OrderBankAccountSnapshotDO;
import com.reagent.order.base.order.service.OrderBankAccountSnapshotRpcService;
import com.reagent.order.base.order.translator.OrderBankAccountSnapshotTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @CreateTime 2023-11-30 09:31
 * @Description
 */
@MSharpService
public class OrderBankAccountSnapshotRpcServiceImpl implements OrderBankAccountSnapshotRpcService {

    @Resource
    private OrderBankAccountSnapshotMapper orderBankAccountSnapshotMapper;

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> insertList(List<OrderBankAccountSnapshotDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return RemoteResponse.success();
        }
        List<OrderBankAccountSnapshotDO> doList = list.stream().map(OrderBankAccountSnapshotTranslator::dto2do).collect(Collectors.toList());
        orderBankAccountSnapshotMapper.insertList(doList);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> updateList(List<OrderBankAccountSnapshotDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return RemoteResponse.success();
        }
        List<OrderBankAccountSnapshotDO> doList = list.stream().map(OrderBankAccountSnapshotTranslator::dto2do).collect(Collectors.toList());
        orderBankAccountSnapshotMapper.updateList(doList);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> saveList(List<OrderBankAccountSnapshotDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            return RemoteResponse.success();
        }
        List<Integer> orderIdList = list.stream().map(OrderBankAccountSnapshotDTO::getOrderId).collect(Collectors.toList());
        List<OrderBankAccountSnapshotDO> existList = orderBankAccountSnapshotMapper.selectByOrderIdList(orderIdList);
        List<Integer> existOrderIdList = existList.stream().map(OrderBankAccountSnapshotDO::getOrderId).collect(Collectors.toList());
        List<OrderBankAccountSnapshotDO> toInsertList = new ArrayList<>(list.size());
        List<OrderBankAccountSnapshotDO> toUpdateList = new ArrayList<>(list.size());
        for(OrderBankAccountSnapshotDTO dto : list){
            if(existOrderIdList.contains(dto.getOrderId())){
                toUpdateList.add(OrderBankAccountSnapshotTranslator.dto2do(dto));
            }else {
                toInsertList.add(OrderBankAccountSnapshotTranslator.dto2do(dto));
            }
        }
        if(CollectionUtils.isNotEmpty(toInsertList)){
            orderBankAccountSnapshotMapper.insertList(toInsertList);
        }
        if(CollectionUtils.isNotEmpty(toUpdateList)){
            orderBankAccountSnapshotMapper.updateList(toUpdateList);
        }
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(operationType = OperationType.READ)
    public RemoteResponse<List<OrderBankAccountSnapshotDTO>> listByOrderId(List<Integer> orderIdList) {
        if(CollectionUtils.isEmpty(orderIdList)){
            return RemoteResponse.success(New.emptyList());
        }
        BusinessErrUtil.isTrue(orderIdList.size() <= 200, "一次查最多200个订单的数据");
        List<OrderBankAccountSnapshotDO> doList = orderBankAccountSnapshotMapper.selectByOrderIdList(orderIdList);
        if(CollectionUtils.isEmpty(doList)){
            return RemoteResponse.success(New.emptyList());
        }
        return RemoteResponse.success(doList.stream().map(OrderBankAccountSnapshotTranslator::do2dto).collect(Collectors.toList()));
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> deleteByOrderIdList(List<Integer> orderIdList) {
        if(CollectionUtils.isEmpty(orderIdList)){
            return RemoteResponse.success();
        }
        BusinessErrUtil.isTrue(orderIdList.size() <= 200, "一次删除最多200个订单的关联数据");
        int count = orderBankAccountSnapshotMapper.deleteByOrderIdList(orderIdList);
        return RemoteResponse.success(count > 0);
    }
}
