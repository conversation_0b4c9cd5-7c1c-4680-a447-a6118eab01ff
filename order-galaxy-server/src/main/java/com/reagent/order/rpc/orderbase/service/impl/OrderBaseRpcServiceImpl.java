package com.reagent.order.rpc.orderbase.service.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.reagent.order.base.order.dto.*;
import com.reagent.order.base.order.model.OrderBase;
import com.reagent.order.base.order.service.OrderBaseRpcService;
import com.reagent.order.base.order.service.OrderBaseService;
import com.reagent.order.base.order.translator.OrderBaseTranslator;
import com.reagent.order.base.rentcar.dto.BuyerRentcarOrderBaseParamDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: RPC接口实现
 * @author: zhukai
 * @create: 2019-07-30 15:12
 **/
@MSharpService
@CatAnnotation
public class OrderBaseRpcServiceImpl implements OrderBaseRpcService {

    private static final String CAT_TYPE = "RentcarOrderServiceImpl";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OrderBaseService orderBaseService;

    /**
     * 根据订单号查询 orderBase
     * @param paramDTO 入参
     * @return 订单信息
     */
    @Override
    public RemoteResponse<OrderBaseDTO> findOrderBaseByOrderNumber(OrderBaseParamDTO paramDTO){
        String paramJson = JsonUtils.toJson(paramDTO);
        try {
            logger.info("进入==》createOrderExtraQuery ，入参{}", JsonUtils.toJson(paramDTO));
            String orderNumber = paramDTO.getOrderNumber();
            if (StringUtils.isEmpty(orderNumber)){
                return RemoteResponse.<OrderBaseDTO>custom().setFailure("订单号不能为空！").build();
            }
            OrderBase orderBase = orderBaseService.findOrderBaseByOrderNumber(orderNumber);
            OrderBaseDTO orderBaseDTO = OrderBaseTranslator.orderBaseToDTO(orderBase);
            return RemoteResponse.<OrderBaseDTO>custom().setSuccess().setData(orderBaseDTO).build();
        } catch (Exception e) {
            logger.error("findOrderBaseByOrderNumber异常",e);
            Cat.logError(CAT_TYPE,"findOrderBaseByOrderNumber",paramJson,e);
            return RemoteResponse.<OrderBaseDTO>custom().setFailure("查询订单异常！").build();
        }
    }

    /**
     * 创建OrderExtraQuery
     * @param orderBaseAndExtrasDTO 入参
     * @return RemoteResponse
     */
    @Override
    public RemoteResponse createOrderBaseAndExtraQuery(OrderBaseAndExtrasDTO orderBaseAndExtrasDTO){
        logger.info("进入==》createOrderExtraQuery ，入参{}",orderBaseAndExtrasDTO.toString());
        try {
             orderBaseService.createOrderBaseAndExtraQuery(orderBaseAndExtrasDTO);
            return RemoteResponse.custom().setSuccess().build();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "createOrderExtraQuery", "createOrderExtraQuery异常", e);
            logger.error("createOrderExtraQuery异常", e);
            return RemoteResponse.custom().setFailure("createOrderExtraQuery" + e.getMessage()).build();
        }
    }

    /**
     * 根据主键 用非空值 更新订单
     *
     * @param orderBaseDTO 入参
     * @return 更新行数
     */
    @Override
    public RemoteResponse updateOrderBaseById(OrderBaseDTO orderBaseDTO){
        logger.info("进入==》updateOrderBaseById ，入参{}",orderBaseDTO.toString());
        try {
            Integer result = orderBaseService.updateOrderBaseById(orderBaseDTO);
            return RemoteResponse.custom().setSuccess().setData(result).build();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "updateOrderBaseById", "updateOrderBaseById异常", e);
            logger.error("updateOrderBaseById异常", e);
            return RemoteResponse.custom().setFailure("updateOrderBaseById" + e.getMessage()).build();
        }
    }

    /**
     * 根据用户id 和 订单状态查询订单条数
     * @param orderBaseParamDTO
     * @return
     */
    @Override
    public RemoteResponse<Integer> countByBuyerIdAndStatus(OrderBaseParamDTO orderBaseParamDTO){
        return orderBaseService.countByBuyerIdAndStatus(orderBaseParamDTO);
    }

    /**
     * 根据主键查询orderbase
     *
     * @param orderBaseParamDTO 入参
     * @return orderBaseDTO
     */
    @Override
    public RemoteResponse<OrderBaseDTO> findOrderBaseById(OrderBaseParamDTO orderBaseParamDTO) {
        return orderBaseService.findOrderBaseById(orderBaseParamDTO);
    }

    /**
     * 根据id集合查询orderBase集合
     *
     * @param orderBaseParamDTO 入参
     * @return RentcarOrderBaseResultDTO
     */
    @Override
    public RemoteResponse<OrderBaseResultDTO> findOrderBaseByIdList(OrderBaseParamDTO orderBaseParamDTO) {
        return orderBaseService.findOrderBaseByIdList(orderBaseParamDTO);
    }

    /**
     * 根据 BusinessType 生成订单id
     *
     * @return 订单号
     */
    @Override
    public RemoteResponse<Long> getOrderId(){
        return orderBaseService.getOrderId();
    }

    /**
     * 新增orderBase
     *
     * @param orderBaseDTO 入参
     * @return orderNumber 订单编号
     */
    @Override
    public RemoteResponse createOrderBase(OrderBaseDTO orderBaseDTO) {

        try {
            return orderBaseService.createOrderBase(orderBaseDTO);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "createOrderBase", "新增orderBase异常", e);
            logger.error("新增orderBase异常", e);
            return RemoteResponse.custom().setFailure("新增orderBase" + e.getMessage()).build();
        }
    }

    /**
     * 更新订单状态
     *
     * @param orderBaseParamDTO 入参
     * @return 结果
     */
    @Override
    public RemoteResponse updateOrderBaseStatus(OrderBaseParamDTO orderBaseParamDTO) {
        try {
            return orderBaseService.updateOrderBaseStatus(orderBaseParamDTO);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "updateOrderBaseStatus", "updateOrderBaseStatus异常", e);
            logger.error("updateOrderBaseStatus异常", e);
            return RemoteResponse.custom().setFailure("updateOrderBaseStatus" + e.getMessage()).build();
        }
    }

    /**
     * 根据 用户和订单状态差查询订单信息
     *
     * @param orderBaseParamDTO 用户id 订单状态
     * @return List<OrderBaseDTO>
     */
    @Override
    public RemoteResponse<List<OrderBaseDTO>> findOrderByBuyerAndStatusLis(OrderBaseParamDTO orderBaseParamDTO){

        logger.info("进入==>findOrderByBuyerAndStatusLis，入参{}", orderBaseParamDTO.toString());
        Long buyerId = orderBaseParamDTO.getBuyerId();
        List<Integer> orderStatusList = orderBaseParamDTO.getOrderStatusList();

        if(CollectionUtils.isEmpty(orderStatusList)){
            return RemoteResponse.<List<OrderBaseDTO>>custom().setFailure("findOrderByBuyerAndStatusLis入参orderStatusList不能为空！").build();
        }

        List<OrderBase> orderBaseList = null;
        try {
            orderBaseList = orderBaseService.findOrdersByStatusAndBuyerId(buyerId, orderStatusList,orderBaseParamDTO.getBusinessType(),orderBaseParamDTO.getSize());
        } catch (Exception e) {
            logger.error("findOrdersByStatusAndBuyerId异常", e);
            Cat.logError(CAT_TYPE,"findOrderByBuyerAndStatusLis","调用findOrdersByStatusAndBuyerId异常！",e);
            return RemoteResponse.<List<OrderBaseDTO>>custom().setFailure("findOrdersByStatusAndBuyerId异常" + e.getMessage()).build();
        }
        List<OrderBaseDTO> resultList = orderBaseList.stream().map(OrderBaseTranslator::orderBaseToDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderBaseDTO>>custom().setSuccess().setData(resultList).build();
    }

    /**
     *  中大 查询公示栏订单
     * @param publicityOrderParamDTO 入参
     * @return 订单集合
     */
    @Override
    public RemoteResponse<OrderBasePageResultDTO> findPublicityOrder(PublicityOrderParamDTO publicityOrderParamDTO){
        logger.info("进入==>findPublicityOrder，入参{}", publicityOrderParamDTO.toString());

        try {
            OrderBasePageResultDTO orderBasePageResultDTO = orderBaseService.findPublicityOrder(publicityOrderParamDTO);
            return RemoteResponse.<OrderBasePageResultDTO>custom().setSuccess().setData(orderBasePageResultDTO).build();
        } catch (Exception e) {
            logger.error("findPublicityOrder异常", e);
            Cat.logError(CAT_TYPE,"findPublicityOrder","调用findPublicityOrder异常！",e);
            return RemoteResponse.<OrderBasePageResultDTO>custom().setFailure("findPublicityOrder异常" + e.getMessage()).build();
        }
    }

    /**
     * 获取行程记录
     * @param buyerParamDTO 入参
     * @return RemoteResponse
     */
    @Override
    public RemoteResponse<OrderBasePageResultDTO> getTravelRecordList(BuyerRentcarOrderBaseParamDTO buyerParamDTO){
        logger.info("进入==>getTravelRecordList，入参{}", buyerParamDTO.toString());
        try {
            OrderBasePageResultDTO orderBasePageResultDTO = orderBaseService.getTravelRecordList(buyerParamDTO);
            return RemoteResponse.<OrderBasePageResultDTO>custom().setSuccess().setData(orderBasePageResultDTO).build();
        } catch (Exception e) {
            logger.error("getTravelRecordList异常", e);
            Cat.logError(CAT_TYPE,"getTravelRecordList","调用getTravelRecordList异常！",e);
            return RemoteResponse.<OrderBasePageResultDTO>custom().setFailure("getTravelRecordList异常" + e.getMessage()).build();
        }

    }

    /**
     * 根据状态和超时天数 获取超时订单
     * @param orderBaseParamDTO
     * @return
     */
    @Override
    @ServiceLog
    public RemoteResponse<OrderBasePageResultDTO> getTimeOutOrder(OrderBaseParamDTO orderBaseParamDTO){
        OrderBasePageResultDTO timeoutNoticeOrder = orderBaseService.getTimeoutNoticeOrder(orderBaseParamDTO);
        return RemoteResponse.<OrderBasePageResultDTO> custom().setData(timeoutNoticeOrder).setSuccess().build();

    }
}
