package com.reagent.order.rpc.orderextra;

import com.reagent.order.base.order.dto.OrderDetailAcceptancePicDTO;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptancePicRequestDTO;
import com.reagent.order.base.order.mapper.OrderDetailAcceptancePicMapper;
import com.reagent.order.base.order.model.OrderDetailAcceptancePicDO;
import com.reagent.order.base.order.service.OrderDetailAcceptancePicRpcService;
import com.reagent.order.base.order.translator.OrderDetailAcceptancePicTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.utils.BusinessErrUtil;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@MSharpService
public class OrderDetailAcceptancePicRpcServiceImpl implements OrderDetailAcceptancePicRpcService {

    @Resource
    private OrderDetailAcceptancePicMapper orderDetailAcceptancePicMapper;

    /**
     * 根据订单ID查询 详情关联验收图片列表
     *
     * @param orderId 订单ID
     * @return 详情关联验收图片列表
     */
    @Override
    public RemoteResponse<List<OrderDetailAcceptancePicDTO>> listByOrderId(Integer orderId) {
        BusinessErrUtil.notNull(orderId, "orderId不能为空");
        List<OrderDetailAcceptancePicDO> picDOS = orderDetailAcceptancePicMapper.selectByOrderId(orderId);
        if (CollectionUtils.isEmpty(picDOS)) {
            return RemoteResponse.success(New.emptyList());
        }
        List<OrderDetailAcceptancePicDTO> picDTOS = OrderDetailAcceptancePicTranslator.listDo2Dto(picDOS);
        return RemoteResponse.success(picDTOS);
    }

    /**
     * 根据订单ID集合批量查询 详情关联验收图片列表
     * 注意：每次最多查询200个订单
     *
     * @param orderIds 订单ID集合（最大支持200个）
     * @return 详情关联验收图片列表
     */
    @Override
    public RemoteResponse<List<OrderDetailAcceptancePicDTO>> listByOrderIds(List<Integer> orderIds) {
        Preconditions.notEmpty(orderIds, "orderIds不能为空");
        // 限制每次最多查询200个订单
        Preconditions.isTrue(CollectionUtils.size(orderIds) <= 200, "orderIds单次最多支持200个");
        List<OrderDetailAcceptancePicDO> picDOS = orderDetailAcceptancePicMapper.selectByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(picDOS)) {
            return RemoteResponse.success(New.emptyList());
        }
        List<OrderDetailAcceptancePicDTO> picDTOS = OrderDetailAcceptancePicTranslator.listDo2Dto(picDOS);
        return RemoteResponse.success(picDTOS);
    }

    /**
     * 根据订单ID删除 详情关联验收图片列表
     *
     * @param orderId 订单ID
     * @return 详情关联验收图片列表
     */
    @Override
    public RemoteResponse<Boolean> deleteByOrderId(Integer orderId) {
        BusinessErrUtil.notNull(orderId, "orderId不能为空");
        orderDetailAcceptancePicMapper.deleteByOrderId(orderId);
        return RemoteResponse.success();
    }

    /**
     * 批量保存 详情关联验收图片列表
     *
     * @param list 详情关联验收图片列表
     * @return boolean
     */
    @Override
    public RemoteResponse<Boolean> batchSave(List<OrderDetailAcceptancePicRequestDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return RemoteResponse.success();
        }
        List<OrderDetailAcceptancePicDO> picDOS = OrderDetailAcceptancePicTranslator.listReqDto2Do(list);
        orderDetailAcceptancePicMapper.batchInsert(picDOS);
        return RemoteResponse.success();
    }
}
