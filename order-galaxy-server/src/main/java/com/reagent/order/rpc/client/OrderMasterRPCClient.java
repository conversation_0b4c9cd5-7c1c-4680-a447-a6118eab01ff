package com.reagent.order.rpc.client;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;

import java.util.List;

@ServiceClient
public class OrderMasterRPCClient {

    @MSharpReference(remoteAppkey = "store-order-service")
    private OrderMasterCommonService orderMasterCommonService;

    @ServiceLog(description = "根据id更新订单", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public int updateById(UpdateOrderParamDTO request) {
        Preconditions.notNull(request.getOrderId(), "orderId must not be null");
        RemoteResponse<Integer> response = orderMasterCommonService.updateOrderById(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "根据id查询", serviceType = ServiceType.RPC_CLIENT)
    public OrderMasterDTO findById(Integer orderId) {
        Preconditions.notNull(orderId, "orderId must not be null");
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setOrderMasterId(orderId);
        RemoteResponse<OrderMasterDTO> response = orderMasterCommonService.findOrderMasterById(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "根据id查询", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderMasterDTO> findByIdList(List<Integer> orderIdList) {
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setOrderMasterIds(orderIdList);
        RemoteResponse<List<OrderMasterDTO>> response = orderMasterCommonService.findOrderListByIds(request);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());

        return response.getData();
    }

    @ServiceLog(description = "根据订单号查询", serviceType = ServiceType.RPC_CLIENT)
    public OrderMasterDTO findByOrderNo(String orderNo) {
        Preconditions.notNull(orderNo, "orderNo must not be null");
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setOrderMasterNo(orderNo);
        RemoteResponse<OrderMasterDTO> response = orderMasterCommonService.findOrderMasterByOrderNo(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }

    @ServiceLog(description = "根据订单号查询", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderMasterDTO> findByOrderNoList(List<String> orderNoList) {
        Preconditions.notEmpty(orderNoList, "orderNo must not be null");
        OrderMasterCommonReqDTO request = new OrderMasterCommonReqDTO();
        request.setOrderMasterNoList(orderNoList);
        RemoteResponse<List<OrderMasterDTO>> response = orderMasterCommonService.findOrderMasterByOrderNoList(request);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));

        return response.getData();
    }
}
