package com.reagent.order.rpc.log.service.impl;

import com.reagent.order.base.log.dto.OrderLogParamDTO;
import com.reagent.order.base.log.dto.OrderOperationLogDTO;
import com.reagent.order.base.log.mapper.OrderOperationLogMapper;
import com.reagent.order.base.log.model.OrderOperationLog;
import com.reagent.order.base.log.service.OrderOperationLogRpcService;
import com.reagent.order.base.log.translator.OrderLogTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 订单日志rpc服务 实现
 * @author: zhuk
 * @create: 2019-07-31 21:09
 **/
@MSharpService
@CatAnnotation
public class OrderOperationLogRpcServiceImpl implements OrderOperationLogRpcService {

    private static final String CAT_TYPE = "OrderOperationLogRpcServiceImpl";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OrderOperationLogMapper orderOperationLogMapper;

    @Override
    public RemoteResponse<List<OrderOperationLogDTO>> findOrderOperationLogByOrderId(OrderLogParamDTO orderLogParamDTO) {

        final String methodName = "findOrderOperationLogByOrderId";

        logger.info("进入==》{},入参{}", methodName, orderLogParamDTO.toString());
        Long orderId = orderLogParamDTO.getOrderId();
        if (orderId == null) {
            return RemoteResponse.<List<OrderOperationLogDTO>> custom().setIllegalParameter("orderId不能为空！").build();
        }
        List<OrderOperationLog> orderOperationLogList = New.emptyList();
        try {
            orderOperationLogList = orderOperationLogMapper.findByOrderId(orderId);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, methodName, "查询orderOperationLog异常", e);
            logger.error("{}查询异常", methodName, e);
            return RemoteResponse.<List<OrderOperationLogDTO>>custom().setFailure("查询失败" + e.getMessage()).build();
        }
        List<OrderOperationLogDTO> resultList = orderOperationLogList.stream().map(OrderLogTranslator::orderOperationLogToDTO).collect(Collectors.toList());
        logger.info("结束==》{},结果{}", methodName, resultList.toString());
        return RemoteResponse.<List<OrderOperationLogDTO>> custom().setSuccess().setData(resultList).build();
    }

    @Override
    public RemoteResponse createOrderOperationLog(OrderOperationLogDTO orderOperationLogDTO){

        final String methodName = "createOrderOperationLog";

        logger.info("进入==》{},入参{}", methodName, orderOperationLogDTO.toString());
        OrderOperationLog orderOperationLog = OrderLogTranslator.dtoToOrderOperationLog(orderOperationLogDTO);

        try {
            orderOperationLogMapper.insertSelective(orderOperationLog);
            return RemoteResponse.custom().setSuccess().build();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, methodName, "插入orderOperationLog异常", e);
            logger.error("{}插入orderOperationLog异常", methodName, e);
            return RemoteResponse.custom().setFailure("插入orderOperationLog异常" + e.getMessage()).build();
        }
    }
}
