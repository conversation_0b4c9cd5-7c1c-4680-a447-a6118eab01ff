package com.reagent.order.rpc.order.detail.batches.impl;

import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.reagent.order.base.order.dto.OrderDetailBatchesDTO;
import com.reagent.order.base.order.dto.OrderDetailBatchesRequestDTO;
import com.reagent.order.base.order.dto.OrderDetailExtraDTO;
import com.reagent.order.base.order.dto.request.OrderDetailExtraListRequestDTO;
import com.reagent.order.base.order.mapper.OrderDetailExtraMapper;
import com.reagent.order.base.order.model.OrderDetailExtraDO;
import com.reagent.order.base.order.model.OrderExtraDO;
import com.reagent.order.base.order.service.OrderDetailBaseService;
import com.reagent.order.base.order.service.OrderDetailBatchesRPCService;
import com.reagent.order.base.order.translator.OrderDetailExtraTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@MSharpService
@CatAnnotation
public class OrderDetailBatchesRPCServiceImpl implements OrderDetailBatchesRPCService {

    @Resource
    private OrderDetailBaseService orderDetailBaseService;

    @Resource
    private OrderDetailExtraMapper orderDetailExtraMapper;

    @Override
    @ServiceLog(description = "更新订单明细批次", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> saveOrderDetailBatches(List<OrderDetailBatchesDTO> list) {
        int affect = orderDetailBaseService.saveOrderDetailBatches(list);
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }

    @Override
    @ServiceLog(description = "查询商品订单明细批次")
    public RemoteResponse<List<OrderDetailBatchesDTO>> findByDetailIdIn(OrderDetailBatchesRequestDTO request) {
        List<OrderDetailBatchesDTO> result = orderDetailBaseService.findByDetailIdIn(request.getDetailIdList());
        return RemoteResponse.<List<OrderDetailBatchesDTO>>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<List<OrderDetailExtraDTO>> listOrderDetailExtra(OrderDetailExtraListRequestDTO req) {
        Preconditions.notNull(req, "入参不可为空");
        if(CollectionUtils.isNotEmpty(req.getOrderIdList())){
            Preconditions.isTrue(req.getOrderIdList().size() <= 200, "id集合数量限制200");
            return RemoteResponse.success(orderDetailExtraMapper.listInOrderIdAndOrderDetailId(req.getOrderIdList(), null, null).stream().map(OrderDetailExtraTranslator::toDTO).collect(Collectors.toList()));
        }else if(CollectionUtils.isNotEmpty(req.getOrderDetailIdList())){
            Preconditions.isTrue(req.getOrderDetailIdList().size() <= 200, "id集合数量限制200");
            return RemoteResponse.success(orderDetailExtraMapper.listInOrderIdAndOrderDetailId(null, req.getOrderDetailIdList(), null).stream().map(OrderDetailExtraTranslator::toDTO).collect(Collectors.toList()));
        }else {
            throw new IllegalArgumentException("订单id或订单详情id不可为空");
        }
    }

    @Override
    public RemoteResponse<Boolean> deleteOrderDetailExtra(OrderDetailExtraListRequestDTO req) {
        Preconditions.notNull(req, "入参不可为空");
        if(CollectionUtils.isNotEmpty(req.getOrderIdList())){
            Preconditions.isTrue(req.getOrderIdList().size() <= 200, "id集合数量限制200");
            orderDetailExtraMapper.deleteInOrderIdAndOrderDetailId(req.getOrderIdList(), null);
        }else if(CollectionUtils.isNotEmpty(req.getOrderDetailIdList())){
            Preconditions.isTrue(req.getOrderDetailIdList().size() <= 200, "id集合数量限制200");
            orderDetailExtraMapper.deleteInOrderIdAndOrderDetailId(null, req.getOrderDetailIdList());
        }else {
            throw new IllegalArgumentException("订单id或订单详情id不可为空");
        }
        return RemoteResponse.success();
    }

    @Override
    public RemoteResponse<Boolean> batchInsertOrderDetailExtra(List<OrderDetailExtraDTO> orderDetailExtraDTOList) {
        Preconditions.notEmpty(orderDetailExtraDTOList, "入参不可为空");
        Preconditions.isTrue(orderDetailExtraDTOList.size() <= 200, "数量不可超过200");
        orderDetailExtraMapper.batchInsert(orderDetailExtraDTOList.stream().map(OrderDetailExtraTranslator::fromDTO).collect(Collectors.toList()));
        return RemoteResponse.success();
    }

    @Override
    public RemoteResponse<Boolean> batchSaveOrderDetailExtra(List<OrderDetailExtraDTO> orderDetailExtraDTOList) {
        if(CollectionUtils.isEmpty(orderDetailExtraDTOList)){
            return RemoteResponse.success();
        }
        List<OrderDetailExtraDO> orderDetailExtraDOList = orderDetailExtraDTOList.stream().map(OrderDetailExtraTranslator::fromDTO).collect(Collectors.toList());
        Set<String> extraKeySet = orderDetailExtraDOList.stream().map(OrderDetailExtraDO::getExtraKey).filter(Objects::nonNull).collect(Collectors.toSet());
        for(String extraKey : extraKeySet){
            List<OrderDetailExtraDO> extraDOList = orderDetailExtraDOList.stream().filter(orderDetailExtraDO -> extraKey.equals(orderDetailExtraDO.getExtraKey()) && orderDetailExtraDO.getOrderDetailId() != null).collect(Collectors.toList());
            List<Integer> detailIdList = extraDOList.stream().map(OrderDetailExtraDO::getOrderDetailId).collect(Collectors.toList());
            List<OrderDetailExtraDO> existExtraList = orderDetailExtraMapper.listInOrderIdAndOrderDetailId(null, detailIdList, New.list(extraKey));

            List<Integer> existDetailIdList = existExtraList.stream().map(OrderDetailExtraDO::getOrderDetailId).collect(Collectors.toList());
            List<OrderDetailExtraDO> toInsertExtraList = extraDOList.stream().filter(detailExtraDO -> !existDetailIdList.contains(detailExtraDO.getOrderDetailId())).collect(Collectors.toList());
            List<OrderDetailExtraDO> toUpdateExtraList = extraDOList.stream().filter(detailExtraDO -> existDetailIdList.contains(detailExtraDO.getOrderDetailId())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(toInsertExtraList)){
                orderDetailExtraMapper.batchInsert(toInsertExtraList);
            }
            if(CollectionUtils.isNotEmpty(toUpdateExtraList)){
                orderDetailExtraMapper.batchUpdateExtraValue(toUpdateExtraList);
            }
        }
        return RemoteResponse.success();
    }
}
