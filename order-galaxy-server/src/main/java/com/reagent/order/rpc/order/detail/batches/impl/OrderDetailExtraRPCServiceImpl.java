package com.reagent.order.rpc.order.detail.batches.impl;

import com.reagent.order.base.order.dto.OrderDetailExtraDTO;
import com.reagent.order.base.order.dto.OrderDetailExtraInfoDTO;
import com.reagent.order.base.order.dto.request.OrderDetailExtraListRequestDTO;
import com.reagent.order.base.order.mapper.OrderDetailExtraMapper;
import com.reagent.order.base.order.model.OrderDetailExtraDO;
import com.reagent.order.base.order.service.OrderDetailExtraRPCService;
import com.reagent.order.base.order.translator.OrderDetailExtraTranslator;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 订单额外信息rpc服务
 * @date 2023/10/27 34
 */
@MSharpService
public class OrderDetailExtraRPCServiceImpl implements OrderDetailExtraRPCService {

    @Resource
    private OrderDetailExtraMapper orderDetailExtraMapper;

    @Override
    @ServiceLog(description = "查询订单详情额外信息", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<List<OrderDetailExtraInfoDTO>> listOrderDetailExtraInfo(OrderDetailExtraListRequestDTO req) {
        Preconditions.notNull(req.getOrderDetailIdList(), "订单详情id不可为空");
        List<OrderDetailExtraDO>  orderDetailExtraDOList = orderDetailExtraMapper.listInOrderIdAndOrderDetailId(null, req.getOrderDetailIdList(), null);
        Map<Integer, OrderDetailExtraInfoDTO> orderDetailExtraInfoDTOMap = New.map();
        orderDetailExtraDOList.forEach(orderDetailExtraDO -> {
            OrderDetailExtraEnum orderDetailExtraEnum = OrderDetailExtraEnum.getByType(orderDetailExtraDO.getExtraKeyType());
            if(orderDetailExtraEnum == null){
                return;
            }
            OrderDetailExtraInfoDTO orderDetailExtraInfoDTO = orderDetailExtraInfoDTOMap.computeIfAbsent(orderDetailExtraDO.getOrderDetailId(), key -> new OrderDetailExtraInfoDTO());
            orderDetailExtraInfoDTO.setId(orderDetailExtraDO.getOrderDetailId());
            switch (orderDetailExtraEnum){
                case PACKING_UNIT:
                    orderDetailExtraInfoDTO.setPackingUnit(orderDetailExtraDO.getExtraValue());
                    break;
                case PACKING_VALUE:
                    orderDetailExtraInfoDTO.setPackingValue(orderDetailExtraDO.getExtraValue());
                    break;
                case MIN_PACKING_UNIT:
                    orderDetailExtraInfoDTO.setMinPackingUnit(orderDetailExtraDO.getExtraValue());
                    break;
                case MIN_PACKING_VALUE:
                    orderDetailExtraInfoDTO.setMinPackingValue(orderDetailExtraDO.getExtraValue());
                    break;
                default:
                    break;
            }
        });
        return RemoteResponse.success(New.list(orderDetailExtraInfoDTOMap.values()));
    }

    @Override
    @ServiceLog(description = "查询订单详情额外信息", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<List<OrderDetailExtraDTO>> listOrderDetailExtra(OrderDetailExtraListRequestDTO req) {
        Preconditions.notNull(req, "入参不可为空");
        if(CollectionUtils.isNotEmpty(req.getOrderIdList())){
            Preconditions.isTrue(req.getOrderIdList().size() <= 500, "id集合数量限制500");
            return RemoteResponse.success(orderDetailExtraMapper.listInOrderIdAndOrderDetailId(req.getOrderIdList(), null, req.getExtraKeyList()).stream().map(OrderDetailExtraTranslator::toDTO).collect(Collectors.toList()));
        }else if(CollectionUtils.isNotEmpty(req.getOrderDetailIdList())){
            Preconditions.isTrue(req.getOrderDetailIdList().size() <= 500, "id集合数量限制500");
            return RemoteResponse.success(orderDetailExtraMapper.listInOrderIdAndOrderDetailId(null, req.getOrderDetailIdList(), req.getExtraKeyList()).stream().map(OrderDetailExtraTranslator::toDTO).collect(Collectors.toList()));
        }else {
            throw new IllegalArgumentException("订单id或订单详情id不可为空");
        }
    }

    @Override
    @ServiceLog(description = "删除订单详情额外信息", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> deleteOrderDetailExtra(OrderDetailExtraListRequestDTO req) {
        Preconditions.notNull(req, "入参不可为空");
        if(CollectionUtils.isNotEmpty(req.getOrderIdList())){
            Preconditions.isTrue(req.getOrderIdList().size() <= 500, "id集合数量限制500");
            orderDetailExtraMapper.deleteInOrderIdAndOrderDetailId(req.getOrderIdList(), null);
        }else if(CollectionUtils.isNotEmpty(req.getOrderDetailIdList())){
            Preconditions.isTrue(req.getOrderDetailIdList().size() <= 500, "id集合数量限制500");
            orderDetailExtraMapper.deleteInOrderIdAndOrderDetailId(null, req.getOrderDetailIdList());
        }else {
            throw new IllegalArgumentException("订单id或订单详情id不可为空");
        }
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "插入订单详情额外信息", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> batchInsertOrderDetailExtra(List<OrderDetailExtraDTO> orderDetailExtraDTOList) {
        Preconditions.notEmpty(orderDetailExtraDTOList, "入参不可为空");
        Preconditions.isTrue(orderDetailExtraDTOList.size() <= 500, "数量不可超过500");
        orderDetailExtraMapper.batchInsert(orderDetailExtraDTOList.stream().map(OrderDetailExtraTranslator::fromDTO).collect(Collectors.toList()));
        return RemoteResponse.success();
    }
}
