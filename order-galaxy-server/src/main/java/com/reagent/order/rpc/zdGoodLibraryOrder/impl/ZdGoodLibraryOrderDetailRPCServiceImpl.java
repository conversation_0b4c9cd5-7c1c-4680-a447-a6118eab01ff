package com.reagent.order.rpc.zdGoodLibraryOrder.impl;

import com.reagent.order.base.order.dto.ZdGoodLibraryOrderDetailDTO;
import com.reagent.order.base.order.mapper.ZdGoodLibraryOrderDetailMapper;
import com.reagent.order.base.order.model.ZdGoodLibraryOrderDetail;
import com.reagent.order.base.order.service.ZdGoodLibraryOrderDetailRPCService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 中大商品库订单接口
 * @date 2023/11/10 28
 */
@MSharpService
public class ZdGoodLibraryOrderDetailRPCServiceImpl implements ZdGoodLibraryOrderDetailRPCService {

    @Resource
    private ZdGoodLibraryOrderDetailMapper zdGoodLibraryOrderDetailMapper;

    @Override
    public RemoteResponse<Boolean> batchInsertSelective(List<ZdGoodLibraryOrderDetailDTO> dtoList) {
        Preconditions.notEmpty(dtoList, "入参不可为空");
        Preconditions.isTrue(dtoList.size() <= 400);
        zdGoodLibraryOrderDetailMapper.batchInsertSelective(dtoList.stream().map(dto -> {
            ZdGoodLibraryOrderDetail entity = new ZdGoodLibraryOrderDetail();
            entity.setId(dto.getId());
            entity.setGoodName(dto.getGoodName());
            entity.setOrderNo(dto.getOrderNo());
            entity.setMatchDegree(dto.getMatchDegree());
            entity.setOrgId(dto.getOrgId());
            return entity;
        }).collect(Collectors.toList()));
        return RemoteResponse.success();
    }
}
