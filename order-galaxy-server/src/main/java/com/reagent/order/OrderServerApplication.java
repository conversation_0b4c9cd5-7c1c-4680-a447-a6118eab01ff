package com.reagent.order;


import com.msharp.sharding.jdbc.springboot.autoconfigure.annotation.EnableMSharpDataSource;
import com.ruijing.cat.springboot.autoconfigure.annotation.EnableCat;
import com.ruijing.fundamental.springboot.starter.ServiceBootApplication;
import com.ruijing.pearl.annotation.EnablePearl;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * 服务启动类
 *
 * <AUTHOR>
 * @date 2019-07-26
 */
@MapperScan(basePackages = "com.reagent.order.base.*.mapper")
@SpringBootApplication
@EnablePearl
@EnableCat
@EnableMSharpDataSource
public class OrderServerApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        ServiceBootApplication.main(OrderServerApplication.class, args);
    }

}

