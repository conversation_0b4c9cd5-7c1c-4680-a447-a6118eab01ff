<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ruijing.order</groupId>
        <artifactId>order-base-dependency</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>com.reagent.order</groupId>
    <artifactId>order-galaxy-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>

    <modules>
        <module>order-galaxy-api</module>
        <module>order-galaxy-server</module>
    </modules>

    <properties>
        <testable.version>0.6.6</testable.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ruijing.fundamental</groupId>
                <artifactId>msharp-api-info-bom</artifactId>
                <version>${msharp.version}</version>
                <type>pom</type>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!--单测覆盖 maven插件-->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <configuration>
                    <excludes>
                        <exclude>com/reagenet/order/**/*DTO.class</exclude>
                        <exclude>com/reagenet/order/**/*DO.class</exclude>
                        <exclude>com/reagenet/order/**/*BO.class</exclude>
                        <exclude>com/reagenet/order/**/*VO.class</exclude>
                        <exclude>com/reagenet/order/**/*Test.class</exclude>
                        <exclude>com/reagenet/order/**/*Request.class</exclude>
                        <exclude>com/reagenet/order/**/*Enum.class</exclude>
                        <exclude>com/reagenet/order/**/*Controller.class</exclude>
                        <exclude>com/reagenet/order/**/*Mapper.class</exclude>
                        <exclude>com/reagenet/order/**/*Client.class</exclude>
                        <exclude>com/reagenet/order/**/*Translator.class</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <argLine>@{argLine} -javaagent:${settings.localRepository}/com/alibaba/testable/testable-agent/${testable.version}/testable-agent-${testable.version}.jar</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-snapshots/</url>
        </snapshotRepository>

        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://nexus.rj-info.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>maven-public</id>
            <name>maven-public</name>
            <url>http://nexus.rj-info.com/repository/maven-public/</url>
        </repository>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://nexus.rj-info.com/repository/maven-releases/</url>
        </repository>
    </repositories>
</project>